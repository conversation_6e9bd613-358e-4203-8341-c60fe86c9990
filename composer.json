{"name": "monsefeledrisse/filament-solar-icons", "description": "Solar Icon Set for Filament Admin Panel - Professional icon package with 7,000+ Solar icons across 6 styles", "version": "1.0.2", "type": "library", "keywords": ["filament", "icons", "solar", "laravel", "blade-icons", "ui", "admin-panel"], "homepage": "https://github.com/monsefeledrisse/filament-solar-icons", "license": "MIT", "autoload": {"psr-4": {"Monsefeledrisse\\FilamentSolarIcons\\": "src/"}}, "autoload-dev": {"psr-4": {"Monsefeledrisse\\FilamentSolarIcons\\Tests\\": "tests/"}}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"issues": "https://github.com/monsefeledrisse/filament-solar-icons/issues", "source": "https://github.com/monsefeledrisse/filament-solar-icons"}, "require": {"blade-ui-kit/blade-icons": "^1.8", "php": "^8.1"}, "suggest": {"filament/filament": "^3.0|^4.0 - For Filament Admin Panel integration"}, "require-dev": {"pestphp/pest": "^2.0", "orchestra/testbench": "^8.0|^9.0", "phpunit/phpunit": "^10.0"}, "scripts": {"test": "pest", "version": "php bin/version", "version:current": "php bin/version current", "version:patch": "php bin/version bump patch", "version:minor": "php bin/version bump minor", "version:major": "php bin/version bump major"}, "extra": {"laravel": {"providers": ["Monsefeledrisse\\FilamentSolarIcons\\SolarIconSetServiceProvider"]}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"allow-plugins": {"pestphp/pest-plugin": true}}}