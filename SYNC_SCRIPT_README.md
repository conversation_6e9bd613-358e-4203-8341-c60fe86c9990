# Solar Icons Enum Synchronization Script

This document describes the automatic synchronization script for keeping the `SolarIcon` enum in sync with the actual icon files in the project.

## Overview

The `bin/sync-solar-icons.php` script automatically synchronizes the `SolarIcon` enum with the actual icon files present in the `resources/icons/solar` directory. This ensures that the enum always reflects the available icons and prevents mismatches between code references and available assets.

## Features

- **Automatic Discovery**: Scans all icon files in the project directory structure
- **Smart Comparison**: Compares existing enum cases with actual icon files
- **Intelligent Updates**: Adds missing icons and removes orphaned enum cases
- **Consistent Naming**: Maintains consistent naming conventions between file names and enum values
- **Structure Preservation**: Preserves existing code structure, formatting, and methods
- **Safety First**: Creates automatic backups before making changes
- **Organized Output**: Groups icons by style for better organization
- **Detailed Reporting**: Provides comprehensive output showing what changes were made

## Usage

### Basic Usage

```bash
# Preview changes without modifying files (recommended first step)
php bin/sync-solar-icons.php --dry-run --verbose

# Apply changes to the enum file
php bin/sync-solar-icons.php
```

### Command Line Options

- `--dry-run`: Show what would be changed without modifying files
- `--verbose`: Show detailed output including sample changes
- `--help`: Show help message with full documentation

### Examples

```bash
# Safe preview with detailed output
php bin/sync-solar-icons.php --dry-run --verbose

# Quick preview
php bin/sync-solar-icons.php --dry-run

# Apply changes (creates backup automatically)
php bin/sync-solar-icons.php

# Show help
php bin/sync-solar-icons.php --help
```

## How It Works

1. **File Scanning**: Uses the existing `SolarIconHelper::getAllIconFiles()` method to discover all available icon files
2. **Enum Parsing**: Parses the current `src/SolarIcon.php` file to extract existing enum cases
3. **Comparison**: Identifies differences between available files and enum cases
4. **Generation**: Generates new enum cases using consistent naming conventions
5. **Organization**: Groups icons by style (Bold, Outline, Linear, etc.) for better readability
6. **Backup**: Creates a timestamped backup of the original enum file
7. **Update**: Writes the updated enum content while preserving existing structure

## Generated Enum Structure

The script organizes the generated enum cases by style:

```php
enum SolarIcon: string
{
    // ========================================
    // AUTO-GENERATED ENUM CASES
    // Generated by: bin/sync-solar-icons.php
    // Total icons: 7330
    // Generated on: 2024-08-03 15:30:45
    // ========================================

    // Bold Style (1500 icons)
    case Home = 'solar-bold-Home';
    case User = 'solar-bold-User';
    // ... more bold icons

    // Outline Style (1200 icons)
    case OutlineHome = 'solar-outline-Home';
    case OutlineUser = 'solar-outline-User';
    // ... more outline icons

    // Linear Style (1100 icons)
    case LinearHome = 'solar-linear-Home';
    case LinearUser = 'solar-linear-User';
    // ... more linear icons

    // ... other styles

    /**
     * Get the icon for a specific size.
     * (existing methods preserved)
     */
    public function getIconForSize(string $size): string
    {
        return $this->value;
    }
    
    // ... other existing methods preserved
}
```

## Safety Features

### Automatic Backups
Before making any changes, the script creates a timestamped backup:
```
src/SolarIcon.php.backup.2024-08-03_15-30-45
```

### Dry Run Mode
Always test changes first:
```bash
php bin/sync-solar-icons.php --dry-run --verbose
```

### Structure Preservation
- Preserves existing enum methods
- Maintains code formatting
- Keeps existing comments and documentation
- Only updates the enum cases section

## Naming Conventions

The script uses the existing `SolarIconHelper::normalizeIconName()` method to ensure consistent naming:

- File: `home_smile.svg` → Enum: `HomeSmile`
- File: `user-block.svg` → Enum: `UserBlock`
- File: `4_k.svg` → Enum: `Icon4K` (handles numeric prefixes)

For duplicate names across styles, it adds style prefixes:
- `Home` (first occurrence)
- `OutlineHome` (outline style)
- `LinearHome` (linear style)

## Integration with Existing Code

The script integrates seamlessly with the existing codebase:

- Uses existing `SolarIconHelper` methods
- Respects the current project structure
- Maintains compatibility with Filament integration
- Preserves all existing enum methods and functionality

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure the script has write permissions to `src/SolarIcon.php`
2. **Memory Issues**: For very large icon sets, increase PHP memory limit if needed
3. **File Not Found**: Ensure you're running from the project root directory

### Recovery

If something goes wrong:
1. Restore from the automatic backup: `cp src/SolarIcon.php.backup.* src/SolarIcon.php`
2. Check the backup files in the `src/` directory
3. Re-run with `--dry-run` to verify before applying changes

## Maintenance

### When to Run

- After adding new icon files to the project
- After removing icon files
- After updating the Solar icon set
- As part of the build process (optional)
- Before major releases to ensure consistency

### Automation

The script can be integrated into your development workflow:

```bash
# Add to composer.json scripts
"scripts": {
    "sync-icons": "php bin/sync-solar-icons.php",
    "sync-icons-preview": "php bin/sync-solar-icons.php --dry-run --verbose"
}
```

Then run with:
```bash
composer sync-icons-preview
composer sync-icons
```

## Technical Details

- **Language**: PHP 8.0+
- **Dependencies**: Uses existing project dependencies
- **Performance**: Handles thousands of icons efficiently
- **Memory**: Optimized for large icon sets
- **Error Handling**: Comprehensive error reporting and recovery

## Contributing

When modifying the script:
1. Test with `--dry-run` first
2. Verify backup creation works
3. Test with different icon set sizes
4. Ensure existing enum methods are preserved
5. Update this documentation if needed
