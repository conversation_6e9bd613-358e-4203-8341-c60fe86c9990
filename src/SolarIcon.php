<?php

declare(strict_types=1);

namespace Monsefeledrisse\FilamentSolarIcons;

/**
 * Solar Icon Set for Filament v4
 *
 * This enum provides type-safe access to Solar icons in Filament v4,
 * similar to how Heroicon works in the core Filament package.
 *
 * Each enum case represents a specific Solar icon with its full identifier.
 * The enum implements ScalableIcon when Filament is available.
 *
 * @example
 * ```php
 * // In Filament components
 * Action::make('star')->icon(SolarIcon::Star)
 * TextInput::make('name')->prefixIcon(SolarIcon::OutlineUser)
 * NavigationItem::make('Dashboard')->icon(SolarIcon::LinearHome)
 * ```
 *
 * @package Monsefeledrisse\FilamentSolarIcons
 */
enum SolarIcon: string
{
    // ========================================
    // AUTO-GENERATED ENUM CASES
    // Generated by: bin/sync-solar-icons.php
    // Total icons: 7330
    // Generated on: 2025-08-03 12:28:35
    // ========================================

    // Bold Style (1235 icons)
    case FacemaskCircle = 'solar-bold-FacemaskCircle';
    case ConfoundedCircle = 'solar-bold-ConfoundedCircle';
    case SadSquare = 'solar-bold-SadSquare';
    case SleepingCircle = 'solar-bold-SleepingCircle';
    case FaceScanCircle = 'solar-bold-FaceScanCircle';
    case SmileCircle = 'solar-bold-SmileCircle';
    case StickerSmileCircle = 'solar-bold-StickerSmileCircle';
    case StickerSquare = 'solar-bold-StickerSquare';
    case EmojiFunnyCircle = 'solar-bold-EmojiFunnyCircle';
    case ExpressionlessSquare = 'solar-bold-ExpressionlessSquare';
    case SleepingSquare = 'solar-bold-SleepingSquare';
    case SadCircle = 'solar-bold-SadCircle';
    case FacemaskSquare = 'solar-bold-FacemaskSquare';
    case ConfoundedSquare = 'solar-bold-ConfoundedSquare';
    case FaceScanSquare = 'solar-bold-FaceScanSquare';
    case SmileSquare = 'solar-bold-SmileSquare';
    case StickerSmileCircle2 = 'solar-bold-StickerSmileCircle2';
    case StickerSmileSquare = 'solar-bold-StickerSmileSquare';
    case EmojiFunnySquare = 'solar-bold-EmojiFunnySquare';
    case StickerCircle = 'solar-bold-StickerCircle';
    case ExpressionlessCircle = 'solar-bold-ExpressionlessCircle';
    case Like = 'solar-bold-Like';
    case MedalStarSquare = 'solar-bold-MedalStarSquare';
    case Dislike = 'solar-bold-Dislike';
    case StarShine = 'solar-bold-StarShine';
    case HeartAngle = 'solar-bold-HeartAngle';
    case MedalRibbon = 'solar-bold-MedalRibbon';
    case HeartShine = 'solar-bold-HeartShine';
    case MedalStarCircle = 'solar-bold-MedalStarCircle';
    case MedalRibbonsStar = 'solar-bold-MedalRibbonsStar';
    case Star = 'solar-bold-Star';
    case HeartUnlock = 'solar-bold-HeartUnlock';
    case MedalRibbonStar = 'solar-bold-MedalRibbonStar';
    case HeartLock = 'solar-bold-HeartLock';
    case HeartBroken = 'solar-bold-HeartBroken';
    case Hearts = 'solar-bold-Hearts';
    case MedalStar = 'solar-bold-MedalStar';
    case Heart = 'solar-bold-Heart';
    case Closet = 'solar-bold-Closet';
    case Bed = 'solar-bold-Bed';
    case WashingMachine = 'solar-bold-WashingMachine';
    case BedsideTable = 'solar-bold-BedsideTable';
    case Sofa3 = 'solar-bold-Sofa3';
    case Sofa2 = 'solar-bold-Sofa2';
    case Chair2 = 'solar-bold-Chair2';
    case Bath = 'solar-bold-Bath';
    case SmartVacuumCleaner2 = 'solar-bold-SmartVacuumCleaner2';
    case Condicioner = 'solar-bold-Condicioner';
    case SmartVacuumCleaner = 'solar-bold-SmartVacuumCleaner';
    case RemoteController2 = 'solar-bold-RemoteController2';
    case FloorLampMinimalistic = 'solar-bold-FloorLampMinimalistic';
    case Lamp = 'solar-bold-Lamp';
    case BarChair = 'solar-bold-BarChair';
    case BedsideTable2 = 'solar-bold-BedsideTable2';
    case Closet2 = 'solar-bold-Closet2';
    case BedsideTable3 = 'solar-bold-BedsideTable3';
    case Speaker = 'solar-bold-Speaker';
    case VolumeKnob = 'solar-bold-VolumeKnob';
    case Armchair = 'solar-bold-Armchair';
    case SpeakerMinimalistic = 'solar-bold-SpeakerMinimalistic';
    case RemoteController = 'solar-bold-RemoteController';
    case Trellis = 'solar-bold-Trellis';
    case FloorLamp = 'solar-bold-FloorLamp';
    case Condicioner2 = 'solar-bold-Condicioner2';
    case BedsideTable4 = 'solar-bold-BedsideTable4';
    case Armchair2 = 'solar-bold-Armchair2';
    case WashingMachineMinimalistic = 'solar-bold-WashingMachineMinimalistic';
    case Chair = 'solar-bold-Chair';
    case RemoteControllerMinimalistic = 'solar-bold-RemoteControllerMinimalistic';
    case Chandelier = 'solar-bold-Chandelier';
    case Fridge = 'solar-bold-Fridge';
    case Mirror = 'solar-bold-Mirror';
    case Sofa = 'solar-bold-Sofa';
    case Earth = 'solar-bold-Earth';
    case StarsLine = 'solar-bold-StarsLine';
    case StarFall2 = 'solar-bold-StarFall2';
    case StarFall = 'solar-bold-StarFall';
    case BlackHole3 = 'solar-bold-BlackHole3';
    case Women = 'solar-bold-Women';
    case BlackHole = 'solar-bold-BlackHole';
    case StarRings = 'solar-bold-StarRings';
    case BlackHole2 = 'solar-bold-BlackHole2';
    case StarFallMinimalistic2 = 'solar-bold-StarFallMinimalistic2';
    case Planet = 'solar-bold-Planet';
    case Satellite = 'solar-bold-Satellite';
    case Men = 'solar-bold-Men';
    case Rocket2 = 'solar-bold-Rocket2';
    case Stars = 'solar-bold-Stars';
    case StarAngle = 'solar-bold-StarAngle';
    case Infinity = 'solar-bold-Infinity';
    case Ufo2 = 'solar-bold-Ufo2';
    case Ufo3 = 'solar-bold-Ufo3';
    case StarRing = 'solar-bold-StarRing';
    case Planet2 = 'solar-bold-Planet2';
    case Planet3 = 'solar-bold-Planet3';
    case Asteroid = 'solar-bold-Asteroid';
    case StarsMinimalistic = 'solar-bold-StarsMinimalistic';
    case UFO = 'solar-bold-UFO';
    case Planet4 = 'solar-bold-Planet4';
    case Rocket = 'solar-bold-Rocket';
    case StarFallMinimalistic = 'solar-bold-StarFallMinimalistic';
    case StarRainbow = 'solar-bold-StarRainbow';
    case Atom = 'solar-bold-Atom';
    case StarCircle = 'solar-bold-StarCircle';
    case CompassBig = 'solar-bold-CompassBig';
    case MapPointSchool = 'solar-bold-MapPointSchool';
    case Signpost = 'solar-bold-Signpost';
    case MapArrowDown = 'solar-bold-MapArrowDown';
    case Map = 'solar-bold-Map';
    case MapArrowUp = 'solar-bold-MapArrowUp';
    case PointOnMapPerspective = 'solar-bold-PointOnMapPerspective';
    case Radar = 'solar-bold-Radar';
    case Streets = 'solar-bold-Streets';
    case MapPointWave = 'solar-bold-MapPointWave';
    case PeopleNearby = 'solar-bold-PeopleNearby';
    case StreetsMapPoint = 'solar-bold-StreetsMapPoint';
    case MapPointSearch = 'solar-bold-MapPointSearch';
    case GPS = 'solar-bold-GPS';
    case MapArrowSquare = 'solar-bold-MapArrowSquare';
    case BranchingPathsDown = 'solar-bold-BranchingPathsDown';
    case MapPointRotate = 'solar-bold-MapPointRotate';
    case Global = 'solar-bold-Global';
    case CompassSquare = 'solar-bold-CompassSquare';
    case Routing3 = 'solar-bold-Routing3';
    case Routing2 = 'solar-bold-Routing2';
    case MapPointRemove = 'solar-bold-MapPointRemove';
    case Globus = 'solar-bold-Globus';
    case Signpost2 = 'solar-bold-Signpost2';
    case Radar2 = 'solar-bold-Radar2';
    case StreetsNavigation = 'solar-bold-StreetsNavigation';
    case MapPoint = 'solar-bold-MapPoint';
    case MapPointHospital = 'solar-bold-MapPointHospital';
    case Compass = 'solar-bold-Compass';
    case MapPointAdd = 'solar-bold-MapPointAdd';
    case BranchingPathsUp = 'solar-bold-BranchingPathsUp';
    case MapPointFavourite = 'solar-bold-MapPointFavourite';
    case Route = 'solar-bold-Route';
    case PointOnMap = 'solar-bold-PointOnMap';
    case MapArrowRight = 'solar-bold-MapArrowRight';
    case Routing = 'solar-bold-Routing';
    case MapArrowLeft = 'solar-bold-MapArrowLeft';
    case Incognito = 'solar-bold-Incognito';
    case LockPassword = 'solar-bold-LockPassword';
    case ShieldNetwork = 'solar-bold-ShieldNetwork';
    case KeyMinimalisticSquare = 'solar-bold-KeyMinimalisticSquare';
    case LockKeyholeUnlocked = 'solar-bold-LockKeyholeUnlocked';
    case Lock = 'solar-bold-Lock';
    case ShieldKeyhole = 'solar-bold-ShieldKeyhole';
    case EyeClosed = 'solar-bold-EyeClosed';
    case Key = 'solar-bold-Key';
    case ShieldMinus = 'solar-bold-ShieldMinus';
    case Shield = 'solar-bold-Shield';
    case LockUnlocked = 'solar-bold-LockUnlocked';
    case BombMinimalistic = 'solar-bold-BombMinimalistic';
    case ShieldStar = 'solar-bold-ShieldStar';
    case Bomb = 'solar-bold-Bomb';
    case KeySquare = 'solar-bold-KeySquare';
    case LockKeyholeMinimalisticUnlocked = 'solar-bold-LockKeyholeMinimalisticUnlocked';
    case ShieldCross = 'solar-bold-ShieldCross';
    case ObjectScan = 'solar-bold-ObjectScan';
    case PasswordMinimalisticInput = 'solar-bold-PasswordMinimalisticInput';
    case LockPasswordUnlocked = 'solar-bold-LockPasswordUnlocked';
    case Siren = 'solar-bold-Siren';
    case ShieldMinimalistic = 'solar-bold-ShieldMinimalistic';
    case EyeScan = 'solar-bold-EyeScan';
    case KeyMinimalisticSquare2 = 'solar-bold-KeyMinimalisticSquare2';
    case Scanner2 = 'solar-bold-Scanner2';
    case KeyMinimalisticSquare3 = 'solar-bold-KeyMinimalisticSquare3';
    case KeyMinimalistic2 = 'solar-bold-KeyMinimalistic2';
    case CodeScan = 'solar-bold-CodeScan';
    case ShieldPlus = 'solar-bold-ShieldPlus';
    case PasswordMinimalistic = 'solar-bold-PasswordMinimalistic';
    case Eye = 'solar-bold-Eye';
    case QrCode = 'solar-bold-QrCode';
    case ShieldCheck = 'solar-bold-ShieldCheck';
    case KeyMinimalistic = 'solar-bold-KeyMinimalistic';
    case LockKeyhole = 'solar-bold-LockKeyhole';
    case ShieldUser = 'solar-bold-ShieldUser';
    case KeySquare2 = 'solar-bold-KeySquare2';
    case BombEmoji = 'solar-bold-BombEmoji';
    case Scanner = 'solar-bold-Scanner';
    case ShieldUp = 'solar-bold-ShieldUp';
    case SirenRounded = 'solar-bold-SirenRounded';
    case LockKeyholeMinimalistic = 'solar-bold-LockKeyholeMinimalistic';
    case Password = 'solar-bold-Password';
    case ShieldKeyholeMinimalistic = 'solar-bold-ShieldKeyholeMinimalistic';
    case ShieldWarning = 'solar-bold-ShieldWarning';
    case Pallete2 = 'solar-bold-Pallete2';
    case AlignVerticalSpacing = 'solar-bold-AlignVerticalSpacing';
    case AlignVerticalCenter = 'solar-bold-AlignVerticalCenter';
    case CropMinimalistic = 'solar-bold-CropMinimalistic';
    case MirrorRight = 'solar-bold-MirrorRight';
    case AlignBottom = 'solar-bold-AlignBottom';
    case RadialBlur = 'solar-bold-RadialBlur';
    case Crop = 'solar-bold-Crop';
    case AlignHorizontaSpacing = 'solar-bold-AlignHorizontaSpacing';
    case RulerPen = 'solar-bold-RulerPen';
    case ThreeSquares = 'solar-bold-ThreeSquares';
    case PaintRoller = 'solar-bold-PaintRoller';
    case Layers = 'solar-bold-Layers';
    case Filters = 'solar-bold-Filters';
    case RulerCrossPen = 'solar-bold-RulerCrossPen';
    case FlipHorizontal = 'solar-bold-FlipHorizontal';
    case AlignLeft = 'solar-bold-AlignLeft';
    case Ruler = 'solar-bold-Ruler';
    case Palette = 'solar-bold-Palette';
    case AlignTop = 'solar-bold-AlignTop';
    case AlignHorizontalCenter = 'solar-bold-AlignHorizontalCenter';
    case AlignRight = 'solar-bold-AlignRight';
    case RulerAngular = 'solar-bold-RulerAngular';
    case Pipette = 'solar-bold-Pipette';
    case FlipVertical = 'solar-bold-FlipVertical';
    case MirrorLeft = 'solar-bold-MirrorLeft';
    case LayersMinimalistic = 'solar-bold-LayersMinimalistic';
    case ColourTuneing = 'solar-bold-ColourTuneing';
    case PaletteRound = 'solar-bold-PaletteRound';
    case Eraser = 'solar-bold-Eraser';
    case TextItalicCircle = 'solar-bold-TextItalicCircle';
    case LinkRound = 'solar-bold-LinkRound';
    case TextItalic = 'solar-bold-TextItalic';
    case LinkBrokenMinimalistic = 'solar-bold-LinkBrokenMinimalistic';
    case TextUnderlineCross = 'solar-bold-TextUnderlineCross';
    case Link = 'solar-bold-Link';
    case EraserCircle = 'solar-bold-EraserCircle';
    case LinkCircle = 'solar-bold-LinkCircle';
    case TextBoldCircle = 'solar-bold-TextBoldCircle';
    case TextField = 'solar-bold-TextField';
    case TextSquare = 'solar-bold-TextSquare';
    case TextSquare2 = 'solar-bold-TextSquare2';
    case LinkRoundAngle = 'solar-bold-LinkRoundAngle';
    case TextUnderlineCircle = 'solar-bold-TextUnderlineCircle';
    case TextCrossCircle = 'solar-bold-TextCrossCircle';
    case TextItalicSquare = 'solar-bold-TextItalicSquare';
    case ParagraphSpacing = 'solar-bold-ParagraphSpacing';
    case Text = 'solar-bold-Text';
    case LinkBroken = 'solar-bold-LinkBroken';
    case TextCross = 'solar-bold-TextCross';
    case TextUnderline = 'solar-bold-TextUnderline';
    case LinkMinimalistic = 'solar-bold-LinkMinimalistic';
    case LinkMinimalistic2 = 'solar-bold-LinkMinimalistic2';
    case TextBold = 'solar-bold-TextBold';
    case TextSelection = 'solar-bold-TextSelection';
    case TextFieldFocus = 'solar-bold-TextFieldFocus';
    case TextBoldSquare = 'solar-bold-TextBoldSquare';
    case EraserSquare = 'solar-bold-EraserSquare';
    case LinkSquare = 'solar-bold-LinkSquare';
    case TextCircle = 'solar-bold-TextCircle';
    case Backspace = 'solar-bold-Backspace';
    case TextCrossSquare = 'solar-bold-TextCrossSquare';
    case InboxUnread = 'solar-bold-InboxUnread';
    case ChatUnread = 'solar-bold-ChatUnread';
    case ChatRound = 'solar-bold-ChatRound';
    case Unread = 'solar-bold-Unread';
    case Mailbox = 'solar-bold-Mailbox';
    case Letter = 'solar-bold-Letter';
    case PenNewRound = 'solar-bold-PenNewRound';
    case MultipleForwardRight = 'solar-bold-MultipleForwardRight';
    case MultipleForwardLeft = 'solar-bold-MultipleForwardLeft';
    case InboxArchive = 'solar-bold-InboxArchive';
    case Inbox = 'solar-bold-Inbox';
    case Pen2 = 'solar-bold-Pen2';
    case PenNewSquare = 'solar-bold-PenNewSquare';
    case Pen = 'solar-bold-Pen';
    case ChatDots = 'solar-bold-ChatDots';
    case ChatSquareCall = 'solar-bold-ChatSquareCall';
    case SquareShareLine = 'solar-bold-SquareShareLine';
    case ChatRoundCheck = 'solar-bold-ChatRoundCheck';
    case InboxOut = 'solar-bold-InboxOut';
    case Plain3 = 'solar-bold-Plain3';
    case ChatRoundDots = 'solar-bold-ChatRoundDots';
    case ChatRoundLike = 'solar-bold-ChatRoundLike';
    case Plain2 = 'solar-bold-Plain2';
    case ChatRoundUnread = 'solar-bold-ChatRoundUnread';
    case ChatSquareLike = 'solar-bold-ChatSquareLike';
    case Paperclip = 'solar-bold-Paperclip';
    case ChatSquareCheck = 'solar-bold-ChatSquareCheck';
    case ChatSquare = 'solar-bold-ChatSquare';
    case LetterOpened = 'solar-bold-LetterOpened';
    case SquareForward = 'solar-bold-SquareForward';
    case LetterUnread = 'solar-bold-LetterUnread';
    case PaperclipRounded2 = 'solar-bold-PaperclipRounded2';
    case ChatRoundCall = 'solar-bold-ChatRoundCall';
    case InboxLine = 'solar-bold-InboxLine';
    case ChatRoundVideo = 'solar-bold-ChatRoundVideo';
    case ChatRoundMoney = 'solar-bold-ChatRoundMoney';
    case InboxIn = 'solar-bold-InboxIn';
    case CheckRead = 'solar-bold-CheckRead';
    case ChatRoundLine = 'solar-bold-ChatRoundLine';
    case Forward = 'solar-bold-Forward';
    case Paperclip2 = 'solar-bold-Paperclip2';
    case Dialog2 = 'solar-bold-Dialog2';
    case Dialog = 'solar-bold-Dialog';
    case PaperclipRounded = 'solar-bold-PaperclipRounded';
    case Plain = 'solar-bold-Plain';
    case ChatSquareArrow = 'solar-bold-ChatSquareArrow';
    case ChatSquareCode = 'solar-bold-ChatSquareCode';
    case ChatLine = 'solar-bold-ChatLine';
    case Tennis = 'solar-bold-Tennis';
    case BicyclingRound = 'solar-bold-BicyclingRound';
    case Balls = 'solar-bold-Balls';
    case MeditationRound = 'solar-bold-MeditationRound';
    case StretchingRound = 'solar-bold-StretchingRound';
    case Dumbbells2 = 'solar-bold-Dumbbells2';
    case Meditation = 'solar-bold-Meditation';
    case Running2 = 'solar-bold-Running2';
    case Rugby = 'solar-bold-Rugby';
    case BodyShapeMinimalistic = 'solar-bold-BodyShapeMinimalistic';
    case Stretching = 'solar-bold-Stretching';
    case Bowling = 'solar-bold-Bowling';
    case Ranking = 'solar-bold-Ranking';
    case TreadmillRound = 'solar-bold-TreadmillRound';
    case Volleyball = 'solar-bold-Volleyball';
    case DumbbellLargeMinimalistic = 'solar-bold-DumbbellLargeMinimalistic';
    case RunningRound = 'solar-bold-RunningRound';
    case Hiking = 'solar-bold-Hiking';
    case HikingMinimalistic = 'solar-bold-HikingMinimalistic';
    case WaterSun = 'solar-bold-WaterSun';
    case Golf = 'solar-bold-Golf';
    case Skateboarding = 'solar-bold-Skateboarding';
    case Dumbbells = 'solar-bold-Dumbbells';
    case WalkingRound = 'solar-bold-WalkingRound';
    case Running = 'solar-bold-Running';
    case Treadmill = 'solar-bold-Treadmill';
    case Skateboard = 'solar-bold-Skateboard';
    case DumbbellSmall = 'solar-bold-DumbbellSmall';
    case Basketball = 'solar-bold-Basketball';
    case Football = 'solar-bold-Football';
    case Dumbbell = 'solar-bold-Dumbbell';
    case BodyShape = 'solar-bold-BodyShape';
    case Water = 'solar-bold-Water';
    case SkateboardingRound = 'solar-bold-SkateboardingRound';
    case HikingRound = 'solar-bold-HikingRound';
    case Volleyball2 = 'solar-bold-Volleyball2';
    case Tennis2 = 'solar-bold-Tennis2';
    case Swimming = 'solar-bold-Swimming';
    case Bicycling = 'solar-bold-Bicycling';
    case Walking = 'solar-bold-Walking';
    case DumbbellLarge = 'solar-bold-DumbbellLarge';
    case CalendarMark = 'solar-bold-CalendarMark';
    case History2 = 'solar-bold-History2';
    case WatchSquareMinimalisticCharge = 'solar-bold-WatchSquareMinimalisticCharge';
    case History3 = 'solar-bold-History3';
    case Hourglass = 'solar-bold-Hourglass';
    case CalendarSearch = 'solar-bold-CalendarSearch';
    case StopwatchPlay = 'solar-bold-StopwatchPlay';
    case WatchRound = 'solar-bold-WatchRound';
    case CalendarAdd = 'solar-bold-CalendarAdd';
    case CalendarDate = 'solar-bold-CalendarDate';
    case Stopwatch = 'solar-bold-Stopwatch';
    case AlarmPause = 'solar-bold-AlarmPause';
    case AlarmTurnOff = 'solar-bold-AlarmTurnOff';
    case ClockSquare = 'solar-bold-ClockSquare';
    case StopwatchPause = 'solar-bold-StopwatchPause';
    case CalendarMinimalistic = 'solar-bold-CalendarMinimalistic';
    case AlarmAdd = 'solar-bold-AlarmAdd';
    case AlarmPlay = 'solar-bold-AlarmPlay';
    case HourglassLine = 'solar-bold-HourglassLine';
    case AlarmSleep = 'solar-bold-AlarmSleep';
    case AlarmRemove = 'solar-bold-AlarmRemove';
    case Calendar = 'solar-bold-Calendar';
    case ClockCircle = 'solar-bold-ClockCircle';
    case History = 'solar-bold-History';
    case Alarm = 'solar-bold-Alarm';
    case WatchSquare = 'solar-bold-WatchSquare';
    case WatchSquareMinimalistic = 'solar-bold-WatchSquareMinimalistic';
    case MagniferBug = 'solar-bold-MagniferBug';
    case Magnifer = 'solar-bold-Magnifer';
    case MagniferZoomIn = 'solar-bold-MagniferZoomIn';
    case RoundedMagnifer = 'solar-bold-RoundedMagnifer';
    case RoundedMagniferZoomIn = 'solar-bold-RoundedMagniferZoomIn';
    case MinimalisticMagniferBug = 'solar-bold-MinimalisticMagniferBug';
    case RoundedMagniferBug = 'solar-bold-RoundedMagniferBug';
    case MinimalisticMagniferZoomOut = 'solar-bold-MinimalisticMagniferZoomOut';
    case MinimalisticMagnifer = 'solar-bold-MinimalisticMagnifer';
    case RoundedMagniferZoomOut = 'solar-bold-RoundedMagniferZoomOut';
    case MinimalisticMagniferZoomIn = 'solar-bold-MinimalisticMagniferZoomIn';
    case MagniferZoomOut = 'solar-bold-MagniferZoomOut';
    case BagCheck = 'solar-bold-BagCheck';
    case ShopMinimalistic = 'solar-bold-ShopMinimalistic';
    case Shop = 'solar-bold-Shop';
    case CartCheck = 'solar-bold-CartCheck';
    case Cart = 'solar-bold-Cart';
    case Cart3 = 'solar-bold-Cart3';
    case Cart2 = 'solar-bold-Cart2';
    case BagMusic = 'solar-bold-BagMusic';
    case CartLargeMinimalistic = 'solar-bold-CartLargeMinimalistic';
    case Cart5 = 'solar-bold-Cart5';
    case Cart4 = 'solar-bold-Cart4';
    case Bag = 'solar-bold-Bag';
    case BagHeart = 'solar-bold-BagHeart';
    case CartPlus = 'solar-bold-CartPlus';
    case CartLarge = 'solar-bold-CartLarge';
    case BagCross = 'solar-bold-BagCross';
    case BagMusic2 = 'solar-bold-BagMusic2';
    case Bag5 = 'solar-bold-Bag5';
    case Bag4 = 'solar-bold-Bag4';
    case CartLarge4 = 'solar-bold-CartLarge4';
    case CartLarge3 = 'solar-bold-CartLarge3';
    case Bag3 = 'solar-bold-Bag3';
    case Bag2 = 'solar-bold-Bag2';
    case Shop2 = 'solar-bold-Shop2';
    case CartLarge2 = 'solar-bold-CartLarge2';
    case BagSmile = 'solar-bold-BagSmile';
    case CartCross = 'solar-bold-CartCross';
    case InfoSquare = 'solar-bold-InfoSquare';
    case FlashlightOn = 'solar-bold-FlashlightOn';
    case XXX = 'solar-bold-XXX';
    case Figma = 'solar-bold-Figma';
    case Flashlight = 'solar-bold-Flashlight';
    case Ghost = 'solar-bold-Ghost';
    case CupMusic = 'solar-bold-CupMusic';
    case BatteryFullMinimalistic = 'solar-bold-BatteryFullMinimalistic';
    case DangerCircle = 'solar-bold-DangerCircle';
    case CheckSquare = 'solar-bold-CheckSquare';
    case GhostSmile = 'solar-bold-GhostSmile';
    case Target = 'solar-bold-Target';
    case BatteryHalfMinimalistic = 'solar-bold-BatteryHalfMinimalistic';
    case Scissors = 'solar-bold-Scissors';
    case PinList = 'solar-bold-PinList';
    case BatteryCharge = 'solar-bold-BatteryCharge';
    case Umbrella = 'solar-bold-Umbrella';
    case HomeSmile = 'solar-bold-HomeSmile';
    case Home = 'solar-bold-Home';
    case Copyright = 'solar-bold-Copyright';
    case HomeWifi = 'solar-bold-HomeWifi';
    case TShirt = 'solar-bold-TShirt';
    case BatteryChargeMinimalistic = 'solar-bold-BatteryChargeMinimalistic';
    case CupStar = 'solar-bold-CupStar';
    case SpecialEffects = 'solar-bold-SpecialEffects';
    case Body = 'solar-bold-Body';
    case HamburgerMenu = 'solar-bold-HamburgerMenu';
    case Power = 'solar-bold-Power';
    case Database = 'solar-bold-Database';
    case CursorSquare = 'solar-bold-CursorSquare';
    case Fuel = 'solar-bold-Fuel';
    case MentionCircle = 'solar-bold-MentionCircle';
    case ConfettiMinimalistic = 'solar-bold-ConfettiMinimalistic';
    case MenuDotsCircle = 'solar-bold-MenuDotsCircle';
    case Paw = 'solar-bold-Paw';
    case Subtitles = 'solar-bold-Subtitles';
    case SliderVerticalMinimalistic = 'solar-bold-SliderVerticalMinimalistic';
    case CrownMinimalistic = 'solar-bold-CrownMinimalistic';
    case MenuDots = 'solar-bold-MenuDots';
    case Delivery = 'solar-bold-Delivery';
    case Waterdrop = 'solar-bold-Waterdrop';
    case Perfume = 'solar-bold-Perfume';
    case HomeAngle2 = 'solar-bold-HomeAngle2';
    case HomeWifiAngle = 'solar-bold-HomeWifiAngle';
    case QuestionCircle = 'solar-bold-QuestionCircle';
    case TrashBinMinimalistic = 'solar-bold-TrashBinMinimalistic';
    case MagicStick3 = 'solar-bold-MagicStick3';
    case AddSquare = 'solar-bold-AddSquare';
    case CrownStar = 'solar-bold-CrownStar';
    case Magnet = 'solar-bold-Magnet';
    case Confetti = 'solar-bold-Confetti';
    case Pin = 'solar-bold-Pin';
    case MinusSquare = 'solar-bold-MinusSquare';
    case Bolt = 'solar-bold-Bolt';
    case CloseCircle = 'solar-bold-CloseCircle';
    case ForbiddenCircle = 'solar-bold-ForbiddenCircle';
    case MagicStick2 = 'solar-bold-MagicStick2';
    case CrownLine = 'solar-bold-CrownLine';
    case BoltCircle = 'solar-bold-BoltCircle';
    case Flag = 'solar-bold-Flag';
    case SliderHorizontal = 'solar-bold-SliderHorizontal';
    case HighDefinition = 'solar-bold-HighDefinition';
    case Cursor = 'solar-bold-Cursor';
    case Feed = 'solar-bold-Feed';
    case TrafficEconomy = 'solar-bold-TrafficEconomy';
    case AugmentedReality = 'solar-bold-AugmentedReality';
    case Icon4K = 'solar-bold-Icon4K';
    case MagnetWave = 'solar-bold-MagnetWave';
    case HomeSmileAngle = 'solar-bold-HomeSmileAngle';
    case SliderVertical = 'solar-bold-SliderVertical';
    case CheckCircle = 'solar-bold-CheckCircle';
    case Copy = 'solar-bold-Copy';
    case DangerSquare = 'solar-bold-DangerSquare';
    case Skirt = 'solar-bold-Skirt';
    case Glasses = 'solar-bold-Glasses';
    case HomeAdd = 'solar-bold-HomeAdd';
    case Sledgehammer = 'solar-bold-Sledgehammer';
    case InfoCircle = 'solar-bold-InfoCircle';
    case DangerTriangle = 'solar-bold-DangerTriangle';
    case PinCircle = 'solar-bold-PinCircle';
    case SmartHome = 'solar-bold-SmartHome';
    case ScissorsSquare = 'solar-bold-ScissorsSquare';
    case Sleeping = 'solar-bold-Sleeping';
    case Box = 'solar-bold-Box';
    case Crown = 'solar-bold-Crown';
    case Broom = 'solar-bold-Broom';
    case PostsCarouselHorizontal = 'solar-bold-PostsCarouselHorizontal';
    case Flag2 = 'solar-bold-Flag2';
    case Plate = 'solar-bold-Plate';
    case TrashBinTrash = 'solar-bold-TrashBinTrash';
    case CupFirst = 'solar-bold-CupFirst';
    case SmartHomeAngle = 'solar-bold-SmartHomeAngle';
    case PaperBin = 'solar-bold-PaperBin';
    case BoxMinimalistic = 'solar-bold-BoxMinimalistic';
    case Danger = 'solar-bold-Danger';
    case MenuDotsSquare = 'solar-bold-MenuDotsSquare';
    case Hanger2 = 'solar-bold-Hanger2';
    case BatteryHalf = 'solar-bold-BatteryHalf';
    case Home2 = 'solar-bold-Home2';
    case PostsCarouselVertical = 'solar-bold-PostsCarouselVertical';
    case Revote = 'solar-bold-Revote';
    case MentionSquare = 'solar-bold-MentionSquare';
    case WinRar = 'solar-bold-WinRar';
    case Forbidden = 'solar-bold-Forbidden';
    case QuestionSquare = 'solar-bold-QuestionSquare';
    case Hanger = 'solar-bold-Hanger';
    case Reorder = 'solar-bold-Reorder';
    case HomeAddAngle = 'solar-bold-HomeAddAngle';
    case Masks = 'solar-bold-Masks';
    case Gift = 'solar-bold-Gift';
    case CreativeCommons = 'solar-bold-CreativeCommons';
    case SliderMinimalisticHorizontal = 'solar-bold-SliderMinimalisticHorizontal';
    case HomeAngle = 'solar-bold-HomeAngle';
    case BatteryLowMinimalistic = 'solar-bold-BatteryLowMinimalistic';
    case Share = 'solar-bold-Share';
    case TrashBin2 = 'solar-bold-TrashBin2';
    case Sort = 'solar-bold-Sort';
    case MinusCircle = 'solar-bold-MinusCircle';
    case Explicit = 'solar-bold-Explicit';
    case Traffic = 'solar-bold-Traffic';
    case Filter = 'solar-bold-Filter';
    case CloseSquare = 'solar-bold-CloseSquare';
    case AddCircle = 'solar-bold-AddCircle';
    case FerrisWheel = 'solar-bold-FerrisWheel';
    case Cup = 'solar-bold-Cup';
    case Balloon = 'solar-bold-Balloon';
    case Help = 'solar-bold-Help';
    case BatteryFull = 'solar-bold-BatteryFull';
    case Cat = 'solar-bold-Cat';
    case MaskSad = 'solar-bold-MaskSad';
    case HighQuality = 'solar-bold-HighQuality';
    case MagicStick = 'solar-bold-MagicStick';
    case Cosmetic = 'solar-bold-Cosmetic';
    case BatteryLow = 'solar-bold-BatteryLow';
    case ShareCircle = 'solar-bold-ShareCircle';
    case MaskHapply = 'solar-bold-MaskHapply';
    case Accessibility = 'solar-bold-Accessibility';
    case TrashBinMinimalistic2 = 'solar-bold-TrashBinMinimalistic2';
    case IncomingCallRounded = 'solar-bold-IncomingCallRounded';
    case CallDropped = 'solar-bold-CallDropped';
    case CallChat = 'solar-bold-CallChat';
    case CallCancelRounded = 'solar-bold-CallCancelRounded';
    case CallMedicineRounded = 'solar-bold-CallMedicineRounded';
    case CallDroppedRounded = 'solar-bold-CallDroppedRounded';
    case RecordSquare = 'solar-bold-RecordSquare';
    case PhoneCalling = 'solar-bold-PhoneCalling';
    case PhoneRounded = 'solar-bold-PhoneRounded';
    case CallMedicine = 'solar-bold-CallMedicine';
    case RecordMinimalistic = 'solar-bold-RecordMinimalistic';
    case EndCall = 'solar-bold-EndCall';
    case OutgoingCall = 'solar-bold-OutgoingCall';
    case RecordCircle = 'solar-bold-RecordCircle';
    case IncomingCall = 'solar-bold-IncomingCall';
    case CallChatRounded = 'solar-bold-CallChatRounded';
    case EndCallRounded = 'solar-bold-EndCallRounded';
    case Phone = 'solar-bold-Phone';
    case OutgoingCallRounded = 'solar-bold-OutgoingCallRounded';
    case CallCancel = 'solar-bold-CallCancel';
    case PhoneCallingRounded = 'solar-bold-PhoneCallingRounded';
    case StationMinimalistic = 'solar-bold-StationMinimalistic';
    case SidebarCode = 'solar-bold-SidebarCode';
    case WiFiRouterMinimalistic = 'solar-bold-WiFiRouterMinimalistic';
    case USB = 'solar-bold-USB';
    case Siderbar = 'solar-bold-Siderbar';
    case Code2 = 'solar-bold-Code2';
    case SlashCircle = 'solar-bold-SlashCircle';
    case Screencast = 'solar-bold-Screencast';
    case HashtagSquare = 'solar-bold-HashtagSquare';
    case SidebarMinimalistic = 'solar-bold-SidebarMinimalistic';
    case Code = 'solar-bold-Code';
    case UsbSquare = 'solar-bold-UsbSquare';
    case WiFiRouter = 'solar-bold-WiFiRouter';
    case CodeCircle = 'solar-bold-CodeCircle';
    case Translation = 'solar-bold-Translation';
    case BugMinimalistic = 'solar-bold-BugMinimalistic';
    case Station = 'solar-bold-Station';
    case Programming = 'solar-bold-Programming';
    case WiFiRouterRound = 'solar-bold-WiFiRouterRound';
    case Hashtag = 'solar-bold-Hashtag';
    case Bug = 'solar-bold-Bug';
    case HashtagChat = 'solar-bold-HashtagChat';
    case Command = 'solar-bold-Command';
    case Translation2 = 'solar-bold-Translation2';
    case HashtagCircle = 'solar-bold-HashtagCircle';
    case Screencast2 = 'solar-bold-Screencast2';
    case SlashSquare = 'solar-bold-SlashSquare';
    case WindowFrame = 'solar-bold-WindowFrame';
    case Structure = 'solar-bold-Structure';
    case UsbCircle = 'solar-bold-UsbCircle';
    case CodeSquare = 'solar-bold-CodeSquare';
    case Notes = 'solar-bold-Notes';
    case DocumentText = 'solar-bold-DocumentText';
    case DocumentAdd = 'solar-bold-DocumentAdd';
    case DocumentMedicine = 'solar-bold-DocumentMedicine';
    case ArchiveMinimalistic = 'solar-bold-ArchiveMinimalistic';
    case Clipboard = 'solar-bold-Clipboard';
    case ClipboardAdd = 'solar-bold-ClipboardAdd';
    case Archive = 'solar-bold-Archive';
    case ClipboardHeart = 'solar-bold-ClipboardHeart';
    case ClipboardRemove = 'solar-bold-ClipboardRemove';
    case ClipboardText = 'solar-bold-ClipboardText';
    case Document = 'solar-bold-Document';
    case NotesMinimalistic = 'solar-bold-NotesMinimalistic';
    case ArchiveUp = 'solar-bold-ArchiveUp';
    case ArchiveUpMinimlistic = 'solar-bold-ArchiveUpMinimlistic';
    case ArchiveCheck = 'solar-bold-ArchiveCheck';
    case ArchiveDown = 'solar-bold-ArchiveDown';
    case ArchiveDownMinimlistic = 'solar-bold-ArchiveDownMinimlistic';
    case DocumentsMinimalistic = 'solar-bold-DocumentsMinimalistic';
    case ClipboardCheck = 'solar-bold-ClipboardCheck';
    case ClipboardList = 'solar-bold-ClipboardList';
    case Documents = 'solar-bold-Documents';
    case Notebook = 'solar-bold-Notebook';
    case GalleryRound = 'solar-bold-GalleryRound';
    case PlayCircle = 'solar-bold-PlayCircle';
    case Stream = 'solar-bold-Stream';
    case GalleryRemove = 'solar-bold-GalleryRemove';
    case Clapperboard = 'solar-bold-Clapperboard';
    case PauseCircle = 'solar-bold-PauseCircle';
    case Rewind5SecondsBack = 'solar-bold-Rewind5SecondsBack';
    case Repeat = 'solar-bold-Repeat';
    case ClapperboardEdit = 'solar-bold-ClapperboardEdit';
    case VideoFrameCut = 'solar-bold-VideoFrameCut';
    case Panorama = 'solar-bold-Panorama';
    case PlayStream = 'solar-bold-PlayStream';
    case ClapperboardOpen = 'solar-bold-ClapperboardOpen';
    case ClapperboardText = 'solar-bold-ClapperboardText';
    case Library = 'solar-bold-Library';
    case Reel2 = 'solar-bold-Reel2';
    case VolumeSmall = 'solar-bold-VolumeSmall';
    case VideoFrame = 'solar-bold-VideoFrame';
    case MicrophoneLarge = 'solar-bold-MicrophoneLarge';
    case RewindForward = 'solar-bold-RewindForward';
    case RewindBackCircle = 'solar-bold-RewindBackCircle';
    case Microphone = 'solar-bold-Microphone';
    case VideoFrameReplace = 'solar-bold-VideoFrameReplace';
    case ClapperboardPlay = 'solar-bold-ClapperboardPlay';
    case GalleryDownload = 'solar-bold-GalleryDownload';
    case MusicNote4 = 'solar-bold-MusicNote4';
    case VideocameraRecord = 'solar-bold-VideocameraRecord';
    case PlaybackSpeed = 'solar-bold-PlaybackSpeed';
    case Soundwave = 'solar-bold-Soundwave';
    case StopCircle = 'solar-bold-StopCircle';
    case QuitFullScreenCircle = 'solar-bold-QuitFullScreenCircle';
    case RewindBack = 'solar-bold-RewindBack';
    case RepeatOne = 'solar-bold-RepeatOne';
    case GalleryCheck = 'solar-bold-GalleryCheck';
    case Wallpaper = 'solar-bold-Wallpaper';
    case RewindForwardCircle = 'solar-bold-RewindForwardCircle';
    case GalleryEdit = 'solar-bold-GalleryEdit';
    case Gallery = 'solar-bold-Gallery';
    case GalleryMinimalistic = 'solar-bold-GalleryMinimalistic';
    case UploadTrack = 'solar-bold-UploadTrack';
    case Volume = 'solar-bold-Volume';
    case UploadTrack2 = 'solar-bold-UploadTrack2';
    case MusicNotes = 'solar-bold-MusicNotes';
    case MusicNote2 = 'solar-bold-MusicNote2';
    case CameraAdd = 'solar-bold-CameraAdd';
    case Podcast = 'solar-bold-Podcast';
    case CameraRotate = 'solar-bold-CameraRotate';
    case MusicNote3 = 'solar-bold-MusicNote3';
    case Stop = 'solar-bold-Stop';
    case Muted = 'solar-bold-Muted';
    case SkipNext = 'solar-bold-SkipNext';
    case GallerySend = 'solar-bold-GallerySend';
    case Record = 'solar-bold-Record';
    case FullScreenCircle = 'solar-bold-FullScreenCircle';
    case VolumeCross = 'solar-bold-VolumeCross';
    case SoundwaveCircle = 'solar-bold-SoundwaveCircle';
    case SkipPrevious = 'solar-bold-SkipPrevious';
    case Rewind5SecondsForward = 'solar-bold-Rewind5SecondsForward';
    case Play = 'solar-bold-Play';
    case PIP = 'solar-bold-PIP';
    case MusicLibrary = 'solar-bold-MusicLibrary';
    case VideoFrame2 = 'solar-bold-VideoFrame2';
    case Camera = 'solar-bold-Camera';
    case QuitPip = 'solar-bold-QuitPip';
    case ClapperboardOpenPlay = 'solar-bold-ClapperboardOpenPlay';
    case Rewind10SecondsBack = 'solar-bold-Rewind10SecondsBack';
    case RepeatOneMinimalistic = 'solar-bold-RepeatOneMinimalistic';
    case Vinyl = 'solar-bold-Vinyl';
    case VideoLibrary = 'solar-bold-VideoLibrary';
    case GalleryWide = 'solar-bold-GalleryWide';
    case Reel = 'solar-bold-Reel';
    case ToPip = 'solar-bold-ToPip';
    case Pip2 = 'solar-bold-Pip2';
    case FullScreen = 'solar-bold-FullScreen';
    case CameraMinimalistic = 'solar-bold-CameraMinimalistic';
    case VideoFrameCut2 = 'solar-bold-VideoFrameCut2';
    case GalleryCircle = 'solar-bold-GalleryCircle';
    case VideoFramePlayHorizontal = 'solar-bold-VideoFramePlayHorizontal';
    case MusicNoteSlider2 = 'solar-bold-MusicNoteSlider2';
    case MusicNoteSlider = 'solar-bold-MusicNoteSlider';
    case VideocameraAdd = 'solar-bold-VideocameraAdd';
    case QuitFullScreenSquare = 'solar-bold-QuitFullScreenSquare';
    case Album = 'solar-bold-Album';
    case GalleryAdd = 'solar-bold-GalleryAdd';
    case CameraSquare = 'solar-bold-CameraSquare';
    case Rewind15SecondsBack = 'solar-bold-Rewind15SecondsBack';
    case Rewind15SecondsForward = 'solar-bold-Rewind15SecondsForward';
    case VinylRecord = 'solar-bold-VinylRecord';
    case Shuffle = 'solar-bold-Shuffle';
    case Pause = 'solar-bold-Pause';
    case MusicNote = 'solar-bold-MusicNote';
    case QuitFullScreen = 'solar-bold-QuitFullScreen';
    case Microphone2 = 'solar-bold-Microphone2';
    case Videocamera = 'solar-bold-Videocamera';
    case GalleryFavourite = 'solar-bold-GalleryFavourite';
    case MusicLibrary2 = 'solar-bold-MusicLibrary2';
    case VideoFramePlayVertical = 'solar-bold-VideoFramePlayVertical';
    case FullScreenSquare = 'solar-bold-FullScreenSquare';
    case Rewind10SecondsForward = 'solar-bold-Rewind10SecondsForward';
    case VolumeLoud = 'solar-bold-VolumeLoud';
    case Microphone3 = 'solar-bold-Microphone3';
    case SoundwaveSquare = 'solar-bold-SoundwaveSquare';
    case Cardholder = 'solar-bold-Cardholder';
    case BillList = 'solar-bold-BillList';
    case SaleSquare = 'solar-bold-SaleSquare';
    case Dollar = 'solar-bold-Dollar';
    case Ticket = 'solar-bold-Ticket';
    case Tag = 'solar-bold-Tag';
    case CashOut = 'solar-bold-CashOut';
    case Wallet2 = 'solar-bold-Wallet2';
    case Ruble = 'solar-bold-Ruble';
    case CardTransfer = 'solar-bold-CardTransfer';
    case Euro = 'solar-bold-Euro';
    case Sale = 'solar-bold-Sale';
    case CardSearch = 'solar-bold-CardSearch';
    case Wallet = 'solar-bold-Wallet';
    case BillCross = 'solar-bold-BillCross';
    case TicketSale = 'solar-bold-TicketSale';
    case SafeSquare = 'solar-bold-SafeSquare';
    case Card = 'solar-bold-Card';
    case Safe2 = 'solar-bold-Safe2';
    case DollarMinimalistic = 'solar-bold-DollarMinimalistic';
    case TagPrice = 'solar-bold-TagPrice';
    case MoneyBag = 'solar-bold-MoneyBag';
    case Bill = 'solar-bold-Bill';
    case CardSend = 'solar-bold-CardSend';
    case CardRecive = 'solar-bold-CardRecive';
    case Banknote2 = 'solar-bold-Banknote2';
    case TagHorizontal = 'solar-bold-TagHorizontal';
    case BillCheck = 'solar-bold-BillCheck';
    case TickerStar = 'solar-bold-TickerStar';
    case Banknote = 'solar-bold-Banknote';
    case VerifiedCheck = 'solar-bold-VerifiedCheck';
    case WadOfMoney = 'solar-bold-WadOfMoney';
    case Card2 = 'solar-bold-Card2';
    case SafeCircle = 'solar-bold-SafeCircle';
    case WalletMoney = 'solar-bold-WalletMoney';
    case List = 'solar-bold-List';
    case ListDownMinimalistic = 'solar-bold-ListDownMinimalistic';
    case Playlist2 = 'solar-bold-Playlist2';
    case ChecklistMinimalistic = 'solar-bold-ChecklistMinimalistic';
    case PlaaylistMinimalistic = 'solar-bold-PlaaylistMinimalistic';
    case ListHeart = 'solar-bold-ListHeart';
    case ListArrowDown = 'solar-bold-ListArrowDown';
    case ListArrowUp = 'solar-bold-ListArrowUp';
    case ListUpMinimalistic = 'solar-bold-ListUpMinimalistic';
    case Playlist = 'solar-bold-Playlist';
    case ListUp = 'solar-bold-ListUp';
    case ListCrossMinimalistic = 'solar-bold-ListCrossMinimalistic';
    case ListCross = 'solar-bold-ListCross';
    case ListArrowDownMinimalistic = 'solar-bold-ListArrowDownMinimalistic';
    case SortByAlphabet = 'solar-bold-SortByAlphabet';
    case Checklist = 'solar-bold-Checklist';
    case SortFromBottomToTop = 'solar-bold-SortFromBottomToTop';
    case ListCheck = 'solar-bold-ListCheck';
    case PlaylistMinimalistic2 = 'solar-bold-PlaylistMinimalistic2';
    case PlaylistMinimalistic3 = 'solar-bold-PlaylistMinimalistic3';
    case List1 = 'solar-bold-List1';
    case SortFromTopToBottom = 'solar-bold-SortFromTopToBottom';
    case SortByTime = 'solar-bold-SortByTime';
    case ListDown = 'solar-bold-ListDown';
    case ListHeartMinimalistic = 'solar-bold-ListHeartMinimalistic';
    case ListCheckMinimalistic = 'solar-bold-ListCheckMinimalistic';
    case ListArrowUpMinimalistic = 'solar-bold-ListArrowUpMinimalistic';
    case UserCrossRounded = 'solar-bold-UserCrossRounded';
    case User = 'solar-bold-User';
    case UsersGroupRounded = 'solar-bold-UsersGroupRounded';
    case UserPlusRounded = 'solar-bold-UserPlusRounded';
    case UserBlock = 'solar-bold-UserBlock';
    case UserMinus = 'solar-bold-UserMinus';
    case UserHands = 'solar-bold-UserHands';
    case UserHeart = 'solar-bold-UserHeart';
    case UserMinusRounded = 'solar-bold-UserMinusRounded';
    case UserCross = 'solar-bold-UserCross';
    case UserSpeakRounded = 'solar-bold-UserSpeakRounded';
    case UserId = 'solar-bold-UserId';
    case UserBlockRounded = 'solar-bold-UserBlockRounded';
    case UserHeartRounded = 'solar-bold-UserHeartRounded';
    case UsersGroupTwoRounded = 'solar-bold-UsersGroupTwoRounded';
    case UserHandUp = 'solar-bold-UserHandUp';
    case UserCircle = 'solar-bold-UserCircle';
    case UserRounded = 'solar-bold-UserRounded';
    case UserCheck = 'solar-bold-UserCheck';
    case UserPlus = 'solar-bold-UserPlus';
    case UserCheckRounded = 'solar-bold-UserCheckRounded';
    case UserSpeak = 'solar-bold-UserSpeak';
    case Virus = 'solar-bold-Virus';
    case AdhesivePlaster2 = 'solar-bold-AdhesivePlaster2';
    case Dropper = 'solar-bold-Dropper';
    case Pulse2 = 'solar-bold-Pulse2';
    case BoneBroken = 'solar-bold-BoneBroken';
    case HeartPulse2 = 'solar-bold-HeartPulse2';
    case MedicalKit = 'solar-bold-MedicalKit';
    case TestTube = 'solar-bold-TestTube';
    case Health = 'solar-bold-Health';
    case DropperMinimalistic2 = 'solar-bold-DropperMinimalistic2';
    case DNA = 'solar-bold-DNA';
    case Dropper3 = 'solar-bold-Dropper3';
    case Thermometer = 'solar-bold-Thermometer';
    case Dropper2 = 'solar-bold-Dropper2';
    case JarOfPills2 = 'solar-bold-JarOfPills2';
    case BoneCrack = 'solar-bold-BoneCrack';
    case JarOfPills = 'solar-bold-JarOfPills';
    case Syringe = 'solar-bold-Syringe';
    case Stethoscope = 'solar-bold-Stethoscope';
    case BenzeneRing = 'solar-bold-BenzeneRing';
    case Bacteria = 'solar-bold-Bacteria';
    case AdhesivePlaster = 'solar-bold-AdhesivePlaster';
    case Bone = 'solar-bold-Bone';
    case Bones = 'solar-bold-Bones';
    case Pill = 'solar-bold-Pill';
    case Pills = 'solar-bold-Pills';
    case HeartPulse = 'solar-bold-HeartPulse';
    case TestTubeMinimalistic = 'solar-bold-TestTubeMinimalistic';
    case Pills2 = 'solar-bold-Pills2';
    case Pulse = 'solar-bold-Pulse';
    case DropperMinimalistic = 'solar-bold-DropperMinimalistic';
    case Pills3 = 'solar-bold-Pills3';
    case Whisk = 'solar-bold-Whisk';
    case Bottle = 'solar-bold-Bottle';
    case OvenMittsMinimalistic = 'solar-bold-OvenMittsMinimalistic';
    case ChefHatMinimalistic = 'solar-bold-ChefHatMinimalistic';
    case TeaCup = 'solar-bold-TeaCup';
    case WineglassTriangle = 'solar-bold-WineglassTriangle';
    case OvenMitts = 'solar-bold-OvenMitts';
    case CupPaper = 'solar-bold-CupPaper';
    case Ladle = 'solar-bold-Ladle';
    case Corkscrew = 'solar-bold-Corkscrew';
    case DonutBitten = 'solar-bold-DonutBitten';
    case Wineglass = 'solar-bold-Wineglass';
    case Donut = 'solar-bold-Donut';
    case CupHot = 'solar-bold-CupHot';
    case ChefHatHeart = 'solar-bold-ChefHatHeart';
    case ChefHat = 'solar-bold-ChefHat';
    case RollingPin = 'solar-bold-RollingPin';
    case CodeFile = 'solar-bold-CodeFile';
    case FileCorrupted = 'solar-bold-FileCorrupted';
    case File = 'solar-bold-File';
    case FileRight = 'solar-bold-FileRight';
    case FileFavourite = 'solar-bold-FileFavourite';
    case FileDownload = 'solar-bold-FileDownload';
    case ZipFile = 'solar-bold-ZipFile';
    case FileText = 'solar-bold-FileText';
    case FileSmile = 'solar-bold-FileSmile)';
    case FileCheck = 'solar-bold-FileCheck';
    case FileSend = 'solar-bold-FileSend';
    case FileLeft = 'solar-bold-FileLeft';
    case FigmaFile = 'solar-bold-FigmaFile';
    case FileRemove = 'solar-bold-FileRemove';
    case CloudFile = 'solar-bold-CloudFile';
    case Suspension = 'solar-bold-Suspension';
    case SpedometerMax = 'solar-bold-SpedometerMax';
    case TransmissionCircle = 'solar-bold-TransmissionCircle';
    case GasStation = 'solar-bold-GasStation';
    case Wheel = 'solar-bold-Wheel';
    case Transmission = 'solar-bold-Transmission';
    case KickScooter = 'solar-bold-KickScooter';
    case SpedometerLow = 'solar-bold-SpedometerLow';
    case SpedometerMiddle = 'solar-bold-SpedometerMiddle';
    case WheelAngle = 'solar-bold-WheelAngle';
    case Tram = 'solar-bold-Tram';
    case TransmissionSquare = 'solar-bold-TransmissionSquare';
    case Scooter = 'solar-bold-Scooter';
    case ShockAbsorber = 'solar-bold-ShockAbsorber';
    case Bus = 'solar-bold-Bus';
    case SuspensionCross = 'solar-bold-SuspensionCross';
    case SuspensionBolt = 'solar-bold-SuspensionBolt';
    case ElectricRefueling = 'solar-bold-ElectricRefueling';
    case Accumulator = 'solar-bold-Accumulator';
    case HandPills = 'solar-bold-HandPills';
    case HandMoney = 'solar-bold-HandMoney';
    case HandShake = 'solar-bold-HandShake';
    case HandHeart = 'solar-bold-HandHeart';
    case HandStars = 'solar-bold-HandStars';
    case RemoveFolder = 'solar-bold-RemoveFolder';
    case FolderFavouritestar = 'solar-bold-FolderFavourite(star)';
    case AddFolder = 'solar-bold-AddFolder';
    case FolderCheck = 'solar-bold-FolderCheck';
    case FolderFavouritebookmark = 'solar-bold-FolderFavourite(bookmark)';
    case Folder2 = 'solar-bold-Folder2';
    case FolderSecurity = 'solar-bold-FolderSecurity';
    case FolderCloud = 'solar-bold-FolderCloud';
    case MoveToFolder = 'solar-bold-MoveToFolder';
    case FolderError = 'solar-bold-FolderError';
    case FolderPathConnect = 'solar-bold-FolderPathConnect';
    case FolderOpen = 'solar-bold-FolderOpen';
    case Folder = 'solar-bold-Folder';
    case FolderWithFiles = 'solar-bold-FolderWithFiles';
    case CloudCheck = 'solar-bold-CloudCheck';
    case Temperature = 'solar-bold-Temperature';
    case Wind = 'solar-bold-Wind';
    case CloudSnowfall = 'solar-bold-CloudSnowfall';
    case Sunrise = 'solar-bold-Sunrise';
    case Sun2 = 'solar-bold-Sun2';
    case CloudSun = 'solar-bold-CloudSun';
    case CloudBoltMinimalistic = 'solar-bold-CloudBoltMinimalistic';
    case CloudDownload = 'solar-bold-CloudDownload';
    case Clouds = 'solar-bold-Clouds';
    case Tornado = 'solar-bold-Tornado';
    case MoonSleep = 'solar-bold-MoonSleep';
    case CloudUpload = 'solar-bold-CloudUpload';
    case CloudRain = 'solar-bold-CloudRain';
    case Fog = 'solar-bold-Fog';
    case Snowflake = 'solar-bold-Snowflake';
    case MoonFog = 'solar-bold-MoonFog';
    case CloudMinus = 'solar-bold-CloudMinus';
    case CloudBolt = 'solar-bold-CloudBolt';
    case CloudWaterdrop = 'solar-bold-CloudWaterdrop';
    case Sunset = 'solar-bold-Sunset';
    case Waterdrops = 'solar-bold-Waterdrops';
    case MoonStars = 'solar-bold-MoonStars';
    case CloudPlus = 'solar-bold-CloudPlus';
    case Sun = 'solar-bold-Sun';
    case CloudWaterdrops = 'solar-bold-CloudWaterdrops';
    case CloudSun2 = 'solar-bold-CloudSun2';
    case CloudyMoon = 'solar-bold-CloudyMoon';
    case TornadoSmall = 'solar-bold-TornadoSmall';
    case Cloud = 'solar-bold-Cloud';
    case SunFog = 'solar-bold-SunFog';
    case CloundCross = 'solar-bold-CloundCross';
    case CloudSnowfallMinimalistic = 'solar-bold-CloudSnowfallMinimalistic';
    case CloudStorm = 'solar-bold-CloudStorm';
    case Moon = 'solar-bold-Moon';
    case RefreshCircle = 'solar-bold-RefreshCircle';
    case SquareArrowRightDown = 'solar-bold-SquareArrowRightDown';
    case RoundArrowLeftDown = 'solar-bold-RoundArrowLeftDown';
    case Restart = 'solar-bold-Restart';
    case RoundAltArrowDown = 'solar-bold-RoundAltArrowDown';
    case RoundSortVertical = 'solar-bold-RoundSortVertical';
    case SquareAltArrowUp = 'solar-bold-SquareAltArrowUp';
    case ArrowLeftUp = 'solar-bold-ArrowLeftUp';
    case SortHorizontal = 'solar-bold-SortHorizontal';
    case TransferHorizontal = 'solar-bold-TransferHorizontal';
    case SquareDoubleAltArrowUp = 'solar-bold-SquareDoubleAltArrowUp';
    case RoundArrowLeftUp = 'solar-bold-RoundArrowLeftUp';
    case AltArrowRight = 'solar-bold-AltArrowRight';
    case RoundDoubleAltArrowUp = 'solar-bold-RoundDoubleAltArrowUp';
    case RestartCircle = 'solar-bold-RestartCircle';
    case SquareArrowDown = 'solar-bold-SquareArrowDown';
    case SortVertical = 'solar-bold-SortVertical';
    case SquareSortHorizontal = 'solar-bold-SquareSortHorizontal';
    case DoubleAltArrowLeft = 'solar-bold-DoubleAltArrowLeft';
    case SquareAltArrowDown = 'solar-bold-SquareAltArrowDown';
    case SquareAltArrowRight = 'solar-bold-SquareAltArrowRight';
    case SquareArrowUp = 'solar-bold-SquareArrowUp';
    case DoubleAltArrowRight = 'solar-bold-DoubleAltArrowRight';
    case RoundTransferVertical = 'solar-bold-RoundTransferVertical';
    case ArrowLeft = 'solar-bold-ArrowLeft';
    case RoundDoubleAltArrowRight = 'solar-bold-RoundDoubleAltArrowRight';
    case SquareDoubleAltArrowLeft = 'solar-bold-SquareDoubleAltArrowLeft';
    case AltArrowDown = 'solar-bold-AltArrowDown';
    case RoundTransferHorizontal = 'solar-bold-RoundTransferHorizontal';
    case RoundArrowRightDown = 'solar-bold-RoundArrowRightDown';
    case ArrowUp = 'solar-bold-ArrowUp';
    case RoundArrowLeft = 'solar-bold-RoundArrowLeft';
    case DoubleAltArrowUp = 'solar-bold-DoubleAltArrowUp';
    case RoundArrowRight = 'solar-bold-RoundArrowRight';
    case SquareTransferHorizontal = 'solar-bold-SquareTransferHorizontal';
    case ArrowRight = 'solar-bold-ArrowRight';
    case RoundDoubleAltArrowLeft = 'solar-bold-RoundDoubleAltArrowLeft';
    case RoundArrowUp = 'solar-bold-RoundArrowUp';
    case SquareSortVertical = 'solar-bold-SquareSortVertical';
    case AltArrowLeft = 'solar-bold-AltArrowLeft';
    case SquareDoubleAltArrowRight = 'solar-bold-SquareDoubleAltArrowRight';
    case Refresh = 'solar-bold-Refresh';
    case TransferVertical = 'solar-bold-TransferVertical';
    case RefreshSquare = 'solar-bold-RefreshSquare';
    case SquareTransferVertical = 'solar-bold-SquareTransferVertical';
    case SquareDoubleAltArrowDown = 'solar-bold-SquareDoubleAltArrowDown';
    case RoundArrowRightUp = 'solar-bold-RoundArrowRightUp';
    case ArrowDown = 'solar-bold-ArrowDown';
    case RestartSquare = 'solar-bold-RestartSquare';
    case SquareArrowRight = 'solar-bold-SquareArrowRight';
    case RoundDoubleAltArrowDown = 'solar-bold-RoundDoubleAltArrowDown';
    case SquareArrowLeftUp = 'solar-bold-SquareArrowLeftUp';
    case RoundArrowDown = 'solar-bold-RoundArrowDown';
    case SquareArrowRightUp = 'solar-bold-SquareArrowRightUp';
    case RoundTransferDiagonal = 'solar-bold-RoundTransferDiagonal';
    case ArrowRightDown = 'solar-bold-ArrowRightDown';
    case ArrowLeftDown = 'solar-bold-ArrowLeftDown';
    case RoundAltArrowLeft = 'solar-bold-RoundAltArrowLeft';
    case ArrowRightUp = 'solar-bold-ArrowRightUp';
    case SquareArrowLeftDown = 'solar-bold-SquareArrowLeftDown';
    case RoundAltArrowUp = 'solar-bold-RoundAltArrowUp';
    case AltArrowUp = 'solar-bold-AltArrowUp';
    case SquareAltArrowLeft = 'solar-bold-SquareAltArrowLeft';
    case RoundSortHorizontal = 'solar-bold-RoundSortHorizontal';
    case DoubleAltArrowDown = 'solar-bold-DoubleAltArrowDown';
    case RoundAltArrowRight = 'solar-bold-RoundAltArrowRight';
    case SquareArrowLeft = 'solar-bold-SquareArrowLeft';
    case TuningSquare2 = 'solar-bold-TuningSquare2';
    case WidgetAdd = 'solar-bold-WidgetAdd';
    case TuningSquare = 'solar-bold-TuningSquare';
    case SettingsMinimalistic = 'solar-bold-SettingsMinimalistic';
    case Widget6 = 'solar-bold-Widget6';
    case Widget4 = 'solar-bold-Widget4';
    case Settings = 'solar-bold-Settings';
    case Widget5 = 'solar-bold-Widget5';
    case Widget2 = 'solar-bold-Widget2';
    case Widget3 = 'solar-bold-Widget3';
    case Tuning2 = 'solar-bold-Tuning2';
    case Tuning3 = 'solar-bold-Tuning3';
    case Widget = 'solar-bold-Widget';
    case Tuning4 = 'solar-bold-Tuning4';
    case Tuning = 'solar-bold-Tuning';
    case DiagramDown = 'solar-bold-DiagramDown';
    case Chart2 = 'solar-bold-Chart2';
    case Chart = 'solar-bold-Chart';
    case DiagramUp = 'solar-bold-DiagramUp';
    case GraphNew = 'solar-bold-GraphNew';
    case CourseUp = 'solar-bold-CourseUp';
    case GraphDownNew = 'solar-bold-GraphDownNew';
    case PieChart3 = 'solar-bold-PieChart3';
    case PieChart2 = 'solar-bold-PieChart2';
    case GraphNewUp = 'solar-bold-GraphNewUp';
    case PieChart = 'solar-bold-PieChart';
    case RoundGraph = 'solar-bold-RoundGraph';
    case GraphUp = 'solar-bold-GraphUp';
    case ChartSquare = 'solar-bold-ChartSquare';
    case CourseDown = 'solar-bold-CourseDown';
    case ChatSquare2 = 'solar-bold-ChatSquare2';
    case GraphDown = 'solar-bold-GraphDown';
    case Graph = 'solar-bold-Graph';
    case PresentationGraph = 'solar-bold-PresentationGraph';
    case MaximizeSquare3 = 'solar-bold-MaximizeSquare3';
    case MaximizeSquareMinimalistic = 'solar-bold-MaximizeSquareMinimalistic';
    case MaximizeSquare2 = 'solar-bold-MaximizeSquare2';
    case MinimizeSquare = 'solar-bold-MinimizeSquare';
    case DownloadSquare = 'solar-bold-DownloadSquare';
    case UndoLeftRoundSquare = 'solar-bold-UndoLeftRoundSquare';
    case Reply = 'solar-bold-Reply';
    case Logout = 'solar-bold-Logout';
    case ReciveSquare = 'solar-bold-ReciveSquare';
    case Export = 'solar-bold-Export';
    case SendTwiceSquare = 'solar-bold-SendTwiceSquare';
    case UndoLeftRound = 'solar-bold-UndoLeftRound';
    case Forward2 = 'solar-bold-Forward2';
    case Maximize = 'solar-bold-Maximize';
    case UndoRightRound = 'solar-bold-UndoRightRound';
    case MinimizeSquare2 = 'solar-bold-MinimizeSquare2';
    case MinimizeSquare3 = 'solar-bold-MinimizeSquare3';
    case UploadTwiceSquare = 'solar-bold-UploadTwiceSquare';
    case Minimize = 'solar-bold-Minimize';
    case CircleTopUp = 'solar-bold-CircleTopUp';
    case UploadMinimalistic = 'solar-bold-UploadMinimalistic';
    case Download = 'solar-bold-Download';
    case Import = 'solar-bold-Import';
    case Login = 'solar-bold-Login';
    case UndoLeft = 'solar-bold-UndoLeft';
    case SquareTopUp = 'solar-bold-SquareTopUp';
    case DownloadTwiceSquare = 'solar-bold-DownloadTwiceSquare';
    case CircleBottomDown = 'solar-bold-CircleBottomDown';
    case MaximizeSquare = 'solar-bold-MaximizeSquare';
    case UploadSquare = 'solar-bold-UploadSquare';
    case UndoRightSquare = 'solar-bold-UndoRightSquare';
    case ReciveTwiceSquare = 'solar-bold-ReciveTwiceSquare';
    case CircleTopDown = 'solar-bold-CircleTopDown';
    case ArrowToDownLeft = 'solar-bold-ArrowToDownLeft';
    case Logout2 = 'solar-bold-Logout2';
    case Logout3 = 'solar-bold-Logout3';
    case Scale = 'solar-bold-Scale';
    case ArrowToDownRight = 'solar-bold-ArrowToDownRight';
    case DownloadMinimalistic = 'solar-bold-DownloadMinimalistic';
    case MinimizeSquareMinimalistic = 'solar-bold-MinimizeSquareMinimalistic';
    case Reply2 = 'solar-bold-Reply2';
    case SquareBottomUp = 'solar-bold-SquareBottomUp';
    case UndoRight = 'solar-bold-UndoRight';
    case UndoLeftSquare = 'solar-bold-UndoLeftSquare';
    case SendSquare = 'solar-bold-SendSquare';
    case Exit = 'solar-bold-Exit';
    case SquareBottomDown = 'solar-bold-SquareBottomDown';
    case UndoRightRoundSquare = 'solar-bold-UndoRightRoundSquare';
    case ArrowToTopLeft = 'solar-bold-ArrowToTopLeft';
    case CircleBottomUp = 'solar-bold-CircleBottomUp';
    case ScreenShare = 'solar-bold-ScreenShare';
    case Upload = 'solar-bold-Upload';
    case SquareTopDown = 'solar-bold-SquareTopDown';
    case ArrowToTopRight = 'solar-bold-ArrowToTopRight';
    case Login3 = 'solar-bold-Login3';
    case Login2 = 'solar-bold-Login2';
    case City = 'solar-bold-City';
    case Buildings = 'solar-bold-Buildings';
    case Buildings3 = 'solar-bold-Buildings3';
    case Buildings2 = 'solar-bold-Buildings2';
    case Hospital = 'solar-bold-Hospital';
    case Garage = 'solar-bold-Garage';
    case Passport = 'solar-bold-Passport';
    case DiplomaVerified = 'solar-bold-DiplomaVerified';
    case CaseRound = 'solar-bold-CaseRound';
    case Backpack = 'solar-bold-Backpack';
    case Book2 = 'solar-bold-Book2';
    case SquareAcademicCap2 = 'solar-bold-SquareAcademicCap2';
    case CaseRoundMinimalistic = 'solar-bold-CaseRoundMinimalistic';
    case Case = 'solar-bold-Case';
    case BookBookmarkMinimalistic = 'solar-bold-BookBookmarkMinimalistic';
    case BookmarkOpened = 'solar-bold-BookmarkOpened';
    case Diploma = 'solar-bold-Diploma';
    case Book = 'solar-bold-Book';
    case SquareAcademicCap = 'solar-bold-SquareAcademicCap';
    case BookmarkCircle = 'solar-bold-BookmarkCircle';
    case CalculatorMinimalistic = 'solar-bold-CalculatorMinimalistic';
    case NotebookSquare = 'solar-bold-NotebookSquare';
    case BookMinimalistic = 'solar-bold-BookMinimalistic';
    case CaseMinimalistic = 'solar-bold-CaseMinimalistic';
    case NotebookBookmark = 'solar-bold-NotebookBookmark';
    case PassportMinimalistic = 'solar-bold-PassportMinimalistic';
    case BookBookmark = 'solar-bold-BookBookmark';
    case BookmarkSquareMinimalistic = 'solar-bold-BookmarkSquareMinimalistic';
    case Bookmark = 'solar-bold-Bookmark';
    case PlusMinus = 'solar-bold-PlusMinus';
    case Calculator = 'solar-bold-Calculator';
    case BookmarkSquare = 'solar-bold-BookmarkSquare';
    case NotebookMinimalistic = 'solar-bold-NotebookMinimalistic';
    case FireSquare = 'solar-bold-FireSquare';
    case SuitcaseLines = 'solar-bold-SuitcaseLines';
    case Fire = 'solar-bold-Fire';
    case Bonfire = 'solar-bold-Bonfire';
    case SuitcaseTag = 'solar-bold-SuitcaseTag';
    case Leaf = 'solar-bold-Leaf';
    case Suitcase = 'solar-bold-Suitcase';
    case Flame = 'solar-bold-Flame';
    case FireMinimalistic = 'solar-bold-FireMinimalistic';
    case BellBing = 'solar-bold-BellBing';
    case NotificationLinesRemove = 'solar-bold-NotificationLinesRemove';
    case NotificationUnread = 'solar-bold-NotificationUnread';
    case Bell = 'solar-bold-Bell';
    case NotificationRemove = 'solar-bold-NotificationRemove';
    case NotificationUnreadLines = 'solar-bold-NotificationUnreadLines';
    case BellOff = 'solar-bold-BellOff';
    case Lightning = 'solar-bold-Lightning';
    case LightbulbMinimalistic = 'solar-bold-LightbulbMinimalistic';
    case ServerSquareCloud = 'solar-bold-ServerSquareCloud';
    case LightbulbBolt = 'solar-bold-LightbulbBolt';
    case AirbudsCharge = 'solar-bold-AirbudsCharge';
    case ServerPath = 'solar-bold-ServerPath';
    case SimCardMinimalistic = 'solar-bold-SimCardMinimalistic';
    case Smartphone = 'solar-bold-Smartphone';
    case Turntable = 'solar-bold-Turntable';
    case AirbudsCheck = 'solar-bold-AirbudsCheck';
    case MouseMinimalistic = 'solar-bold-MouseMinimalistic';
    case SmartphoneRotateAngle = 'solar-bold-SmartphoneRotateAngle';
    case RadioMinimalistic = 'solar-bold-RadioMinimalistic';
    case Airbuds = 'solar-bold-Airbuds';
    case SmartphoneRotateOrientation = 'solar-bold-SmartphoneRotateOrientation';
    case IPhone = 'solar-bold-IPhone';
    case SimCard = 'solar-bold-SimCard';
    case FlashDrive = 'solar-bold-FlashDrive';
    case Devices = 'solar-bold-Devices';
    case SimCards = 'solar-bold-SimCards';
    case AirbudsCaseOpen = 'solar-bold-AirbudsCaseOpen';
    case TurntableMusicNote = 'solar-bold-TurntableMusicNote';
    case Keyboard = 'solar-bold-Keyboard';
    case GamepadCharge = 'solar-bold-GamepadCharge';
    case Boombox = 'solar-bold-Boombox';
    case SmartSpeakerMinimalistic = 'solar-bold-SmartSpeakerMinimalistic';
    case Telescope = 'solar-bold-Telescope';
    case MonitorCamera = 'solar-bold-MonitorCamera';
    case LaptopMinimalistic = 'solar-bold-LaptopMinimalistic';
    case Server2 = 'solar-bold-Server2';
    case SmartSpeaker = 'solar-bold-SmartSpeaker';
    case Projector = 'solar-bold-Projector';
    case Server = 'solar-bold-Server';
    case TV = 'solar-bold-TV';
    case Cassette2 = 'solar-bold-Cassette2';
    case Radio = 'solar-bold-Radio';
    case SmartphoneVibration = 'solar-bold-SmartphoneVibration';
    case AirbudsLeft = 'solar-bold-AirbudsLeft';
    case HeadphonesRound = 'solar-bold-HeadphonesRound';
    case Gameboy = 'solar-bold-Gameboy';
    case HeadphonesRoundSound = 'solar-bold-HeadphonesRoundSound';
    case CPU = 'solar-bold-CPU';
    case Printer2 = 'solar-bold-Printer2';
    case HeadphonesSquare = 'solar-bold-HeadphonesSquare';
    case ServerSquareUpdate = 'solar-bold-ServerSquareUpdate';
    case PrinterMinimalistic = 'solar-bold-PrinterMinimalistic';
    case Bluetooth = 'solar-bold-Bluetooth';
    case WirelessCharge = 'solar-bold-WirelessCharge';
    case BluetoothCircle = 'solar-bold-BluetoothCircle';
    case AirbudsCaseMinimalistic = 'solar-bold-AirbudsCaseMinimalistic';
    case Lightbulb = 'solar-bold-Lightbulb';
    case AirbudsRemove = 'solar-bold-AirbudsRemove';
    case SmartphoneRotate2 = 'solar-bold-SmartphoneRotate2';
    case SsdSquare = 'solar-bold-SsdSquare';
    case Printer = 'solar-bold-Printer';
    case Smartphone2 = 'solar-bold-Smartphone2';
    case ServerMinimalistic = 'solar-bold-ServerMinimalistic';
    case HeadphonesSquareSound = 'solar-bold-HeadphonesSquareSound';
    case Diskette = 'solar-bold-Diskette';
    case BluetoothWave = 'solar-bold-BluetoothWave';
    case SmartSpeaker2 = 'solar-bold-SmartSpeaker2';
    case Laptop3 = 'solar-bold-Laptop3';
    case Laptop2 = 'solar-bold-Laptop2';
    case MouseCircle = 'solar-bold-MouseCircle';
    case TurntableMinimalistic = 'solar-bold-TurntableMinimalistic';
    case SmartphoneUpdate = 'solar-bold-SmartphoneUpdate';
    case GamepadMinimalistic = 'solar-bold-GamepadMinimalistic';
    case SdCard = 'solar-bold-SdCard';
    case PlugCircle = 'solar-bold-PlugCircle';
    case AirbudsCase = 'solar-bold-AirbudsCase';
    case SsdRound = 'solar-bold-SsdRound';
    case Laptop = 'solar-bold-Laptop';
    case AirbudsRight = 'solar-bold-AirbudsRight';
    case Display = 'solar-bold-Display';
    case MonitorSmartphone = 'solar-bold-MonitorSmartphone';
    case Socket = 'solar-bold-Socket';
    case GamepadOld = 'solar-bold-GamepadOld';
    case CpuBolt = 'solar-bold-CpuBolt';
    case AirbudsCaseCharge = 'solar-bold-AirbudsCaseCharge';
    case Tablet = 'solar-bold-Tablet';
    case Weigher = 'solar-bold-Weigher';
    case ServerSquare = 'solar-bold-ServerSquare';
    case Mouse = 'solar-bold-Mouse';
    case GamepadNoCharge = 'solar-bold-GamepadNoCharge';
    case BluetoothSquare = 'solar-bold-BluetoothSquare';
    case CloudStorage = 'solar-bold-CloudStorage';
    case Gamepad = 'solar-bold-Gamepad';
    case Monitor = 'solar-bold-Monitor';
    case Cassette = 'solar-bold-Cassette';
    // Outline Style (1205 icons)
    case OutlineFacemaskCircle = 'solar-outline-FacemaskCircle';
    case OutlineConfoundedCircle = 'solar-outline-ConfoundedCircle';
    case OutlineSadSquare = 'solar-outline-SadSquare';
    case OutlineSleepingCircle = 'solar-outline-SleepingCircle';
    case OutlineFaceScanCircle = 'solar-outline-FaceScanCircle';
    case OutlineSmileCircle = 'solar-outline-SmileCircle';
    case OutlineStickerSmileCircle = 'solar-outline-StickerSmileCircle';
    case OutlineStickerSquare = 'solar-outline-StickerSquare';
    case OutlineEmojiFunnyCircle = 'solar-outline-EmojiFunnyCircle';
    case OutlineExpressionlessSquare = 'solar-outline-ExpressionlessSquare';
    case OutlineSleepingSquare = 'solar-outline-SleepingSquare';
    case OutlineSadCircle = 'solar-outline-SadCircle';
    case OutlineFacemaskSquare = 'solar-outline-FacemaskSquare';
    case OutlineConfoundedSquare = 'solar-outline-ConfoundedSquare';
    case OutlineFaceScanSquare = 'solar-outline-FaceScanSquare';
    case OutlineSmileSquare = 'solar-outline-SmileSquare';
    case OutlineStickerSmileCircle2 = 'solar-outline-StickerSmileCircle2';
    case OutlineStickerSmileSquare = 'solar-outline-StickerSmileSquare';
    case OutlineEmojiFunnySquare = 'solar-outline-EmojiFunnySquare';
    case OutlineStickerCircle = 'solar-outline-StickerCircle';
    case OutlineExpressionlessCircle = 'solar-outline-ExpressionlessCircle';
    case OutlineLike = 'solar-outline-Like';
    case OutlineMedalStarSquare = 'solar-outline-MedalStarSquare';
    case OutlineDislike = 'solar-outline-Dislike';
    case OutlineStarShine = 'solar-outline-StarShine';
    case OutlineHeartAngle = 'solar-outline-HeartAngle';
    case OutlineMedalRibbon = 'solar-outline-MedalRibbon';
    case OutlineHeartShine = 'solar-outline-HeartShine';
    case OutlineMedalStarCircle = 'solar-outline-MedalStarCircle';
    case OutlineMedalRibbonsStar = 'solar-outline-MedalRibbonsStar';
    case OutlineStar = 'solar-outline-Star';
    case OutlineHeartUnlock = 'solar-outline-HeartUnlock';
    case OutlineMedalRibbonStar = 'solar-outline-MedalRibbonStar';
    case OutlineHeartLock = 'solar-outline-HeartLock';
    case OutlineHeartBroken = 'solar-outline-HeartBroken';
    case OutlineHearts = 'solar-outline-Hearts';
    case OutlineMedalStar = 'solar-outline-MedalStar';
    case OutlineHeart = 'solar-outline-Heart';
    case OutlineCloset = 'solar-outline-Closet';
    case OutlineBed = 'solar-outline-Bed';
    case OutlineWashingMachine = 'solar-outline-WashingMachine';
    case OutlineBedsideTable = 'solar-outline-BedsideTable';
    case OutlineSofa3 = 'solar-outline-Sofa3';
    case OutlineSofa2 = 'solar-outline-Sofa2';
    case OutlineChair2 = 'solar-outline-Chair2';
    case OutlineBath = 'solar-outline-Bath';
    case OutlineSmartVacuumCleaner2 = 'solar-outline-SmartVacuumCleaner2';
    case OutlineCondicioner = 'solar-outline-Condicioner';
    case OutlineSmartVacuumCleaner = 'solar-outline-SmartVacuumCleaner';
    case OutlineRemoteController2 = 'solar-outline-RemoteController2';
    case OutlineFloorLampMinimalistic = 'solar-outline-FloorLampMinimalistic';
    case OutlineLamp = 'solar-outline-Lamp';
    case OutlineBarChair = 'solar-outline-BarChair';
    case OutlineBedsideTable2 = 'solar-outline-BedsideTable2';
    case OutlineCloset2 = 'solar-outline-Closet2';
    case OutlineBedsideTable3 = 'solar-outline-BedsideTable3';
    case OutlineSpeaker = 'solar-outline-Speaker';
    case OutlineVolumeKnob = 'solar-outline-VolumeKnob';
    case OutlineArmchair = 'solar-outline-Armchair';
    case OutlineSpeakerMinimalistic = 'solar-outline-SpeakerMinimalistic';
    case OutlineRemoteController = 'solar-outline-RemoteController';
    case OutlineTrellis = 'solar-outline-Trellis';
    case OutlineFloorLamp = 'solar-outline-FloorLamp';
    case OutlineCondicioner2 = 'solar-outline-Condicioner2';
    case OutlineBedsideTable4 = 'solar-outline-BedsideTable4';
    case OutlineArmchair2 = 'solar-outline-Armchair2';
    case OutlineWashingMachineMinimalistic = 'solar-outline-WashingMachineMinimalistic';
    case OutlineChair = 'solar-outline-Chair';
    case OutlineRemoteControllerMinimalistic = 'solar-outline-RemoteControllerMinimalistic';
    case OutlineChandelier = 'solar-outline-Chandelier';
    case OutlineFridge = 'solar-outline-Fridge';
    case OutlineMirror = 'solar-outline-Mirror';
    case OutlineSofa = 'solar-outline-Sofa';
    case OutlineEarth = 'solar-outline-Earth';
    case OutlineStarsLine = 'solar-outline-StarsLine';
    case OutlineStarFall2 = 'solar-outline-StarFall2';
    case OutlineStarFall = 'solar-outline-StarFall';
    case OutlineBlackHole3 = 'solar-outline-BlackHole3';
    case OutlineWomen = 'solar-outline-Women';
    case OutlineBlackHole = 'solar-outline-BlackHole';
    case OutlineStarRings = 'solar-outline-StarRings';
    case OutlineBlackHole2 = 'solar-outline-BlackHole2';
    case OutlineStarFallMinimalistic2 = 'solar-outline-StarFallMinimalistic2';
    case OutlinePlanet = 'solar-outline-Planet';
    case OutlineSatellite = 'solar-outline-Satellite';
    case OutlineMen = 'solar-outline-Men';
    case OutlineRocket2 = 'solar-outline-Rocket2';
    case OutlineStars = 'solar-outline-Stars';
    case OutlineStarAngle = 'solar-outline-StarAngle';
    case OutlineInfinity = 'solar-outline-Infinity';
    case OutlineUfo2 = 'solar-outline-Ufo2';
    case OutlineUfo3 = 'solar-outline-Ufo3';
    case OutlineStarRing = 'solar-outline-StarRing';
    case OutlinePlanet2 = 'solar-outline-Planet2';
    case OutlinePlanet3 = 'solar-outline-Planet3';
    case OutlineAsteroid = 'solar-outline-Asteroid';
    case OutlineStarsMinimalistic = 'solar-outline-StarsMinimalistic';
    case OutlineUFO = 'solar-outline-UFO';
    case OutlinePlanet4 = 'solar-outline-Planet4';
    case OutlineRocket = 'solar-outline-Rocket';
    case OutlineStarFallMinimalistic = 'solar-outline-StarFallMinimalistic';
    case OutlineStarRainbow = 'solar-outline-StarRainbow';
    case OutlineAtom = 'solar-outline-Atom';
    case OutlineStarCircle = 'solar-outline-StarCircle';
    case OutlineCompassBig = 'solar-outline-CompassBig';
    case OutlineMapPointSchool = 'solar-outline-MapPointSchool';
    case OutlineSignpost = 'solar-outline-Signpost';
    case OutlineMapArrowDown = 'solar-outline-MapArrowDown';
    case OutlineMap = 'solar-outline-Map';
    case OutlineMapArrowUp = 'solar-outline-MapArrowUp';
    case OutlinePointOnMapPerspective = 'solar-outline-PointOnMapPerspective';
    case OutlineRadar = 'solar-outline-Radar';
    case OutlineStreets = 'solar-outline-Streets';
    case OutlineMapPointWave = 'solar-outline-MapPointWave';
    case OutlinePeopleNearby = 'solar-outline-PeopleNearby';
    case OutlineStreetsMapPoint = 'solar-outline-StreetsMapPoint';
    case OutlineMapPointSearch = 'solar-outline-MapPointSearch';
    case OutlineGPS = 'solar-outline-GPS';
    case OutlineMapArrowSquare = 'solar-outline-MapArrowSquare';
    case OutlineBranchingPathsDown = 'solar-outline-BranchingPathsDown';
    case OutlineMapPointRotate = 'solar-outline-MapPointRotate';
    case OutlineGlobal = 'solar-outline-Global';
    case OutlineCompassSquare = 'solar-outline-CompassSquare';
    case OutlineRouting3 = 'solar-outline-Routing3';
    case OutlineRouting2 = 'solar-outline-Routing2';
    case OutlineMapPointRemove = 'solar-outline-MapPointRemove';
    case OutlineGlobus = 'solar-outline-Globus';
    case OutlineSignpost2 = 'solar-outline-Signpost2';
    case OutlineRadar2 = 'solar-outline-Radar2';
    case OutlineStreetsNavigation = 'solar-outline-StreetsNavigation';
    case OutlineMapPoint = 'solar-outline-MapPoint';
    case OutlineMapPointHospital = 'solar-outline-MapPointHospital';
    case OutlineCompass = 'solar-outline-Compass';
    case OutlineMapPointAdd = 'solar-outline-MapPointAdd';
    case OutlineBranchingPathsUp = 'solar-outline-BranchingPathsUp';
    case OutlineMapPointFavourite = 'solar-outline-MapPointFavourite';
    case OutlineRoute = 'solar-outline-Route';
    case OutlinePointOnMap = 'solar-outline-PointOnMap';
    case OutlineMapArrowRight = 'solar-outline-MapArrowRight';
    case OutlineRouting = 'solar-outline-Routing';
    case OutlineMapArrowLeft = 'solar-outline-MapArrowLeft';
    case OutlineIncognito = 'solar-outline-Incognito';
    case OutlineLockPassword = 'solar-outline-LockPassword';
    case OutlineShieldNetwork = 'solar-outline-ShieldNetwork';
    case OutlineKeyMinimalisticSquare = 'solar-outline-KeyMinimalisticSquare';
    case OutlineLockKeyholeUnlocked = 'solar-outline-LockKeyholeUnlocked';
    case OutlineLock = 'solar-outline-Lock';
    case OutlineShieldKeyhole = 'solar-outline-ShieldKeyhole';
    case OutlineEyeClosed = 'solar-outline-EyeClosed';
    case OutlineKey = 'solar-outline-Key';
    case OutlineShieldMinus = 'solar-outline-ShieldMinus';
    case OutlineShield = 'solar-outline-Shield';
    case OutlineLockUnlocked = 'solar-outline-LockUnlocked';
    case OutlineBombMinimalistic = 'solar-outline-BombMinimalistic';
    case OutlineShieldStar = 'solar-outline-ShieldStar';
    case OutlineBomb = 'solar-outline-Bomb';
    case OutlineKeySquare = 'solar-outline-KeySquare';
    case OutlineLockKeyholeMinimalisticUnlocked = 'solar-outline-LockKeyholeMinimalisticUnlocked';
    case OutlineShieldCross = 'solar-outline-ShieldCross';
    case OutlineObjectScan = 'solar-outline-ObjectScan';
    case OutlinePasswordMinimalisticInput = 'solar-outline-PasswordMinimalisticInput';
    case OutlineLockPasswordUnlocked = 'solar-outline-LockPasswordUnlocked';
    case OutlineSiren = 'solar-outline-Siren';
    case OutlineShieldMinimalistic = 'solar-outline-ShieldMinimalistic';
    case OutlineEyeScan = 'solar-outline-EyeScan';
    case OutlineKeyMinimalisticSquare2 = 'solar-outline-KeyMinimalisticSquare2';
    case OutlineScanner2 = 'solar-outline-Scanner2';
    case OutlineKeyMinimalisticSquare3 = 'solar-outline-KeyMinimalisticSquare3';
    case OutlineKeyMinimalistic2 = 'solar-outline-KeyMinimalistic2';
    case OutlineCodeScan = 'solar-outline-CodeScan';
    case OutlineShieldPlus = 'solar-outline-ShieldPlus';
    case OutlinePasswordMinimalistic = 'solar-outline-PasswordMinimalistic';
    case OutlineEye = 'solar-outline-Eye';
    case OutlineQrCode = 'solar-outline-QrCode';
    case OutlineShieldCheck = 'solar-outline-ShieldCheck';
    case OutlineKeyMinimalistic = 'solar-outline-KeyMinimalistic';
    case OutlineLockKeyhole = 'solar-outline-LockKeyhole';
    case OutlineShieldUser = 'solar-outline-ShieldUser';
    case OutlineKeySquare2 = 'solar-outline-KeySquare2';
    case OutlineBombEmoji = 'solar-outline-BombEmoji';
    case OutlineScanner = 'solar-outline-Scanner';
    case OutlineShieldUp = 'solar-outline-ShieldUp';
    case OutlineSirenRounded = 'solar-outline-SirenRounded';
    case OutlineLockKeyholeMinimalistic = 'solar-outline-LockKeyholeMinimalistic';
    case OutlinePassword = 'solar-outline-Password';
    case OutlineShieldKeyholeMinimalistic = 'solar-outline-ShieldKeyholeMinimalistic';
    case OutlineShieldWarning = 'solar-outline-ShieldWarning';
    case OutlinePallete2 = 'solar-outline-Pallete2';
    case OutlineAlignVerticalSpacing = 'solar-outline-AlignVerticalSpacing';
    case OutlineAlignVerticalCenter = 'solar-outline-AlignVerticalCenter';
    case OutlineCropMinimalistic = 'solar-outline-CropMinimalistic';
    case OutlineMirrorRight = 'solar-outline-MirrorRight';
    case OutlineAlignBottom = 'solar-outline-AlignBottom';
    case OutlineRadialBlur = 'solar-outline-RadialBlur';
    case OutlineCrop = 'solar-outline-Crop';
    case OutlineAlignHorizontaSpacing = 'solar-outline-AlignHorizontaSpacing';
    case OutlineRulerPen = 'solar-outline-RulerPen';
    case OutlineThreeSquares = 'solar-outline-ThreeSquares';
    case OutlinePaintRoller = 'solar-outline-PaintRoller';
    case OutlineLayers = 'solar-outline-Layers';
    case OutlineFilters = 'solar-outline-Filters';
    case OutlineRulerCrossPen = 'solar-outline-RulerCrossPen';
    case OutlineFlipHorizontal = 'solar-outline-FlipHorizontal';
    case OutlineAlignLeft = 'solar-outline-AlignLeft';
    case OutlineRuler = 'solar-outline-Ruler';
    case OutlinePalette = 'solar-outline-Palette';
    case OutlineAlignTop = 'solar-outline-AlignTop';
    case OutlineAlignHorizontalCenter = 'solar-outline-AlignHorizontalCenter';
    case OutlineAlignRight = 'solar-outline-AlignRight';
    case OutlineRulerAngular = 'solar-outline-RulerAngular';
    case OutlinePipette = 'solar-outline-Pipette';
    case OutlineFlipVertical = 'solar-outline-FlipVertical';
    case OutlineMirrorLeft = 'solar-outline-MirrorLeft';
    case OutlineLayersMinimalistic = 'solar-outline-LayersMinimalistic';
    case OutlineColourTuneing = 'solar-outline-ColourTuneing';
    case OutlinePaletteRound = 'solar-outline-PaletteRound';
    case OutlineEraser = 'solar-outline-Eraser';
    case OutlineTextItalicCircle = 'solar-outline-TextItalicCircle';
    case OutlineLinkRound = 'solar-outline-LinkRound';
    case OutlineTextItalic = 'solar-outline-TextItalic';
    case OutlineLinkBrokenMinimalistic = 'solar-outline-LinkBrokenMinimalistic';
    case OutlineTextUnderlineCross = 'solar-outline-TextUnderlineCross';
    case OutlineLink = 'solar-outline-Link';
    case OutlineEraserCircle = 'solar-outline-EraserCircle';
    case OutlineLinkCircle = 'solar-outline-LinkCircle';
    case OutlineTextBoldCircle = 'solar-outline-TextBoldCircle';
    case OutlineTextField = 'solar-outline-TextField';
    case OutlineTextSquare = 'solar-outline-TextSquare';
    case OutlineTextSquare2 = 'solar-outline-TextSquare2';
    case OutlineLinkRoundAngle = 'solar-outline-LinkRoundAngle';
    case OutlineTextUnderlineCircle = 'solar-outline-TextUnderlineCircle';
    case OutlineTextCrossCircle = 'solar-outline-TextCrossCircle';
    case OutlineTextItalicSquare = 'solar-outline-TextItalicSquare';
    case OutlineParagraphSpacing = 'solar-outline-ParagraphSpacing';
    case OutlineText = 'solar-outline-Text';
    case OutlineLinkBroken = 'solar-outline-LinkBroken';
    case OutlineTextCross = 'solar-outline-TextCross';
    case OutlineTextUnderline = 'solar-outline-TextUnderline';
    case OutlineLinkMinimalistic = 'solar-outline-LinkMinimalistic';
    case OutlineLinkMinimalistic2 = 'solar-outline-LinkMinimalistic2';
    case OutlineTextBold = 'solar-outline-TextBold';
    case OutlineTextSelection = 'solar-outline-TextSelection';
    case OutlineTextFieldFocus = 'solar-outline-TextFieldFocus';
    case OutlineTextBoldSquare = 'solar-outline-TextBoldSquare';
    case OutlineEraserSquare = 'solar-outline-EraserSquare';
    case OutlineLinkSquare = 'solar-outline-LinkSquare';
    case OutlineTextCircle = 'solar-outline-TextCircle';
    case OutlineBackspace = 'solar-outline-Backspace';
    case OutlineTextCrossSquare = 'solar-outline-TextCrossSquare';
    case OutlineInboxUnread = 'solar-outline-InboxUnread';
    case OutlineChatUnread = 'solar-outline-ChatUnread';
    case OutlineChatRound = 'solar-outline-ChatRound';
    case OutlineUnread = 'solar-outline-Unread';
    case OutlineMailbox = 'solar-outline-Mailbox';
    case OutlineLetter = 'solar-outline-Letter';
    case OutlinePenNewRound = 'solar-outline-PenNewRound';
    case OutlineMultipleForwardRight = 'solar-outline-MultipleForwardRight';
    case OutlineMultipleForwardLeft = 'solar-outline-MultipleForwardLeft';
    case OutlineInboxArchive = 'solar-outline-InboxArchive';
    case OutlineInbox = 'solar-outline-Inbox';
    case OutlinePen2 = 'solar-outline-Pen2';
    case OutlinePenNewSquare = 'solar-outline-PenNewSquare';
    case OutlinePen = 'solar-outline-Pen';
    case OutlineChatDots = 'solar-outline-ChatDots';
    case OutlineChatSquareCall = 'solar-outline-ChatSquareCall';
    case OutlineSquareShareLine = 'solar-outline-SquareShareLine';
    case OutlineChatRoundCheck = 'solar-outline-ChatRoundCheck';
    case OutlineInboxOut = 'solar-outline-InboxOut';
    case OutlinePlain3 = 'solar-outline-Plain3';
    case OutlineChatRoundDots = 'solar-outline-ChatRoundDots';
    case OutlineChatRoundLike = 'solar-outline-ChatRoundLike';
    case OutlinePlain2 = 'solar-outline-Plain2';
    case OutlineChatRoundUnread = 'solar-outline-ChatRoundUnread';
    case OutlineChatSquareLike = 'solar-outline-ChatSquareLike';
    case OutlinePaperclip = 'solar-outline-Paperclip';
    case OutlineChatSquareCheck = 'solar-outline-ChatSquareCheck';
    case OutlineChatSquare = 'solar-outline-ChatSquare';
    case OutlineLetterOpened = 'solar-outline-LetterOpened';
    case OutlineSquareForward = 'solar-outline-SquareForward';
    case OutlineLetterUnread = 'solar-outline-LetterUnread';
    case OutlinePaperclipRounded2 = 'solar-outline-PaperclipRounded2';
    case OutlineChatRoundCall = 'solar-outline-ChatRoundCall';
    case OutlineInboxLine = 'solar-outline-InboxLine';
    case OutlineChatRoundVideo = 'solar-outline-ChatRoundVideo';
    case OutlineChatRoundMoney = 'solar-outline-ChatRoundMoney';
    case OutlineInboxIn = 'solar-outline-InboxIn';
    case OutlineCheckRead = 'solar-outline-CheckRead';
    case OutlineChatRoundLine = 'solar-outline-ChatRoundLine';
    case OutlineForward = 'solar-outline-Forward';
    case OutlinePaperclip2 = 'solar-outline-Paperclip2';
    case OutlineDialog2 = 'solar-outline-Dialog2';
    case OutlineDialog = 'solar-outline-Dialog';
    case OutlinePaperclipRounded = 'solar-outline-PaperclipRounded';
    case OutlinePlain = 'solar-outline-Plain';
    case OutlineChatSquareArrow = 'solar-outline-ChatSquareArrow';
    case OutlineChatSquareCode = 'solar-outline-ChatSquareCode';
    case OutlineChatLine = 'solar-outline-ChatLine';
    case OutlineTennis = 'solar-outline-Tennis';
    case OutlineBicyclingRound = 'solar-outline-BicyclingRound';
    case OutlineBalls = 'solar-outline-Balls';
    case OutlineMeditationRound = 'solar-outline-MeditationRound';
    case OutlineStretchingRound = 'solar-outline-StretchingRound';
    case OutlineDumbbells2 = 'solar-outline-Dumbbells2';
    case OutlineMeditation = 'solar-outline-Meditation';
    case OutlineRunning2 = 'solar-outline-Running2';
    case OutlineRugby = 'solar-outline-Rugby';
    case OutlineBodyShapeMinimalistic = 'solar-outline-BodyShapeMinimalistic';
    case OutlineStretching = 'solar-outline-Stretching';
    case OutlineBowling = 'solar-outline-Bowling';
    case OutlineRanking = 'solar-outline-Ranking';
    case OutlineTreadmillRound = 'solar-outline-TreadmillRound';
    case OutlineVolleyball = 'solar-outline-Volleyball';
    case OutlineDumbbellLargeMinimalistic = 'solar-outline-DumbbellLargeMinimalistic';
    case OutlineRunningRound = 'solar-outline-RunningRound';
    case OutlineHiking = 'solar-outline-Hiking';
    case OutlineHikingMinimalistic = 'solar-outline-HikingMinimalistic';
    case OutlineWaterSun = 'solar-outline-WaterSun';
    case OutlineGolf = 'solar-outline-Golf';
    case OutlineSkateboarding = 'solar-outline-Skateboarding';
    case OutlineDumbbells = 'solar-outline-Dumbbells';
    case OutlineWalkingRound = 'solar-outline-WalkingRound';
    case OutlineRunning = 'solar-outline-Running';
    case OutlineTreadmill = 'solar-outline-Treadmill';
    case OutlineSkateboard = 'solar-outline-Skateboard';
    case OutlineDumbbellSmall = 'solar-outline-DumbbellSmall';
    case OutlineBasketball = 'solar-outline-Basketball';
    case OutlineFootball = 'solar-outline-Football';
    case OutlineDumbbell = 'solar-outline-Dumbbell';
    case OutlineBodyShape = 'solar-outline-BodyShape';
    case OutlineWater = 'solar-outline-Water';
    case OutlineSkateboardingRound = 'solar-outline-SkateboardingRound';
    case OutlineHikingRound = 'solar-outline-HikingRound';
    case OutlineVolleyball2 = 'solar-outline-Volleyball2';
    case OutlineTennis2 = 'solar-outline-Tennis2';
    case OutlineSwimming = 'solar-outline-Swimming';
    case OutlineBicycling = 'solar-outline-Bicycling';
    case OutlineWalking = 'solar-outline-Walking';
    case OutlineDumbbellLarge = 'solar-outline-DumbbellLarge';
    case OutlineCalendarMark = 'solar-outline-CalendarMark';
    case OutlineHistory2 = 'solar-outline-History2';
    case OutlineWatchSquareMinimalisticCharge = 'solar-outline-WatchSquareMinimalisticCharge';
    case OutlineHistory3 = 'solar-outline-History3';
    case OutlineHourglass = 'solar-outline-Hourglass';
    case OutlineCalendarSearch = 'solar-outline-CalendarSearch';
    case OutlineStopwatchPlay = 'solar-outline-StopwatchPlay';
    case OutlineWatchRound = 'solar-outline-WatchRound';
    case OutlineCalendarAdd = 'solar-outline-CalendarAdd';
    case OutlineCalendarDate = 'solar-outline-CalendarDate';
    case OutlineStopwatch = 'solar-outline-Stopwatch';
    case OutlineAlarmPause = 'solar-outline-AlarmPause';
    case OutlineAlarmTurnOff = 'solar-outline-AlarmTurnOff';
    case OutlineClockSquare = 'solar-outline-ClockSquare';
    case OutlineStopwatchPause = 'solar-outline-StopwatchPause';
    case OutlineCalendarMinimalistic = 'solar-outline-CalendarMinimalistic';
    case OutlineAlarmAdd = 'solar-outline-AlarmAdd';
    case OutlineAlarmPlay = 'solar-outline-AlarmPlay';
    case OutlineHourglassLine = 'solar-outline-HourglassLine';
    case OutlineAlarmSleep = 'solar-outline-AlarmSleep';
    case OutlineAlarmRemove = 'solar-outline-AlarmRemove';
    case OutlineCalendar = 'solar-outline-Calendar';
    case OutlineClockCircle = 'solar-outline-ClockCircle';
    case OutlineHistory = 'solar-outline-History';
    case OutlineAlarm = 'solar-outline-Alarm';
    case OutlineWatchSquare = 'solar-outline-WatchSquare';
    case OutlineWatchSquareMinimalistic = 'solar-outline-WatchSquareMinimalistic';
    case OutlineMagniferBug = 'solar-outline-MagniferBug';
    case OutlineMagnifer = 'solar-outline-Magnifer';
    case OutlineMagniferZoomIn = 'solar-outline-MagniferZoomIn';
    case OutlineRoundedMagnifer = 'solar-outline-RoundedMagnifer';
    case OutlineRoundedMagniferZoomIn = 'solar-outline-RoundedMagniferZoomIn';
    case OutlineMinimalisticMagniferBug = 'solar-outline-MinimalisticMagniferBug';
    case OutlineRoundedMagniferBug = 'solar-outline-RoundedMagniferBug';
    case OutlineMinimalisticMagniferZoomOut = 'solar-outline-MinimalisticMagniferZoomOut';
    case OutlineMinimalisticMagnifer = 'solar-outline-MinimalisticMagnifer';
    case OutlineRoundedMagniferZoomOut = 'solar-outline-RoundedMagniferZoomOut';
    case OutlineMinimalisticMagniferZoomIn = 'solar-outline-MinimalisticMagniferZoomIn';
    case OutlineMagniferZoomOut = 'solar-outline-MagniferZoomOut';
    case OutlineBagCheck = 'solar-outline-BagCheck';
    case OutlineShopMinimalistic = 'solar-outline-ShopMinimalistic';
    case OutlineShop = 'solar-outline-Shop';
    case OutlineCartCheck = 'solar-outline-CartCheck';
    case OutlineCart = 'solar-outline-Cart';
    case OutlineCart3 = 'solar-outline-Cart3';
    case OutlineCart2 = 'solar-outline-Cart2';
    case OutlineBagMusic = 'solar-outline-BagMusic';
    case OutlineCartLargeMinimalistic = 'solar-outline-CartLargeMinimalistic';
    case OutlineCart5 = 'solar-outline-Cart5';
    case OutlineCart4 = 'solar-outline-Cart4';
    case OutlineBag = 'solar-outline-Bag';
    case OutlineBagHeart = 'solar-outline-BagHeart';
    case OutlineCartPlus = 'solar-outline-CartPlus';
    case OutlineCartLarge = 'solar-outline-CartLarge';
    case OutlineBagCross = 'solar-outline-BagCross';
    case OutlineBagMusic2 = 'solar-outline-BagMusic2';
    case OutlineBag5 = 'solar-outline-Bag5';
    case OutlineBag4 = 'solar-outline-Bag4';
    case OutlineCartLarge4 = 'solar-outline-CartLarge4';
    case OutlineCartLarge3 = 'solar-outline-CartLarge3';
    case OutlineBag3 = 'solar-outline-Bag3';
    case OutlineBag2 = 'solar-outline-Bag2';
    case OutlineShop2 = 'solar-outline-Shop2';
    case OutlineCartLarge2 = 'solar-outline-CartLarge2';
    case OutlineBagSmile = 'solar-outline-BagSmile';
    case OutlineCartCross = 'solar-outline-CartCross';
    case OutlineInfoSquare = 'solar-outline-InfoSquare';
    case OutlineFlashlightOn = 'solar-outline-FlashlightOn';
    case OutlineXXX = 'solar-outline-XXX';
    case OutlineFigma = 'solar-outline-Figma';
    case OutlineFlashlight = 'solar-outline-Flashlight';
    case OutlineGhost = 'solar-outline-Ghost';
    case OutlineCupMusic = 'solar-outline-CupMusic';
    case OutlineBatteryFullMinimalistic = 'solar-outline-BatteryFullMinimalistic';
    case OutlineDangerCircle = 'solar-outline-DangerCircle';
    case OutlineCheckSquare = 'solar-outline-CheckSquare';
    case OutlineGhostSmile = 'solar-outline-GhostSmile';
    case OutlineTarget = 'solar-outline-Target';
    case OutlineBatteryHalfMinimalistic = 'solar-outline-BatteryHalfMinimalistic';
    case OutlineScissors = 'solar-outline-Scissors';
    case OutlinePinList = 'solar-outline-PinList';
    case OutlineBatteryCharge = 'solar-outline-BatteryCharge';
    case OutlineUmbrella = 'solar-outline-Umbrella';
    case OutlineHomeSmile = 'solar-outline-HomeSmile';
    case OutlineHome = 'solar-outline-Home';
    case OutlineCopyright = 'solar-outline-Copyright';
    case OutlineHomeWifi = 'solar-outline-HomeWifi';
    case OutlineTShirt = 'solar-outline-TShirt';
    case OutlineBatteryChargeMinimalistic = 'solar-outline-BatteryChargeMinimalistic';
    case OutlineCupStar = 'solar-outline-CupStar';
    case OutlineSpecialEffects = 'solar-outline-SpecialEffects';
    case OutlineBody = 'solar-outline-Body';
    case OutlineHamburgerMenu = 'solar-outline-HamburgerMenu';
    case OutlinePower = 'solar-outline-Power';
    case OutlineDatabase = 'solar-outline-Database';
    case OutlineCursorSquare = 'solar-outline-CursorSquare';
    case OutlineFuel = 'solar-outline-Fuel';
    case OutlineMentionCircle = 'solar-outline-MentionCircle';
    case OutlineConfettiMinimalistic = 'solar-outline-ConfettiMinimalistic';
    case OutlineMenuDotsCircle = 'solar-outline-MenuDotsCircle';
    case OutlinePaw = 'solar-outline-Paw';
    case OutlineSubtitles = 'solar-outline-Subtitles';
    case OutlineSliderVerticalMinimalistic = 'solar-outline-SliderVerticalMinimalistic';
    case OutlineCrownMinimalistic = 'solar-outline-CrownMinimalistic';
    case OutlineMenuDots = 'solar-outline-MenuDots';
    case OutlineDelivery = 'solar-outline-Delivery';
    case OutlineWaterdrop = 'solar-outline-Waterdrop';
    case OutlinePerfume = 'solar-outline-Perfume';
    case OutlineHomeAngle2 = 'solar-outline-HomeAngle2';
    case OutlineHomeWifiAngle = 'solar-outline-HomeWifiAngle';
    case OutlineQuestionCircle = 'solar-outline-QuestionCircle';
    case OutlineTrashBinMinimalistic = 'solar-outline-TrashBinMinimalistic';
    case OutlineMagicStick3 = 'solar-outline-MagicStick3';
    case OutlineAddSquare = 'solar-outline-AddSquare';
    case OutlineCrownStar = 'solar-outline-CrownStar';
    case OutlineMagnet = 'solar-outline-Magnet';
    case OutlineConfetti = 'solar-outline-Confetti';
    case OutlinePin = 'solar-outline-Pin';
    case OutlineMinusSquare = 'solar-outline-MinusSquare';
    case OutlineBolt = 'solar-outline-Bolt';
    case OutlineCloseCircle = 'solar-outline-CloseCircle';
    case OutlineForbiddenCircle = 'solar-outline-ForbiddenCircle';
    case OutlineMagicStick2 = 'solar-outline-MagicStick2';
    case OutlineCrownLine = 'solar-outline-CrownLine';
    case OutlineBoltCircle = 'solar-outline-BoltCircle';
    case OutlineFlag = 'solar-outline-Flag';
    case OutlineSliderHorizontal = 'solar-outline-SliderHorizontal';
    case OutlineHighDefinition = 'solar-outline-HighDefinition';
    case OutlineCursor = 'solar-outline-Cursor';
    case OutlineFeed = 'solar-outline-Feed';
    case OutlineTrafficEconomy = 'solar-outline-TrafficEconomy';
    case OutlineAugmentedReality = 'solar-outline-AugmentedReality';
    case OutlineIcon4K = 'solar-outline-Icon4K';
    case OutlineMagnetWave = 'solar-outline-MagnetWave';
    case OutlineHomeSmileAngle = 'solar-outline-HomeSmileAngle';
    case OutlineSliderVertical = 'solar-outline-SliderVertical';
    case OutlineCheckCircle = 'solar-outline-CheckCircle';
    case OutlineCopy = 'solar-outline-Copy';
    case OutlineDangerSquare = 'solar-outline-DangerSquare';
    case OutlineSkirt = 'solar-outline-Skirt';
    case OutlineGlasses = 'solar-outline-Glasses';
    case OutlineHomeAdd = 'solar-outline-HomeAdd';
    case OutlineSledgehammer = 'solar-outline-Sledgehammer';
    case OutlineInfoCircle = 'solar-outline-InfoCircle';
    case OutlineDangerTriangle = 'solar-outline-DangerTriangle';
    case OutlinePinCircle = 'solar-outline-PinCircle';
    case OutlineSmartHome = 'solar-outline-SmartHome';
    case OutlineScissorsSquare = 'solar-outline-ScissorsSquare';
    case OutlineSleeping = 'solar-outline-Sleeping';
    case OutlineBox = 'solar-outline-Box';
    case OutlineCrown = 'solar-outline-Crown';
    case OutlineBroom = 'solar-outline-Broom';
    case OutlinePostsCarouselHorizontal = 'solar-outline-PostsCarouselHorizontal';
    case OutlineFlag2 = 'solar-outline-Flag2';
    case OutlinePlate = 'solar-outline-Plate';
    case OutlineTrashBinTrash = 'solar-outline-TrashBinTrash';
    case OutlineCupFirst = 'solar-outline-CupFirst';
    case OutlineSmartHomeAngle = 'solar-outline-SmartHomeAngle';
    case OutlinePaperBin = 'solar-outline-PaperBin';
    case OutlineBoxMinimalistic = 'solar-outline-BoxMinimalistic';
    case OutlineDanger = 'solar-outline-Danger';
    case OutlineMenuDotsSquare = 'solar-outline-MenuDotsSquare';
    case OutlineHanger2 = 'solar-outline-Hanger2';
    case OutlineBatteryHalf = 'solar-outline-BatteryHalf';
    case OutlineHome2 = 'solar-outline-Home2';
    case OutlinePostsCarouselVertical = 'solar-outline-PostsCarouselVertical';
    case OutlineRevote = 'solar-outline-Revote';
    case OutlineMentionSquare = 'solar-outline-MentionSquare';
    case OutlineWinRar = 'solar-outline-WinRar';
    case OutlineForbidden = 'solar-outline-Forbidden';
    case OutlineQuestionSquare = 'solar-outline-QuestionSquare';
    case OutlineHanger = 'solar-outline-Hanger';
    case OutlineReorder = 'solar-outline-Reorder';
    case OutlineHomeAddAngle = 'solar-outline-HomeAddAngle';
    case OutlineMasks = 'solar-outline-Masks';
    case OutlineGift = 'solar-outline-Gift';
    case OutlineCreativeCommons = 'solar-outline-CreativeCommons';
    case OutlineSliderMinimalisticHorizontal = 'solar-outline-SliderMinimalisticHorizontal';
    case OutlineHomeAngle = 'solar-outline-HomeAngle';
    case OutlineBatteryLowMinimalistic = 'solar-outline-BatteryLowMinimalistic';
    case OutlineShare = 'solar-outline-Share';
    case OutlineTrashBin2 = 'solar-outline-TrashBin2';
    case OutlineSort = 'solar-outline-Sort';
    case OutlineMinusCircle = 'solar-outline-MinusCircle';
    case OutlineExplicit = 'solar-outline-Explicit';
    case OutlineTraffic = 'solar-outline-Traffic';
    case OutlineFilter = 'solar-outline-Filter';
    case OutlineCloseSquare = 'solar-outline-CloseSquare';
    case OutlineAddCircle = 'solar-outline-AddCircle';
    case OutlineFerrisWheel = 'solar-outline-FerrisWheel';
    case OutlineCup = 'solar-outline-Cup';
    case OutlineBalloon = 'solar-outline-Balloon';
    case OutlineHelp = 'solar-outline-Help';
    case OutlineBatteryFull = 'solar-outline-BatteryFull';
    case OutlineCat = 'solar-outline-Cat';
    case OutlineMaskSad = 'solar-outline-MaskSad';
    case OutlineHighQuality = 'solar-outline-HighQuality';
    case OutlineMagicStick = 'solar-outline-MagicStick';
    case OutlineCosmetic = 'solar-outline-Cosmetic';
    case OutlineBatteryLow = 'solar-outline-BatteryLow';
    case OutlineShareCircle = 'solar-outline-ShareCircle';
    case OutlineMaskHapply = 'solar-outline-MaskHapply';
    case OutlineAccessibility = 'solar-outline-Accessibility';
    case OutlineTrashBinMinimalistic2 = 'solar-outline-TrashBinMinimalistic2';
    case OutlineIncomingCallRounded = 'solar-outline-IncomingCallRounded';
    case OutlineCallDropped = 'solar-outline-CallDropped';
    case OutlineCallChat = 'solar-outline-CallChat';
    case OutlineCallCancelRounded = 'solar-outline-CallCancelRounded';
    case OutlineCallMedicineRounded = 'solar-outline-CallMedicineRounded';
    case OutlineCallDroppedRounded = 'solar-outline-CallDroppedRounded';
    case OutlineRecordSquare = 'solar-outline-RecordSquare';
    case OutlinePhoneCalling = 'solar-outline-PhoneCalling';
    case OutlinePhoneRounded = 'solar-outline-PhoneRounded';
    case OutlineCallMedicine = 'solar-outline-CallMedicine';
    case OutlineRecordMinimalistic = 'solar-outline-RecordMinimalistic';
    case OutlineEndCall = 'solar-outline-EndCall';
    case OutlineOutgoingCall = 'solar-outline-OutgoingCall';
    case OutlineRecordCircle = 'solar-outline-RecordCircle';
    case OutlineIncomingCall = 'solar-outline-IncomingCall';
    case OutlineCallChatRounded = 'solar-outline-CallChatRounded';
    case OutlineEndCallRounded = 'solar-outline-EndCallRounded';
    case OutlinePhone = 'solar-outline-Phone';
    case OutlineOutgoingCallRounded = 'solar-outline-OutgoingCallRounded';
    case OutlineCallCancel = 'solar-outline-CallCancel';
    case OutlinePhoneCallingRounded = 'solar-outline-PhoneCallingRounded';
    case OutlineStationMinimalistic = 'solar-outline-StationMinimalistic';
    case OutlineSidebarCode = 'solar-outline-SidebarCode';
    case OutlineWiFiRouterMinimalistic = 'solar-outline-WiFiRouterMinimalistic';
    case OutlineUSB = 'solar-outline-USB';
    case OutlineSiderbar = 'solar-outline-Siderbar';
    case OutlineCode2 = 'solar-outline-Code2';
    case OutlineSlashCircle = 'solar-outline-SlashCircle';
    case OutlineScreencast = 'solar-outline-Screencast';
    case OutlineHashtagSquare = 'solar-outline-HashtagSquare';
    case OutlineSidebarMinimalistic = 'solar-outline-SidebarMinimalistic';
    case OutlineCode = 'solar-outline-Code';
    case OutlineUsbSquare = 'solar-outline-UsbSquare';
    case OutlineWiFiRouter = 'solar-outline-WiFiRouter';
    case OutlineCodeCircle = 'solar-outline-CodeCircle';
    case OutlineTranslation = 'solar-outline-Translation';
    case OutlineBugMinimalistic = 'solar-outline-BugMinimalistic';
    case OutlineStation = 'solar-outline-Station';
    case OutlineProgramming = 'solar-outline-Programming';
    case OutlineWiFiRouterRound = 'solar-outline-WiFiRouterRound';
    case OutlineHashtag = 'solar-outline-Hashtag';
    case OutlineBug = 'solar-outline-Bug';
    case OutlineHashtagChat = 'solar-outline-HashtagChat';
    case OutlineCommand = 'solar-outline-Command';
    case OutlineTranslation2 = 'solar-outline-Translation2';
    case OutlineHashtagCircle = 'solar-outline-HashtagCircle';
    case OutlineScreencast2 = 'solar-outline-Screencast2';
    case OutlineSlashSquare = 'solar-outline-SlashSquare';
    case OutlineWindowFrame = 'solar-outline-WindowFrame';
    case OutlineStructure = 'solar-outline-Structure';
    case OutlineUsbCircle = 'solar-outline-UsbCircle';
    case OutlineCodeSquare = 'solar-outline-CodeSquare';
    case OutlineNotes = 'solar-outline-Notes';
    case OutlineDocumentText = 'solar-outline-DocumentText';
    case OutlineDocumentAdd = 'solar-outline-DocumentAdd';
    case OutlineDocumentMedicine = 'solar-outline-DocumentMedicine';
    case OutlineArchiveMinimalistic = 'solar-outline-ArchiveMinimalistic';
    case OutlineClipboard = 'solar-outline-Clipboard';
    case OutlineClipboardAdd = 'solar-outline-ClipboardAdd';
    case OutlineArchive = 'solar-outline-Archive';
    case OutlineClipboardHeart = 'solar-outline-ClipboardHeart';
    case OutlineClipboardRemove = 'solar-outline-ClipboardRemove';
    case OutlineClipboardText = 'solar-outline-ClipboardText';
    case OutlineDocument = 'solar-outline-Document';
    case OutlineNotesMinimalistic = 'solar-outline-NotesMinimalistic';
    case OutlineArchiveUp = 'solar-outline-ArchiveUp';
    case OutlineArchiveUpMinimlistic = 'solar-outline-ArchiveUpMinimlistic';
    case OutlineArchiveCheck = 'solar-outline-ArchiveCheck';
    case OutlineArchiveDown = 'solar-outline-ArchiveDown';
    case OutlineArchiveDownMinimlistic = 'solar-outline-ArchiveDownMinimlistic';
    case OutlineDocumentsMinimalistic = 'solar-outline-DocumentsMinimalistic';
    case OutlineClipboardCheck = 'solar-outline-ClipboardCheck';
    case OutlineClipboardList = 'solar-outline-ClipboardList';
    case OutlineDocuments = 'solar-outline-Documents';
    case OutlineNotebook = 'solar-outline-Notebook';
    case OutlineGalleryRound = 'solar-outline-GalleryRound';
    case OutlinePlayCircle = 'solar-outline-PlayCircle';
    case OutlineStream = 'solar-outline-Stream';
    case OutlineGalleryRemove = 'solar-outline-GalleryRemove';
    case OutlineClapperboard = 'solar-outline-Clapperboard';
    case OutlinePauseCircle = 'solar-outline-PauseCircle';
    case OutlineRewind5SecondsBack = 'solar-outline-Rewind5SecondsBack';
    case OutlineRepeat = 'solar-outline-Repeat';
    case OutlineClapperboardEdit = 'solar-outline-ClapperboardEdit';
    case OutlineVideoFrameCut = 'solar-outline-VideoFrameCut';
    case OutlinePanorama = 'solar-outline-Panorama';
    case OutlinePlayStream = 'solar-outline-PlayStream';
    case OutlineClapperboardOpen = 'solar-outline-ClapperboardOpen';
    case OutlineClapperboardText = 'solar-outline-ClapperboardText';
    case OutlineLibrary = 'solar-outline-Library';
    case OutlineReel2 = 'solar-outline-Reel2';
    case OutlineVolumeSmall = 'solar-outline-VolumeSmall';
    case OutlineVideoFrame = 'solar-outline-VideoFrame';
    case OutlineMicrophoneLarge = 'solar-outline-MicrophoneLarge';
    case OutlineRewindForward = 'solar-outline-RewindForward';
    case OutlineRewindBackCircle = 'solar-outline-RewindBackCircle';
    case OutlineMicrophone = 'solar-outline-Microphone';
    case OutlineVideoFrameReplace = 'solar-outline-VideoFrameReplace';
    case OutlineClapperboardPlay = 'solar-outline-ClapperboardPlay';
    case OutlineGalleryDownload = 'solar-outline-GalleryDownload';
    case OutlineMusicNote4 = 'solar-outline-MusicNote4';
    case OutlineVideocameraRecord = 'solar-outline-VideocameraRecord';
    case OutlinePlaybackSpeed = 'solar-outline-PlaybackSpeed';
    case OutlineSoundwave = 'solar-outline-Soundwave';
    case OutlineStopCircle = 'solar-outline-StopCircle';
    case OutlineQuitFullScreenCircle = 'solar-outline-QuitFullScreenCircle';
    case OutlineRewindBack = 'solar-outline-RewindBack';
    case OutlineRepeatOne = 'solar-outline-RepeatOne';
    case OutlineGalleryCheck = 'solar-outline-GalleryCheck';
    case OutlineWallpaper = 'solar-outline-Wallpaper';
    case OutlineRewindForwardCircle = 'solar-outline-RewindForwardCircle';
    case OutlineGalleryEdit = 'solar-outline-GalleryEdit';
    case OutlineGallery = 'solar-outline-Gallery';
    case OutlineGalleryMinimalistic = 'solar-outline-GalleryMinimalistic';
    case OutlineUploadTrack = 'solar-outline-UploadTrack';
    case OutlineVolume = 'solar-outline-Volume';
    case OutlineUploadTrack2 = 'solar-outline-UploadTrack2';
    case OutlineMusicNotes = 'solar-outline-MusicNotes';
    case OutlineMusicNote2 = 'solar-outline-MusicNote2';
    case OutlineCameraAdd = 'solar-outline-CameraAdd';
    case OutlinePodcast = 'solar-outline-Podcast';
    case OutlineCameraRotate = 'solar-outline-CameraRotate';
    case OutlineMusicNote3 = 'solar-outline-MusicNote3';
    case OutlineStop = 'solar-outline-Stop';
    case OutlineMuted = 'solar-outline-Muted';
    case OutlineSkipNext = 'solar-outline-SkipNext';
    case OutlineGallerySend = 'solar-outline-GallerySend';
    case OutlineRecord = 'solar-outline-Record';
    case OutlineFullScreenCircle = 'solar-outline-FullScreenCircle';
    case OutlineVolumeCross = 'solar-outline-VolumeCross';
    case OutlineSoundwaveCircle = 'solar-outline-SoundwaveCircle';
    case OutlineSkipPrevious = 'solar-outline-SkipPrevious';
    case OutlineRewind5SecondsForward = 'solar-outline-Rewind5SecondsForward';
    case OutlinePlay = 'solar-outline-Play';
    case OutlinePIP = 'solar-outline-PIP';
    case OutlineMusicLibrary = 'solar-outline-MusicLibrary';
    case OutlineVideoFrame2 = 'solar-outline-VideoFrame2';
    case OutlineCamera = 'solar-outline-Camera';
    case OutlineQuitPip = 'solar-outline-QuitPip';
    case OutlineClapperboardOpenPlay = 'solar-outline-ClapperboardOpenPlay';
    case OutlineRewind10SecondsBack = 'solar-outline-Rewind10SecondsBack';
    case OutlineRepeatOneMinimalistic = 'solar-outline-RepeatOneMinimalistic';
    case OutlineVinyl = 'solar-outline-Vinyl';
    case OutlineVideoLibrary = 'solar-outline-VideoLibrary';
    case OutlineGalleryWide = 'solar-outline-GalleryWide';
    case OutlineReel = 'solar-outline-Reel';
    case OutlineToPip = 'solar-outline-ToPip';
    case OutlinePip2 = 'solar-outline-Pip2';
    case OutlineFullScreen = 'solar-outline-FullScreen';
    case OutlineCameraMinimalistic = 'solar-outline-CameraMinimalistic';
    case OutlineVideoFrameCut2 = 'solar-outline-VideoFrameCut2';
    case OutlineGalleryCircle = 'solar-outline-GalleryCircle';
    case OutlineVideoFramePlayHorizontal = 'solar-outline-VideoFramePlayHorizontal';
    case OutlineMusicNoteSlider2 = 'solar-outline-MusicNoteSlider2';
    case OutlineMusicNoteSlider = 'solar-outline-MusicNoteSlider';
    case OutlineVideocameraAdd = 'solar-outline-VideocameraAdd';
    case OutlineQuitFullScreenSquare = 'solar-outline-QuitFullScreenSquare';
    case OutlineAlbum = 'solar-outline-Album';
    case OutlineGalleryAdd = 'solar-outline-GalleryAdd';
    case OutlineCameraSquare = 'solar-outline-CameraSquare';
    case OutlineRewind15SecondsBack = 'solar-outline-Rewind15SecondsBack';
    case OutlineRewind15SecondsForward = 'solar-outline-Rewind15SecondsForward';
    case OutlineVinylRecord = 'solar-outline-VinylRecord';
    case OutlineShuffle = 'solar-outline-Shuffle';
    case OutlinePause = 'solar-outline-Pause';
    case OutlineMusicNote = 'solar-outline-MusicNote';
    case OutlineQuitFullScreen = 'solar-outline-QuitFullScreen';
    case OutlineMicrophone2 = 'solar-outline-Microphone2';
    case OutlineVideocamera = 'solar-outline-Videocamera';
    case OutlineGalleryFavourite = 'solar-outline-GalleryFavourite';
    case OutlineMusicLibrary2 = 'solar-outline-MusicLibrary2';
    case OutlineVideoFramePlayVertical = 'solar-outline-VideoFramePlayVertical';
    case OutlineFullScreenSquare = 'solar-outline-FullScreenSquare';
    case OutlineRewind10SecondsForward = 'solar-outline-Rewind10SecondsForward';
    case OutlineVolumeLoud = 'solar-outline-VolumeLoud';
    case OutlineMicrophone3 = 'solar-outline-Microphone3';
    case OutlineSoundwaveSquare = 'solar-outline-SoundwaveSquare';
    case OutlineCardholder = 'solar-outline-Cardholder';
    case OutlineBillList = 'solar-outline-BillList';
    case OutlineSaleSquare = 'solar-outline-SaleSquare';
    case OutlineDollar = 'solar-outline-Dollar';
    case OutlineTicket = 'solar-outline-Ticket';
    case OutlineTag = 'solar-outline-Tag';
    case OutlineCashOut = 'solar-outline-CashOut';
    case OutlineWallet2 = 'solar-outline-Wallet2';
    case OutlineRuble = 'solar-outline-Ruble';
    case OutlineCardTransfer = 'solar-outline-CardTransfer';
    case OutlineEuro = 'solar-outline-Euro';
    case OutlineSale = 'solar-outline-Sale';
    case OutlineCardSearch = 'solar-outline-CardSearch';
    case OutlineWallet = 'solar-outline-Wallet';
    case OutlineBillCross = 'solar-outline-BillCross';
    case OutlineTicketSale = 'solar-outline-TicketSale';
    case OutlineSafeSquare = 'solar-outline-SafeSquare';
    case OutlineCard = 'solar-outline-Card';
    case OutlineSafe2 = 'solar-outline-Safe2';
    case OutlineDollarMinimalistic = 'solar-outline-DollarMinimalistic';
    case OutlineTagPrice = 'solar-outline-TagPrice';
    case OutlineMoneyBag = 'solar-outline-MoneyBag';
    case OutlineBill = 'solar-outline-Bill';
    case OutlineCardSend = 'solar-outline-CardSend';
    case OutlineCardRecive = 'solar-outline-CardRecive';
    case OutlineBanknote2 = 'solar-outline-Banknote2';
    case OutlineTagHorizontal = 'solar-outline-TagHorizontal';
    case OutlineBillCheck = 'solar-outline-BillCheck';
    case OutlineTickerStar = 'solar-outline-TickerStar';
    case OutlineBanknote = 'solar-outline-Banknote';
    case OutlineVerifiedCheck = 'solar-outline-VerifiedCheck';
    case OutlineWadOfMoney = 'solar-outline-WadOfMoney';
    case OutlineCard2 = 'solar-outline-Card2';
    case OutlineSafeCircle = 'solar-outline-SafeCircle';
    case OutlineWalletMoney = 'solar-outline-WalletMoney';
    case OutlineList = 'solar-outline-List';
    case OutlineListDownMinimalistic = 'solar-outline-ListDownMinimalistic';
    case OutlinePlaylist2 = 'solar-outline-Playlist2';
    case OutlineChecklistMinimalistic = 'solar-outline-ChecklistMinimalistic';
    case OutlinePlaaylistMinimalistic = 'solar-outline-PlaaylistMinimalistic';
    case OutlineListHeart = 'solar-outline-ListHeart';
    case OutlineListArrowDown = 'solar-outline-ListArrowDown';
    case OutlineListArrowUp = 'solar-outline-ListArrowUp';
    case OutlineListUpMinimalistic = 'solar-outline-ListUpMinimalistic';
    case OutlinePlaylist = 'solar-outline-Playlist';
    case OutlineListUp = 'solar-outline-ListUp';
    case OutlineListCrossMinimalistic = 'solar-outline-ListCrossMinimalistic';
    case OutlineListCross = 'solar-outline-ListCross';
    case OutlineListArrowDownMinimalistic = 'solar-outline-ListArrowDownMinimalistic';
    case OutlineSortByAlphabet = 'solar-outline-SortByAlphabet';
    case OutlineChecklist = 'solar-outline-Checklist';
    case OutlineSortFromBottomToTop = 'solar-outline-SortFromBottomToTop';
    case OutlineListCheck = 'solar-outline-ListCheck';
    case OutlinePlaylistMinimalistic2 = 'solar-outline-PlaylistMinimalistic2';
    case OutlinePlaylistMinimalistic3 = 'solar-outline-PlaylistMinimalistic3';
    case OutlineList1 = 'solar-outline-List1';
    case OutlineSortFromTopToBottom = 'solar-outline-SortFromTopToBottom';
    case OutlineSortByTime = 'solar-outline-SortByTime';
    case OutlineListDown = 'solar-outline-ListDown';
    case OutlineListHeartMinimalistic = 'solar-outline-ListHeartMinimalistic';
    case OutlineListCheckMinimalistic = 'solar-outline-ListCheckMinimalistic';
    case OutlineListArrowUpMinimalistic = 'solar-outline-ListArrowUpMinimalistic';
    case OutlineUserCrossRounded = 'solar-outline-UserCrossRounded';
    case OutlineUser = 'solar-outline-User';
    case OutlineUsersGroupRounded = 'solar-outline-UsersGroupRounded';
    case OutlineUserPlusRounded = 'solar-outline-UserPlusRounded';
    case OutlineUserBlock = 'solar-outline-UserBlock';
    case OutlineUserMinus = 'solar-outline-UserMinus';
    case OutlineUserHands = 'solar-outline-UserHands';
    case OutlineUserHeart = 'solar-outline-UserHeart';
    case OutlineUserMinusRounded = 'solar-outline-UserMinusRounded';
    case OutlineUserCross = 'solar-outline-UserCross';
    case OutlineUserSpeakRounded = 'solar-outline-UserSpeakRounded';
    case OutlineUserId = 'solar-outline-UserId';
    case OutlineUserBlockRounded = 'solar-outline-UserBlockRounded';
    case OutlineUserHeartRounded = 'solar-outline-UserHeartRounded';
    case OutlineUsersGroupTwoRounded = 'solar-outline-UsersGroupTwoRounded';
    case OutlineUserHandUp = 'solar-outline-UserHandUp';
    case OutlineUserCircle = 'solar-outline-UserCircle';
    case OutlineUserRounded = 'solar-outline-UserRounded';
    case OutlineUserCheck = 'solar-outline-UserCheck';
    case OutlineUserPlus = 'solar-outline-UserPlus';
    case OutlineUserCheckRounded = 'solar-outline-UserCheckRounded';
    case OutlineUserSpeak = 'solar-outline-UserSpeak';
    case OutlineVirus = 'solar-outline-Virus';
    case OutlineAdhesivePlaster2 = 'solar-outline-AdhesivePlaster2';
    case OutlineDropper = 'solar-outline-Dropper';
    case OutlinePulse2 = 'solar-outline-Pulse2';
    case OutlineBoneBroken = 'solar-outline-BoneBroken';
    case OutlineHeartPulse2 = 'solar-outline-HeartPulse2';
    case OutlineMedicalKit = 'solar-outline-MedicalKit';
    case OutlineTestTube = 'solar-outline-TestTube';
    case OutlineHealth = 'solar-outline-Health';
    case OutlineDropperMinimalistic2 = 'solar-outline-DropperMinimalistic2';
    case OutlineDNA = 'solar-outline-DNA';
    case OutlineDropper3 = 'solar-outline-Dropper3';
    case OutlineThermometer = 'solar-outline-Thermometer';
    case OutlineDropper2 = 'solar-outline-Dropper2';
    case OutlineJarOfPills2 = 'solar-outline-JarOfPills2';
    case OutlineBoneCrack = 'solar-outline-BoneCrack';
    case OutlineJarOfPills = 'solar-outline-JarOfPills';
    case OutlineSyringe = 'solar-outline-Syringe';
    case OutlineStethoscope = 'solar-outline-Stethoscope';
    case OutlineBenzeneRing = 'solar-outline-BenzeneRing';
    case OutlineBacteria = 'solar-outline-Bacteria';
    case OutlineAdhesivePlaster = 'solar-outline-AdhesivePlaster';
    case OutlineBone = 'solar-outline-Bone';
    case OutlineBones = 'solar-outline-Bones';
    case OutlinePill = 'solar-outline-Pill';
    case OutlinePills = 'solar-outline-Pills';
    case OutlineHeartPulse = 'solar-outline-HeartPulse';
    case OutlineTestTubeMinimalistic = 'solar-outline-TestTubeMinimalistic';
    case OutlinePills2 = 'solar-outline-Pills2';
    case OutlinePulse = 'solar-outline-Pulse';
    case OutlineDropperMinimalistic = 'solar-outline-DropperMinimalistic';
    case OutlinePills3 = 'solar-outline-Pills3';
    case OutlineWhisk = 'solar-outline-Whisk';
    case OutlineBottle = 'solar-outline-Bottle';
    case OutlineOvenMittsMinimalistic = 'solar-outline-OvenMittsMinimalistic';
    case OutlineChefHatMinimalistic = 'solar-outline-ChefHatMinimalistic';
    case OutlineTeaCup = 'solar-outline-TeaCup';
    case OutlineWineglassTriangle = 'solar-outline-WineglassTriangle';
    case OutlineOvenMitts = 'solar-outline-OvenMitts';
    case OutlineCupPaper = 'solar-outline-CupPaper';
    case OutlineLadle = 'solar-outline-Ladle';
    case OutlineCorkscrew = 'solar-outline-Corkscrew';
    case OutlineDonutBitten = 'solar-outline-DonutBitten';
    case OutlineWineglass = 'solar-outline-Wineglass';
    case OutlineDonut = 'solar-outline-Donut';
    case OutlineCupHot = 'solar-outline-CupHot';
    case OutlineChefHatHeart = 'solar-outline-ChefHatHeart';
    case OutlineChefHat = 'solar-outline-ChefHat';
    case OutlineRollingPin = 'solar-outline-RollingPin';
    case OutlineCodeFile = 'solar-outline-CodeFile';
    case OutlineFileCorrupted = 'solar-outline-FileCorrupted';
    case OutlineFile = 'solar-outline-File';
    case OutlineFileRight = 'solar-outline-FileRight';
    case OutlineFileFavourite = 'solar-outline-FileFavourite';
    case OutlineFileDownload = 'solar-outline-FileDownload';
    case OutlineZipFile = 'solar-outline-ZipFile';
    case OutlineFileText = 'solar-outline-FileText';
    case OutlineFileSmile = 'solar-outline-FileSmile)';
    case OutlineFileCheck = 'solar-outline-FileCheck';
    case OutlineFileSend = 'solar-outline-FileSend';
    case OutlineFileLeft = 'solar-outline-FileLeft';
    case OutlineFigmaFile = 'solar-outline-FigmaFile';
    case OutlineFileRemove = 'solar-outline-FileRemove';
    case OutlineCloudFile = 'solar-outline-CloudFile';
    case OutlineRemoveFolder = 'solar-outline-RemoveFolder';
    case OutlineFolderFavouritestar = 'solar-outline-FolderFavourite(star)';
    case OutlineAddFolder = 'solar-outline-AddFolder';
    case OutlineFolderCheck = 'solar-outline-FolderCheck';
    case OutlineFolderFavouritebookmark = 'solar-outline-FolderFavourite(bookmark)';
    case OutlineFolder2 = 'solar-outline-Folder2';
    case OutlineFolderSecurity = 'solar-outline-FolderSecurity';
    case OutlineFolderCloud = 'solar-outline-FolderCloud';
    case OutlineMoveToFolder = 'solar-outline-MoveToFolder';
    case OutlineFolderError = 'solar-outline-FolderError';
    case OutlineFolderPathConnect = 'solar-outline-FolderPathConnect';
    case OutlineFolderOpen = 'solar-outline-FolderOpen';
    case OutlineFolder = 'solar-outline-Folder';
    case OutlineFolderWithFiles = 'solar-outline-FolderWithFiles';
    case OutlineCloudCheck = 'solar-outline-CloudCheck';
    case OutlineTemperature = 'solar-outline-Temperature';
    case OutlineWind = 'solar-outline-Wind';
    case OutlineCloudSnowfall = 'solar-outline-CloudSnowfall';
    case OutlineSunrise = 'solar-outline-Sunrise';
    case OutlineSun2 = 'solar-outline-Sun2';
    case OutlineCloudSun = 'solar-outline-CloudSun';
    case OutlineCloudBoltMinimalistic = 'solar-outline-CloudBoltMinimalistic';
    case OutlineCloudDownload = 'solar-outline-CloudDownload';
    case OutlineClouds = 'solar-outline-Clouds';
    case OutlineTornado = 'solar-outline-Tornado';
    case OutlineMoonSleep = 'solar-outline-MoonSleep';
    case OutlineCloudUpload = 'solar-outline-CloudUpload';
    case OutlineCloudRain = 'solar-outline-CloudRain';
    case OutlineFog = 'solar-outline-Fog';
    case OutlineSnowflake = 'solar-outline-Snowflake';
    case OutlineMoonFog = 'solar-outline-MoonFog';
    case OutlineCloudMinus = 'solar-outline-CloudMinus';
    case OutlineCloudBolt = 'solar-outline-CloudBolt';
    case OutlineCloudWaterdrop = 'solar-outline-CloudWaterdrop';
    case OutlineSunset = 'solar-outline-Sunset';
    case OutlineWaterdrops = 'solar-outline-Waterdrops';
    case OutlineMoonStars = 'solar-outline-MoonStars';
    case OutlineCloudPlus = 'solar-outline-CloudPlus';
    case OutlineSun = 'solar-outline-Sun';
    case OutlineCloudWaterdrops = 'solar-outline-CloudWaterdrops';
    case OutlineCloudSun2 = 'solar-outline-CloudSun2';
    case OutlineCloudyMoon = 'solar-outline-CloudyMoon';
    case OutlineTornadoSmall = 'solar-outline-TornadoSmall';
    case OutlineCloud = 'solar-outline-Cloud';
    case OutlineSunFog = 'solar-outline-SunFog';
    case OutlineCloundCross = 'solar-outline-CloundCross';
    case OutlineCloudSnowfallMinimalistic = 'solar-outline-CloudSnowfallMinimalistic';
    case OutlineCloudStorm = 'solar-outline-CloudStorm';
    case OutlineMoon = 'solar-outline-Moon';
    case OutlineRefreshCircle = 'solar-outline-RefreshCircle';
    case OutlineSquareArrowRightDown = 'solar-outline-SquareArrowRightDown';
    case OutlineRoundArrowLeftDown = 'solar-outline-RoundArrowLeftDown';
    case OutlineRestart = 'solar-outline-Restart';
    case OutlineRoundAltArrowDown = 'solar-outline-RoundAltArrowDown';
    case OutlineRoundSortVertical = 'solar-outline-RoundSortVertical';
    case OutlineSquareAltArrowUp = 'solar-outline-SquareAltArrowUp';
    case OutlineArrowLeftUp = 'solar-outline-ArrowLeftUp';
    case OutlineSortHorizontal = 'solar-outline-SortHorizontal';
    case OutlineTransferHorizontal = 'solar-outline-TransferHorizontal';
    case OutlineSquareDoubleAltArrowUp = 'solar-outline-SquareDoubleAltArrowUp';
    case OutlineRoundArrowLeftUp = 'solar-outline-RoundArrowLeftUp';
    case OutlineAltArrowRight = 'solar-outline-AltArrowRight';
    case OutlineRoundDoubleAltArrowUp = 'solar-outline-RoundDoubleAltArrowUp';
    case OutlineRestartCircle = 'solar-outline-RestartCircle';
    case OutlineSquareArrowDown = 'solar-outline-SquareArrowDown';
    case OutlineSortVertical = 'solar-outline-SortVertical';
    case OutlineSquareSortHorizontal = 'solar-outline-SquareSortHorizontal';
    case OutlineDoubleAltArrowLeft = 'solar-outline-DoubleAltArrowLeft';
    case OutlineSquareAltArrowDown = 'solar-outline-SquareAltArrowDown';
    case OutlineSquareAltArrowRight = 'solar-outline-SquareAltArrowRight';
    case OutlineSquareArrowUp = 'solar-outline-SquareArrowUp';
    case OutlineDoubleAltArrowRight = 'solar-outline-DoubleAltArrowRight';
    case OutlineRoundTransferVertical = 'solar-outline-RoundTransferVertical';
    case OutlineArrowLeft = 'solar-outline-ArrowLeft';
    case OutlineRoundDoubleAltArrowRight = 'solar-outline-RoundDoubleAltArrowRight';
    case OutlineSquareDoubleAltArrowLeft = 'solar-outline-SquareDoubleAltArrowLeft';
    case OutlineAltArrowDown = 'solar-outline-AltArrowDown';
    case OutlineRoundTransferHorizontal = 'solar-outline-RoundTransferHorizontal';
    case OutlineRoundArrowRightDown = 'solar-outline-RoundArrowRightDown';
    case OutlineArrowUp = 'solar-outline-ArrowUp';
    case OutlineRoundArrowLeft = 'solar-outline-RoundArrowLeft';
    case OutlineDoubleAltArrowUp = 'solar-outline-DoubleAltArrowUp';
    case OutlineRoundArrowRight = 'solar-outline-RoundArrowRight';
    case OutlineSquareTransferHorizontal = 'solar-outline-SquareTransferHorizontal';
    case OutlineArrowRight = 'solar-outline-ArrowRight';
    case OutlineRoundDoubleAltArrowLeft = 'solar-outline-RoundDoubleAltArrowLeft';
    case OutlineRoundArrowUp = 'solar-outline-RoundArrowUp';
    case OutlineSquareSortVertical = 'solar-outline-SquareSortVertical';
    case OutlineAltArrowLeft = 'solar-outline-AltArrowLeft';
    case OutlineSquareDoubleAltArrowRight = 'solar-outline-SquareDoubleAltArrowRight';
    case OutlineRefresh = 'solar-outline-Refresh';
    case OutlineTransferVertical = 'solar-outline-TransferVertical';
    case OutlineRefreshSquare = 'solar-outline-RefreshSquare';
    case OutlineSquareTransferVertical = 'solar-outline-SquareTransferVertical';
    case OutlineSquareDoubleAltArrowDown = 'solar-outline-SquareDoubleAltArrowDown';
    case OutlineRoundArrowRightUp = 'solar-outline-RoundArrowRightUp';
    case OutlineArrowDown = 'solar-outline-ArrowDown';
    case OutlineRestartSquare = 'solar-outline-RestartSquare';
    case OutlineSquareArrowRight = 'solar-outline-SquareArrowRight';
    case OutlineRoundDoubleAltArrowDown = 'solar-outline-RoundDoubleAltArrowDown';
    case OutlineSquareArrowLeftUp = 'solar-outline-SquareArrowLeftUp';
    case OutlineRoundArrowDown = 'solar-outline-RoundArrowDown';
    case OutlineSquareArrowRightUp = 'solar-outline-SquareArrowRightUp';
    case OutlineRoundTransferDiagonal = 'solar-outline-RoundTransferDiagonal';
    case OutlineArrowRightDown = 'solar-outline-ArrowRightDown';
    case OutlineArrowLeftDown = 'solar-outline-ArrowLeftDown';
    case OutlineRoundAltArrowLeft = 'solar-outline-RoundAltArrowLeft';
    case OutlineArrowRightUp = 'solar-outline-ArrowRightUp';
    case OutlineSquareArrowLeftDown = 'solar-outline-SquareArrowLeftDown';
    case OutlineRoundAltArrowUp = 'solar-outline-RoundAltArrowUp';
    case OutlineAltArrowUp = 'solar-outline-AltArrowUp';
    case OutlineSquareAltArrowLeft = 'solar-outline-SquareAltArrowLeft';
    case OutlineRoundSortHorizontal = 'solar-outline-RoundSortHorizontal';
    case OutlineDoubleAltArrowDown = 'solar-outline-DoubleAltArrowDown';
    case OutlineRoundAltArrowRight = 'solar-outline-RoundAltArrowRight';
    case OutlineSquareArrowLeft = 'solar-outline-SquareArrowLeft';
    case OutlineTuningSquare2 = 'solar-outline-TuningSquare2';
    case OutlineWidgetAdd = 'solar-outline-WidgetAdd';
    case OutlineTuningSquare = 'solar-outline-TuningSquare';
    case OutlineSettingsMinimalistic = 'solar-outline-SettingsMinimalistic';
    case OutlineWidget6 = 'solar-outline-Widget6';
    case OutlineWidget4 = 'solar-outline-Widget4';
    case OutlineSettings = 'solar-outline-Settings';
    case OutlineWidget5 = 'solar-outline-Widget5';
    case OutlineWidget2 = 'solar-outline-Widget2';
    case OutlineWidget3 = 'solar-outline-Widget3';
    case OutlineTuning2 = 'solar-outline-Tuning2';
    case OutlineTuning3 = 'solar-outline-Tuning3';
    case OutlineWidget = 'solar-outline-Widget';
    case OutlineTuning4 = 'solar-outline-Tuning4';
    case OutlineTuning = 'solar-outline-Tuning';
    case OutlineDiagramDown = 'solar-outline-DiagramDown';
    case OutlineChart2 = 'solar-outline-Chart2';
    case OutlineChart = 'solar-outline-Chart';
    case OutlineDiagramUp = 'solar-outline-DiagramUp';
    case OutlineGraphNew = 'solar-outline-GraphNew';
    case OutlineCourseUp = 'solar-outline-CourseUp';
    case OutlineGraphDownNew = 'solar-outline-GraphDownNew';
    case OutlinePieChart3 = 'solar-outline-PieChart3';
    case OutlinePieChart2 = 'solar-outline-PieChart2';
    case OutlineGraphNewUp = 'solar-outline-GraphNewUp';
    case OutlinePieChart = 'solar-outline-PieChart';
    case OutlineRoundGraph = 'solar-outline-RoundGraph';
    case OutlineGraphUp = 'solar-outline-GraphUp';
    case OutlineChartSquare = 'solar-outline-ChartSquare';
    case OutlineCourseDown = 'solar-outline-CourseDown';
    case OutlineChatSquare2 = 'solar-outline-ChatSquare2';
    case OutlineGraphDown = 'solar-outline-GraphDown';
    case OutlineGraph = 'solar-outline-Graph';
    case OutlinePresentationGraph = 'solar-outline-PresentationGraph';
    case OutlineMaximizeSquare3 = 'solar-outline-MaximizeSquare3';
    case OutlineMaximizeSquareMinimalistic = 'solar-outline-MaximizeSquareMinimalistic';
    case OutlineMaximizeSquare2 = 'solar-outline-MaximizeSquare2';
    case OutlineMinimizeSquare = 'solar-outline-MinimizeSquare';
    case OutlineDownloadSquare = 'solar-outline-DownloadSquare';
    case OutlineUndoLeftRoundSquare = 'solar-outline-UndoLeftRoundSquare';
    case OutlineReply = 'solar-outline-Reply';
    case OutlineLogout = 'solar-outline-Logout';
    case OutlineReciveSquare = 'solar-outline-ReciveSquare';
    case OutlineExport = 'solar-outline-Export';
    case OutlineSendTwiceSquare = 'solar-outline-SendTwiceSquare';
    case OutlineUndoLeftRound = 'solar-outline-UndoLeftRound';
    case OutlineForward2 = 'solar-outline-Forward2';
    case OutlineMaximize = 'solar-outline-Maximize';
    case OutlineUndoRightRound = 'solar-outline-UndoRightRound';
    case OutlineMinimizeSquare2 = 'solar-outline-MinimizeSquare2';
    case OutlineMinimizeSquare3 = 'solar-outline-MinimizeSquare3';
    case OutlineUploadTwiceSquare = 'solar-outline-UploadTwiceSquare';
    case OutlineMinimize = 'solar-outline-Minimize';
    case OutlineCircleTopUp = 'solar-outline-CircleTopUp';
    case OutlineUploadMinimalistic = 'solar-outline-UploadMinimalistic';
    case OutlineDownload = 'solar-outline-Download';
    case OutlineImport = 'solar-outline-Import';
    case OutlineLogin = 'solar-outline-Login';
    case OutlineUndoLeft = 'solar-outline-UndoLeft';
    case OutlineSquareTopUp = 'solar-outline-SquareTopUp';
    case OutlineDownloadTwiceSquare = 'solar-outline-DownloadTwiceSquare';
    case OutlineCircleBottomDown = 'solar-outline-CircleBottomDown';
    case OutlineMaximizeSquare = 'solar-outline-MaximizeSquare';
    case OutlineUploadSquare = 'solar-outline-UploadSquare';
    case OutlineUndoRightSquare = 'solar-outline-UndoRightSquare';
    case OutlineReciveTwiceSquare = 'solar-outline-ReciveTwiceSquare';
    case OutlineCircleTopDown = 'solar-outline-CircleTopDown';
    case OutlineArrowToDownLeft = 'solar-outline-ArrowToDownLeft';
    case OutlineLogout2 = 'solar-outline-Logout2';
    case OutlineLogout3 = 'solar-outline-Logout3';
    case OutlineScale = 'solar-outline-Scale';
    case OutlineArrowToDownRight = 'solar-outline-ArrowToDownRight';
    case OutlineDownloadMinimalistic = 'solar-outline-DownloadMinimalistic';
    case OutlineMinimizeSquareMinimalistic = 'solar-outline-MinimizeSquareMinimalistic';
    case OutlineReply2 = 'solar-outline-Reply2';
    case OutlineSquareBottomUp = 'solar-outline-SquareBottomUp';
    case OutlineUndoRight = 'solar-outline-UndoRight';
    case OutlineUndoLeftSquare = 'solar-outline-UndoLeftSquare';
    case OutlineSendSquare = 'solar-outline-SendSquare';
    case OutlineExit = 'solar-outline-Exit';
    case OutlineSquareBottomDown = 'solar-outline-SquareBottomDown';
    case OutlineUndoRightRoundSquare = 'solar-outline-UndoRightRoundSquare';
    case OutlineArrowToTopLeft = 'solar-outline-ArrowToTopLeft';
    case OutlineCircleBottomUp = 'solar-outline-CircleBottomUp';
    case OutlineScreenShare = 'solar-outline-ScreenShare';
    case OutlineUpload = 'solar-outline-Upload';
    case OutlineSquareTopDown = 'solar-outline-SquareTopDown';
    case OutlineArrowToTopRight = 'solar-outline-ArrowToTopRight';
    case OutlineLogin3 = 'solar-outline-Login3';
    case OutlineLogin2 = 'solar-outline-Login2';
    case OutlinePassport = 'solar-outline-Passport';
    case OutlineDiplomaVerified = 'solar-outline-DiplomaVerified';
    case OutlineCaseRound = 'solar-outline-CaseRound';
    case OutlineBackpack = 'solar-outline-Backpack';
    case OutlineBook2 = 'solar-outline-Book2';
    case OutlineSquareAcademicCap2 = 'solar-outline-SquareAcademicCap2';
    case OutlineCaseRoundMinimalistic = 'solar-outline-CaseRoundMinimalistic';
    case OutlineCase = 'solar-outline-Case';
    case OutlineBookBookmarkMinimalistic = 'solar-outline-BookBookmarkMinimalistic';
    case OutlineBookmarkOpened = 'solar-outline-BookmarkOpened';
    case OutlineDiploma = 'solar-outline-Diploma';
    case OutlineBook = 'solar-outline-Book';
    case OutlineSquareAcademicCap = 'solar-outline-SquareAcademicCap';
    case OutlineBookmarkCircle = 'solar-outline-BookmarkCircle';
    case OutlineCalculatorMinimalistic = 'solar-outline-CalculatorMinimalistic';
    case OutlineNotebookSquare = 'solar-outline-NotebookSquare';
    case OutlineBookMinimalistic = 'solar-outline-BookMinimalistic';
    case OutlineCaseMinimalistic = 'solar-outline-CaseMinimalistic';
    case OutlineNotebookBookmark = 'solar-outline-NotebookBookmark';
    case OutlinePassportMinimalistic = 'solar-outline-PassportMinimalistic';
    case OutlineBookBookmark = 'solar-outline-BookBookmark';
    case OutlineBookmarkSquareMinimalistic = 'solar-outline-BookmarkSquareMinimalistic';
    case OutlineBookmark = 'solar-outline-Bookmark';
    case OutlinePlusMinus = 'solar-outline-PlusMinus';
    case OutlineCalculator = 'solar-outline-Calculator';
    case OutlineBookmarkSquare = 'solar-outline-BookmarkSquare';
    case OutlineNotebookMinimalistic = 'solar-outline-NotebookMinimalistic';
    case OutlineFireSquare = 'solar-outline-FireSquare';
    case OutlineSuitcaseLines = 'solar-outline-SuitcaseLines';
    case OutlineFire = 'solar-outline-Fire';
    case OutlineBonfire = 'solar-outline-Bonfire';
    case OutlineSuitcaseTag = 'solar-outline-SuitcaseTag';
    case OutlineLeaf = 'solar-outline-Leaf';
    case OutlineSuitcase = 'solar-outline-Suitcase';
    case OutlineFlame = 'solar-outline-Flame';
    case OutlineFireMinimalistic = 'solar-outline-FireMinimalistic';
    case OutlineBellBing = 'solar-outline-BellBing';
    case OutlineNotificationLinesRemove = 'solar-outline-NotificationLinesRemove';
    case OutlineNotificationUnread = 'solar-outline-NotificationUnread';
    case OutlineBell = 'solar-outline-Bell';
    case OutlineNotificationRemove = 'solar-outline-NotificationRemove';
    case OutlineNotificationUnreadLines = 'solar-outline-NotificationUnreadLines';
    case OutlineBellOff = 'solar-outline-BellOff';
    case OutlineLightning = 'solar-outline-Lightning';
    case OutlineLightbulbMinimalistic = 'solar-outline-LightbulbMinimalistic';
    case OutlineServerSquareCloud = 'solar-outline-ServerSquareCloud';
    case OutlineLightbulbBolt = 'solar-outline-LightbulbBolt';
    case OutlineAirbudsCharge = 'solar-outline-AirbudsCharge';
    case OutlineServerPath = 'solar-outline-ServerPath';
    case OutlineSimCardMinimalistic = 'solar-outline-SimCardMinimalistic';
    case OutlineSmartphone = 'solar-outline-Smartphone';
    case OutlineTurntable = 'solar-outline-Turntable';
    case OutlineAirbudsCheck = 'solar-outline-AirbudsCheck';
    case OutlineMouseMinimalistic = 'solar-outline-MouseMinimalistic';
    case OutlineSmartphoneRotateAngle = 'solar-outline-SmartphoneRotateAngle';
    case OutlineRadioMinimalistic = 'solar-outline-RadioMinimalistic';
    case OutlineAirbuds = 'solar-outline-Airbuds';
    case OutlineSmartphoneRotateOrientation = 'solar-outline-SmartphoneRotateOrientation';
    case OutlineIPhone = 'solar-outline-IPhone';
    case OutlineSimCard = 'solar-outline-SimCard';
    case OutlineFlashDrive = 'solar-outline-FlashDrive';
    case OutlineDevices = 'solar-outline-Devices';
    case OutlineSimCards = 'solar-outline-SimCards';
    case OutlineAirbudsCaseOpen = 'solar-outline-AirbudsCaseOpen';
    case OutlineTurntableMusicNote = 'solar-outline-TurntableMusicNote';
    case OutlineKeyboard = 'solar-outline-Keyboard';
    case OutlineGamepadCharge = 'solar-outline-GamepadCharge';
    case OutlineBoombox = 'solar-outline-Boombox';
    case OutlineSmartSpeakerMinimalistic = 'solar-outline-SmartSpeakerMinimalistic';
    case OutlineTelescope = 'solar-outline-Telescope';
    case OutlineMonitorCamera = 'solar-outline-MonitorCamera';
    case OutlineLaptopMinimalistic = 'solar-outline-LaptopMinimalistic';
    case OutlineServer2 = 'solar-outline-Server2';
    case OutlineSmartSpeaker = 'solar-outline-SmartSpeaker';
    case OutlineProjector = 'solar-outline-Projector';
    case OutlineServer = 'solar-outline-Server';
    case OutlineTV = 'solar-outline-TV';
    case OutlineCassette2 = 'solar-outline-Cassette2';
    case OutlineRadio = 'solar-outline-Radio';
    case OutlineSmartphoneVibration = 'solar-outline-SmartphoneVibration';
    case OutlineAirbudsLeft = 'solar-outline-AirbudsLeft';
    case OutlineHeadphonesRound = 'solar-outline-HeadphonesRound';
    case OutlineGameboy = 'solar-outline-Gameboy';
    case OutlineHeadphonesRoundSound = 'solar-outline-HeadphonesRoundSound';
    case OutlineCPU = 'solar-outline-CPU';
    case OutlinePrinter2 = 'solar-outline-Printer2';
    case OutlineHeadphonesSquare = 'solar-outline-HeadphonesSquare';
    case OutlineServerSquareUpdate = 'solar-outline-ServerSquareUpdate';
    case OutlinePrinterMinimalistic = 'solar-outline-PrinterMinimalistic';
    case OutlineBluetooth = 'solar-outline-Bluetooth';
    case OutlineWirelessCharge = 'solar-outline-WirelessCharge';
    case OutlineBluetoothCircle = 'solar-outline-BluetoothCircle';
    case OutlineAirbudsCaseMinimalistic = 'solar-outline-AirbudsCaseMinimalistic';
    case OutlineLightbulb = 'solar-outline-Lightbulb';
    case OutlineAirbudsRemove = 'solar-outline-AirbudsRemove';
    case OutlineSmartphoneRotate2 = 'solar-outline-SmartphoneRotate2';
    case OutlineSsdSquare = 'solar-outline-SsdSquare';
    case OutlinePrinter = 'solar-outline-Printer';
    case OutlineSmartphone2 = 'solar-outline-Smartphone2';
    case OutlineServerMinimalistic = 'solar-outline-ServerMinimalistic';
    case OutlineHeadphonesSquareSound = 'solar-outline-HeadphonesSquareSound';
    case OutlineDiskette = 'solar-outline-Diskette';
    case OutlineBluetoothWave = 'solar-outline-BluetoothWave';
    case OutlineSmartSpeaker2 = 'solar-outline-SmartSpeaker2';
    case OutlineLaptop3 = 'solar-outline-Laptop3';
    case OutlineLaptop2 = 'solar-outline-Laptop2';
    case OutlineMouseCircle = 'solar-outline-MouseCircle';
    case OutlineTurntableMinimalistic = 'solar-outline-TurntableMinimalistic';
    case OutlineSmartphoneUpdate = 'solar-outline-SmartphoneUpdate';
    case OutlineGamepadMinimalistic = 'solar-outline-GamepadMinimalistic';
    case OutlineSdCard = 'solar-outline-SdCard';
    case OutlinePlugCircle = 'solar-outline-PlugCircle';
    case OutlineAirbudsCase = 'solar-outline-AirbudsCase';
    case OutlineSsdRound = 'solar-outline-SsdRound';
    case OutlineLaptop = 'solar-outline-Laptop';
    case OutlineAirbudsRight = 'solar-outline-AirbudsRight';
    case OutlineDisplay = 'solar-outline-Display';
    case OutlineMonitorSmartphone = 'solar-outline-MonitorSmartphone';
    case OutlineSocket = 'solar-outline-Socket';
    case OutlineGamepadOld = 'solar-outline-GamepadOld';
    case OutlineCpuBolt = 'solar-outline-CpuBolt';
    case OutlineAirbudsCaseCharge = 'solar-outline-AirbudsCaseCharge';
    case OutlineTablet = 'solar-outline-Tablet';
    case OutlineWeigher = 'solar-outline-Weigher';
    case OutlineServerSquare = 'solar-outline-ServerSquare';
    case OutlineMouse = 'solar-outline-Mouse';
    case OutlineGamepadNoCharge = 'solar-outline-GamepadNoCharge';
    case OutlineBluetoothSquare = 'solar-outline-BluetoothSquare';
    case OutlineCloudStorage = 'solar-outline-CloudStorage';
    case OutlineGamepad = 'solar-outline-Gamepad';
    case OutlineMonitor = 'solar-outline-Monitor';
    case OutlineCassette = 'solar-outline-Cassette';
    // Linear Style (1235 icons)
    case LinearFacemaskCircle = 'solar-linear-FacemaskCircle';
    case LinearConfoundedCircle = 'solar-linear-ConfoundedCircle';
    case LinearSadSquare = 'solar-linear-SadSquare';
    case LinearSleepingCircle = 'solar-linear-SleepingCircle';
    case LinearFaceScanCircle = 'solar-linear-FaceScanCircle';
    case LinearSmileCircle = 'solar-linear-SmileCircle';
    case LinearStickerSmileCircle = 'solar-linear-StickerSmileCircle';
    case LinearStickerSquare = 'solar-linear-StickerSquare';
    case LinearEmojiFunnyCircle = 'solar-linear-EmojiFunnyCircle';
    case LinearExpressionlessSquare = 'solar-linear-ExpressionlessSquare';
    case LinearSleepingSquare = 'solar-linear-SleepingSquare';
    case LinearSadCircle = 'solar-linear-SadCircle';
    case LinearFacemaskSquare = 'solar-linear-FacemaskSquare';
    case LinearConfoundedSquare = 'solar-linear-ConfoundedSquare';
    case LinearFaceScanSquare = 'solar-linear-FaceScanSquare';
    case LinearSmileSquare = 'solar-linear-SmileSquare';
    case LinearStickerSmileCircle2 = 'solar-linear-StickerSmileCircle2';
    case LinearStickerSmileSquare = 'solar-linear-StickerSmileSquare';
    case LinearEmojiFunnySquare = 'solar-linear-EmojiFunnySquare';
    case LinearStickerCircle = 'solar-linear-StickerCircle';
    case LinearExpressionlessCircle = 'solar-linear-ExpressionlessCircle';
    case LinearLike = 'solar-linear-Like';
    case LinearMedalStarSquare = 'solar-linear-MedalStarSquare';
    case LinearDislike = 'solar-linear-Dislike';
    case LinearStarShine = 'solar-linear-StarShine';
    case LinearHeartAngle = 'solar-linear-HeartAngle';
    case LinearMedalRibbon = 'solar-linear-MedalRibbon';
    case LinearHeartShine = 'solar-linear-HeartShine';
    case LinearMedalStarCircle = 'solar-linear-MedalStarCircle';
    case LinearMedalRibbonsStar = 'solar-linear-MedalRibbonsStar';
    case LinearStar = 'solar-linear-Star';
    case LinearHeartUnlock = 'solar-linear-HeartUnlock';
    case LinearMedalRibbonStar = 'solar-linear-MedalRibbonStar';
    case LinearHeartLock = 'solar-linear-HeartLock';
    case LinearHeartBroken = 'solar-linear-HeartBroken';
    case LinearHearts = 'solar-linear-Hearts';
    case LinearMedalStar = 'solar-linear-MedalStar';
    case LinearHeart = 'solar-linear-Heart';
    case LinearCloset = 'solar-linear-Closet';
    case LinearBed = 'solar-linear-Bed';
    case LinearWashingMachine = 'solar-linear-WashingMachine';
    case LinearBedsideTable = 'solar-linear-BedsideTable';
    case LinearSofa3 = 'solar-linear-Sofa3';
    case LinearSofa2 = 'solar-linear-Sofa2';
    case LinearChair2 = 'solar-linear-Chair2';
    case LinearBath = 'solar-linear-Bath';
    case LinearSmartVacuumCleaner2 = 'solar-linear-SmartVacuumCleaner2';
    case LinearCondicioner = 'solar-linear-Condicioner';
    case LinearSmartVacuumCleaner = 'solar-linear-SmartVacuumCleaner';
    case LinearRemoteController2 = 'solar-linear-RemoteController2';
    case LinearFloorLampMinimalistic = 'solar-linear-FloorLampMinimalistic';
    case LinearLamp = 'solar-linear-Lamp';
    case LinearBarChair = 'solar-linear-BarChair';
    case LinearBedsideTable2 = 'solar-linear-BedsideTable2';
    case LinearCloset2 = 'solar-linear-Closet2';
    case LinearBedsideTable3 = 'solar-linear-BedsideTable3';
    case LinearSpeaker = 'solar-linear-Speaker';
    case LinearVolumeKnob = 'solar-linear-VolumeKnob';
    case LinearArmchair = 'solar-linear-Armchair';
    case LinearSpeakerMinimalistic = 'solar-linear-SpeakerMinimalistic';
    case LinearRemoteController = 'solar-linear-RemoteController';
    case LinearTrellis = 'solar-linear-Trellis';
    case LinearFloorLamp = 'solar-linear-FloorLamp';
    case LinearCondicioner2 = 'solar-linear-Condicioner2';
    case LinearBedsideTable4 = 'solar-linear-BedsideTable4';
    case LinearArmchair2 = 'solar-linear-Armchair2';
    case LinearWashingMachineMinimalistic = 'solar-linear-WashingMachineMinimalistic';
    case LinearChair = 'solar-linear-Chair';
    case LinearRemoteControllerMinimalistic = 'solar-linear-RemoteControllerMinimalistic';
    case LinearChandelier = 'solar-linear-Chandelier';
    case LinearFridge = 'solar-linear-Fridge';
    case LinearMirror = 'solar-linear-Mirror';
    case LinearSofa = 'solar-linear-Sofa';
    case LinearEarth = 'solar-linear-Earth';
    case LinearStarsLine = 'solar-linear-StarsLine';
    case LinearStarFall2 = 'solar-linear-StarFall2';
    case LinearStarFall = 'solar-linear-StarFall';
    case LinearBlackHole3 = 'solar-linear-BlackHole3';
    case LinearWomen = 'solar-linear-Women';
    case LinearBlackHole = 'solar-linear-BlackHole';
    case LinearStarRings = 'solar-linear-StarRings';
    case LinearBlackHole2 = 'solar-linear-BlackHole2';
    case LinearStarFallMinimalistic2 = 'solar-linear-StarFallMinimalistic2';
    case LinearPlanet = 'solar-linear-Planet';
    case LinearSatellite = 'solar-linear-Satellite';
    case LinearMen = 'solar-linear-Men';
    case LinearRocket2 = 'solar-linear-Rocket2';
    case LinearStars = 'solar-linear-Stars';
    case LinearStarAngle = 'solar-linear-StarAngle';
    case LinearInfinity = 'solar-linear-Infinity';
    case LinearUfo2 = 'solar-linear-Ufo2';
    case LinearUfo3 = 'solar-linear-Ufo3';
    case LinearStarRing = 'solar-linear-StarRing';
    case LinearPlanet2 = 'solar-linear-Planet2';
    case LinearPlanet3 = 'solar-linear-Planet3';
    case LinearAsteroid = 'solar-linear-Asteroid';
    case LinearStarsMinimalistic = 'solar-linear-StarsMinimalistic';
    case LinearUFO = 'solar-linear-UFO';
    case LinearPlanet4 = 'solar-linear-Planet4';
    case LinearRocket = 'solar-linear-Rocket';
    case LinearStarFallMinimalistic = 'solar-linear-StarFallMinimalistic';
    case LinearStarRainbow = 'solar-linear-StarRainbow';
    case LinearAtom = 'solar-linear-Atom';
    case LinearStarCircle = 'solar-linear-StarCircle';
    case LinearCompassBig = 'solar-linear-CompassBig';
    case LinearMapPointSchool = 'solar-linear-MapPointSchool';
    case LinearSignpost = 'solar-linear-Signpost';
    case LinearMapArrowDown = 'solar-linear-MapArrowDown';
    case LinearMap = 'solar-linear-Map';
    case LinearMapArrowUp = 'solar-linear-MapArrowUp';
    case LinearPointOnMapPerspective = 'solar-linear-PointOnMapPerspective';
    case LinearRadar = 'solar-linear-Radar';
    case LinearStreets = 'solar-linear-Streets';
    case LinearMapPointWave = 'solar-linear-MapPointWave';
    case LinearPeopleNearby = 'solar-linear-PeopleNearby';
    case LinearStreetsMapPoint = 'solar-linear-StreetsMapPoint';
    case LinearMapPointSearch = 'solar-linear-MapPointSearch';
    case LinearGPS = 'solar-linear-GPS';
    case LinearMapArrowSquare = 'solar-linear-MapArrowSquare';
    case LinearBranchingPathsDown = 'solar-linear-BranchingPathsDown';
    case LinearMapPointRotate = 'solar-linear-MapPointRotate';
    case LinearGlobal = 'solar-linear-Global';
    case LinearCompassSquare = 'solar-linear-CompassSquare';
    case LinearRouting3 = 'solar-linear-Routing3';
    case LinearRouting2 = 'solar-linear-Routing2';
    case LinearMapPointRemove = 'solar-linear-MapPointRemove';
    case LinearGlobus = 'solar-linear-Globus';
    case LinearSignpost2 = 'solar-linear-Signpost2';
    case LinearRadar2 = 'solar-linear-Radar2';
    case LinearStreetsNavigation = 'solar-linear-StreetsNavigation';
    case LinearMapPoint = 'solar-linear-MapPoint';
    case LinearMapPointHospital = 'solar-linear-MapPointHospital';
    case LinearCompass = 'solar-linear-Compass';
    case LinearMapPointAdd = 'solar-linear-MapPointAdd';
    case LinearBranchingPathsUp = 'solar-linear-BranchingPathsUp';
    case LinearMapPointFavourite = 'solar-linear-MapPointFavourite';
    case LinearRoute = 'solar-linear-Route';
    case LinearPointOnMap = 'solar-linear-PointOnMap';
    case LinearMapArrowRight = 'solar-linear-MapArrowRight';
    case LinearRouting = 'solar-linear-Routing';
    case LinearMapArrowLeft = 'solar-linear-MapArrowLeft';
    case LinearIncognito = 'solar-linear-Incognito';
    case LinearLockPassword = 'solar-linear-LockPassword';
    case LinearShieldNetwork = 'solar-linear-ShieldNetwork';
    case LinearKeyMinimalisticSquare = 'solar-linear-KeyMinimalisticSquare';
    case LinearLockKeyholeUnlocked = 'solar-linear-LockKeyholeUnlocked';
    case LinearLock = 'solar-linear-Lock';
    case LinearShieldKeyhole = 'solar-linear-ShieldKeyhole';
    case LinearEyeClosed = 'solar-linear-EyeClosed';
    case LinearKey = 'solar-linear-Key';
    case LinearShieldMinus = 'solar-linear-ShieldMinus';
    case LinearShield = 'solar-linear-Shield';
    case LinearLockUnlocked = 'solar-linear-LockUnlocked';
    case LinearBombMinimalistic = 'solar-linear-BombMinimalistic';
    case LinearShieldStar = 'solar-linear-ShieldStar';
    case LinearBomb = 'solar-linear-Bomb';
    case LinearKeySquare = 'solar-linear-KeySquare';
    case LinearLockKeyholeMinimalisticUnlocked = 'solar-linear-LockKeyholeMinimalisticUnlocked';
    case LinearShieldCross = 'solar-linear-ShieldCross';
    case LinearObjectScan = 'solar-linear-ObjectScan';
    case LinearPasswordMinimalisticInput = 'solar-linear-PasswordMinimalisticInput';
    case LinearLockPasswordUnlocked = 'solar-linear-LockPasswordUnlocked';
    case LinearSiren = 'solar-linear-Siren';
    case LinearShieldMinimalistic = 'solar-linear-ShieldMinimalistic';
    case LinearEyeScan = 'solar-linear-EyeScan';
    case LinearKeyMinimalisticSquare2 = 'solar-linear-KeyMinimalisticSquare2';
    case LinearScanner2 = 'solar-linear-Scanner2';
    case LinearKeyMinimalisticSquare3 = 'solar-linear-KeyMinimalisticSquare3';
    case LinearKeyMinimalistic2 = 'solar-linear-KeyMinimalistic2';
    case LinearCodeScan = 'solar-linear-CodeScan';
    case LinearShieldPlus = 'solar-linear-ShieldPlus';
    case LinearPasswordMinimalistic = 'solar-linear-PasswordMinimalistic';
    case LinearEye = 'solar-linear-Eye';
    case LinearQrCode = 'solar-linear-QrCode';
    case LinearShieldCheck = 'solar-linear-ShieldCheck';
    case LinearKeyMinimalistic = 'solar-linear-KeyMinimalistic';
    case LinearLockKeyhole = 'solar-linear-LockKeyhole';
    case LinearShieldUser = 'solar-linear-ShieldUser';
    case LinearKeySquare2 = 'solar-linear-KeySquare2';
    case LinearBombEmoji = 'solar-linear-BombEmoji';
    case LinearScanner = 'solar-linear-Scanner';
    case LinearShieldUp = 'solar-linear-ShieldUp';
    case LinearSirenRounded = 'solar-linear-SirenRounded';
    case LinearLockKeyholeMinimalistic = 'solar-linear-LockKeyholeMinimalistic';
    case LinearPassword = 'solar-linear-Password';
    case LinearShieldKeyholeMinimalistic = 'solar-linear-ShieldKeyholeMinimalistic';
    case LinearShieldWarning = 'solar-linear-ShieldWarning';
    case LinearPallete2 = 'solar-linear-Pallete2';
    case LinearAlignVerticalSpacing = 'solar-linear-AlignVerticalSpacing';
    case LinearAlignVerticalCenter = 'solar-linear-AlignVerticalCenter';
    case LinearCropMinimalistic = 'solar-linear-CropMinimalistic';
    case LinearMirrorRight = 'solar-linear-MirrorRight';
    case LinearAlignBottom = 'solar-linear-AlignBottom';
    case LinearRadialBlur = 'solar-linear-RadialBlur';
    case LinearCrop = 'solar-linear-Crop';
    case LinearAlignHorizontaSpacing = 'solar-linear-AlignHorizontaSpacing';
    case LinearRulerPen = 'solar-linear-RulerPen';
    case LinearThreeSquares = 'solar-linear-ThreeSquares';
    case LinearPaintRoller = 'solar-linear-PaintRoller';
    case LinearLayers = 'solar-linear-Layers';
    case LinearFilters = 'solar-linear-Filters';
    case LinearRulerCrossPen = 'solar-linear-RulerCrossPen';
    case LinearFlipHorizontal = 'solar-linear-FlipHorizontal';
    case LinearAlignLeft = 'solar-linear-AlignLeft';
    case LinearRuler = 'solar-linear-Ruler';
    case LinearPalette = 'solar-linear-Palette';
    case LinearAlignTop = 'solar-linear-AlignTop';
    case LinearAlignHorizontalCenter = 'solar-linear-AlignHorizontalCenter';
    case LinearAlignRight = 'solar-linear-AlignRight';
    case LinearRulerAngular = 'solar-linear-RulerAngular';
    case LinearPipette = 'solar-linear-Pipette';
    case LinearFlipVertical = 'solar-linear-FlipVertical';
    case LinearMirrorLeft = 'solar-linear-MirrorLeft';
    case LinearLayersMinimalistic = 'solar-linear-LayersMinimalistic';
    case LinearColourTuneing = 'solar-linear-ColourTuneing';
    case LinearPaletteRound = 'solar-linear-PaletteRound';
    case LinearEraser = 'solar-linear-Eraser';
    case LinearTextItalicCircle = 'solar-linear-TextItalicCircle';
    case LinearLinkRound = 'solar-linear-LinkRound';
    case LinearTextItalic = 'solar-linear-TextItalic';
    case LinearLinkBrokenMinimalistic = 'solar-linear-LinkBrokenMinimalistic';
    case LinearTextUnderlineCross = 'solar-linear-TextUnderlineCross';
    case LinearLink = 'solar-linear-Link';
    case LinearEraserCircle = 'solar-linear-EraserCircle';
    case LinearLinkCircle = 'solar-linear-LinkCircle';
    case LinearTextBoldCircle = 'solar-linear-TextBoldCircle';
    case LinearTextField = 'solar-linear-TextField';
    case LinearTextSquare = 'solar-linear-TextSquare';
    case LinearTextSquare2 = 'solar-linear-TextSquare2';
    case LinearLinkRoundAngle = 'solar-linear-LinkRoundAngle';
    case LinearTextUnderlineCircle = 'solar-linear-TextUnderlineCircle';
    case LinearTextCrossCircle = 'solar-linear-TextCrossCircle';
    case LinearTextItalicSquare = 'solar-linear-TextItalicSquare';
    case LinearParagraphSpacing = 'solar-linear-ParagraphSpacing';
    case LinearText = 'solar-linear-Text';
    case LinearLinkBroken = 'solar-linear-LinkBroken';
    case LinearTextCross = 'solar-linear-TextCross';
    case LinearTextUnderline = 'solar-linear-TextUnderline';
    case LinearLinkMinimalistic = 'solar-linear-LinkMinimalistic';
    case LinearLinkMinimalistic2 = 'solar-linear-LinkMinimalistic2';
    case LinearTextBold = 'solar-linear-TextBold';
    case LinearTextSelection = 'solar-linear-TextSelection';
    case LinearTextFieldFocus = 'solar-linear-TextFieldFocus';
    case LinearTextBoldSquare = 'solar-linear-TextBoldSquare';
    case LinearEraserSquare = 'solar-linear-EraserSquare';
    case LinearLinkSquare = 'solar-linear-LinkSquare';
    case LinearTextCircle = 'solar-linear-TextCircle';
    case LinearBackspace = 'solar-linear-Backspace';
    case LinearTextCrossSquare = 'solar-linear-TextCrossSquare';
    case LinearInboxUnread = 'solar-linear-InboxUnread';
    case LinearChatUnread = 'solar-linear-ChatUnread';
    case LinearChatRound = 'solar-linear-ChatRound';
    case LinearUnread = 'solar-linear-Unread';
    case LinearMailbox = 'solar-linear-Mailbox';
    case LinearLetter = 'solar-linear-Letter';
    case LinearPenNewRound = 'solar-linear-PenNewRound';
    case LinearMultipleForwardRight = 'solar-linear-MultipleForwardRight';
    case LinearMultipleForwardLeft = 'solar-linear-MultipleForwardLeft';
    case LinearInboxArchive = 'solar-linear-InboxArchive';
    case LinearInbox = 'solar-linear-Inbox';
    case LinearPen2 = 'solar-linear-Pen2';
    case LinearPenNewSquare = 'solar-linear-PenNewSquare';
    case LinearPen = 'solar-linear-Pen';
    case LinearChatDots = 'solar-linear-ChatDots';
    case LinearChatSquareCall = 'solar-linear-ChatSquareCall';
    case LinearSquareShareLine = 'solar-linear-SquareShareLine';
    case LinearChatRoundCheck = 'solar-linear-ChatRoundCheck';
    case LinearInboxOut = 'solar-linear-InboxOut';
    case LinearPlain3 = 'solar-linear-Plain3';
    case LinearChatRoundDots = 'solar-linear-ChatRoundDots';
    case LinearChatRoundLike = 'solar-linear-ChatRoundLike';
    case LinearPlain2 = 'solar-linear-Plain2';
    case LinearChatRoundUnread = 'solar-linear-ChatRoundUnread';
    case LinearChatSquareLike = 'solar-linear-ChatSquareLike';
    case LinearPaperclip = 'solar-linear-Paperclip';
    case LinearChatSquareCheck = 'solar-linear-ChatSquareCheck';
    case LinearChatSquare = 'solar-linear-ChatSquare';
    case LinearLetterOpened = 'solar-linear-LetterOpened';
    case LinearSquareForward = 'solar-linear-SquareForward';
    case LinearLetterUnread = 'solar-linear-LetterUnread';
    case LinearPaperclipRounded2 = 'solar-linear-PaperclipRounded2';
    case LinearChatRoundCall = 'solar-linear-ChatRoundCall';
    case LinearInboxLine = 'solar-linear-InboxLine';
    case LinearChatRoundVideo = 'solar-linear-ChatRoundVideo';
    case LinearChatRoundMoney = 'solar-linear-ChatRoundMoney';
    case LinearInboxIn = 'solar-linear-InboxIn';
    case LinearCheckRead = 'solar-linear-CheckRead';
    case LinearChatRoundLine = 'solar-linear-ChatRoundLine';
    case LinearForward = 'solar-linear-Forward';
    case LinearPaperclip2 = 'solar-linear-Paperclip2';
    case LinearDialog2 = 'solar-linear-Dialog2';
    case LinearDialog = 'solar-linear-Dialog';
    case LinearPaperclipRounded = 'solar-linear-PaperclipRounded';
    case LinearPlain = 'solar-linear-Plain';
    case LinearChatSquareArrow = 'solar-linear-ChatSquareArrow';
    case LinearChatSquareCode = 'solar-linear-ChatSquareCode';
    case LinearChatLine = 'solar-linear-ChatLine';
    case LinearTennis = 'solar-linear-Tennis';
    case LinearBicyclingRound = 'solar-linear-BicyclingRound';
    case LinearBalls = 'solar-linear-Balls';
    case LinearMeditationRound = 'solar-linear-MeditationRound';
    case LinearStretchingRound = 'solar-linear-StretchingRound';
    case LinearDumbbells2 = 'solar-linear-Dumbbells2';
    case LinearMeditation = 'solar-linear-Meditation';
    case LinearRunning2 = 'solar-linear-Running2';
    case LinearRugby = 'solar-linear-Rugby';
    case LinearBodyShapeMinimalistic = 'solar-linear-BodyShapeMinimalistic';
    case LinearStretching = 'solar-linear-Stretching';
    case LinearBowling = 'solar-linear-Bowling';
    case LinearRanking = 'solar-linear-Ranking';
    case LinearTreadmillRound = 'solar-linear-TreadmillRound';
    case LinearVolleyball = 'solar-linear-Volleyball';
    case LinearDumbbellLargeMinimalistic = 'solar-linear-DumbbellLargeMinimalistic';
    case LinearRunningRound = 'solar-linear-RunningRound';
    case LinearHiking = 'solar-linear-Hiking';
    case LinearHikingMinimalistic = 'solar-linear-HikingMinimalistic';
    case LinearWaterSun = 'solar-linear-WaterSun';
    case LinearGolf = 'solar-linear-Golf';
    case LinearSkateboarding = 'solar-linear-Skateboarding';
    case LinearDumbbells = 'solar-linear-Dumbbells';
    case LinearWalkingRound = 'solar-linear-WalkingRound';
    case LinearRunning = 'solar-linear-Running';
    case LinearTreadmill = 'solar-linear-Treadmill';
    case LinearSkateboard = 'solar-linear-Skateboard';
    case LinearDumbbellSmall = 'solar-linear-DumbbellSmall';
    case LinearBasketball = 'solar-linear-Basketball';
    case LinearFootball = 'solar-linear-Football';
    case LinearDumbbell = 'solar-linear-Dumbbell';
    case LinearBodyShape = 'solar-linear-BodyShape';
    case LinearWater = 'solar-linear-Water';
    case LinearSkateboardingRound = 'solar-linear-SkateboardingRound';
    case LinearHikingRound = 'solar-linear-HikingRound';
    case LinearVolleyball2 = 'solar-linear-Volleyball2';
    case LinearTennis2 = 'solar-linear-Tennis2';
    case LinearSwimming = 'solar-linear-Swimming';
    case LinearBicycling = 'solar-linear-Bicycling';
    case LinearWalking = 'solar-linear-Walking';
    case LinearDumbbellLarge = 'solar-linear-DumbbellLarge';
    case LinearCalendarMark = 'solar-linear-CalendarMark';
    case LinearHistory2 = 'solar-linear-History2';
    case LinearWatchSquareMinimalisticCharge = 'solar-linear-WatchSquareMinimalisticCharge';
    case LinearHistory3 = 'solar-linear-History3';
    case LinearHourglass = 'solar-linear-Hourglass';
    case LinearCalendarSearch = 'solar-linear-CalendarSearch';
    case LinearStopwatchPlay = 'solar-linear-StopwatchPlay';
    case LinearWatchRound = 'solar-linear-WatchRound';
    case LinearCalendarAdd = 'solar-linear-CalendarAdd';
    case LinearCalendarDate = 'solar-linear-CalendarDate';
    case LinearStopwatch = 'solar-linear-Stopwatch';
    case LinearAlarmPause = 'solar-linear-AlarmPause';
    case LinearAlarmTurnOff = 'solar-linear-AlarmTurnOff';
    case LinearClockSquare = 'solar-linear-ClockSquare';
    case LinearStopwatchPause = 'solar-linear-StopwatchPause';
    case LinearCalendarMinimalistic = 'solar-linear-CalendarMinimalistic';
    case LinearAlarmAdd = 'solar-linear-AlarmAdd';
    case LinearAlarmPlay = 'solar-linear-AlarmPlay';
    case LinearHourglassLine = 'solar-linear-HourglassLine';
    case LinearAlarmSleep = 'solar-linear-AlarmSleep';
    case LinearAlarmRemove = 'solar-linear-AlarmRemove';
    case LinearCalendar = 'solar-linear-Calendar';
    case LinearClockCircle = 'solar-linear-ClockCircle';
    case LinearHistory = 'solar-linear-History';
    case LinearAlarm = 'solar-linear-Alarm';
    case LinearWatchSquare = 'solar-linear-WatchSquare';
    case LinearWatchSquareMinimalistic = 'solar-linear-WatchSquareMinimalistic';
    case LinearMagniferBug = 'solar-linear-MagniferBug';
    case LinearMagnifer = 'solar-linear-Magnifer';
    case LinearMagniferZoomIn = 'solar-linear-MagniferZoomIn';
    case LinearRoundedMagnifer = 'solar-linear-RoundedMagnifer';
    case LinearRoundedMagniferZoomIn = 'solar-linear-RoundedMagniferZoomIn';
    case LinearMinimalisticMagniferBug = 'solar-linear-MinimalisticMagniferBug';
    case LinearRoundedMagniferBug = 'solar-linear-RoundedMagniferBug';
    case LinearMinimalisticMagniferZoomOut = 'solar-linear-MinimalisticMagniferZoomOut';
    case LinearMinimalisticMagnifer = 'solar-linear-MinimalisticMagnifer';
    case LinearRoundedMagniferZoomOut = 'solar-linear-RoundedMagniferZoomOut';
    case LinearMinimalisticMagniferZoomIn = 'solar-linear-MinimalisticMagniferZoomIn';
    case LinearMagniferZoomOut = 'solar-linear-MagniferZoomOut';
    case LinearBagCheck = 'solar-linear-BagCheck';
    case LinearShopMinimalistic = 'solar-linear-ShopMinimalistic';
    case LinearShop = 'solar-linear-Shop';
    case LinearCartCheck = 'solar-linear-CartCheck';
    case LinearCart = 'solar-linear-Cart';
    case LinearCart3 = 'solar-linear-Cart3';
    case LinearCart2 = 'solar-linear-Cart2';
    case LinearBagMusic = 'solar-linear-BagMusic';
    case LinearCartLargeMinimalistic = 'solar-linear-CartLargeMinimalistic';
    case LinearCart5 = 'solar-linear-Cart5';
    case LinearCart4 = 'solar-linear-Cart4';
    case LinearBag = 'solar-linear-Bag';
    case LinearBagHeart = 'solar-linear-BagHeart';
    case LinearCartPlus = 'solar-linear-CartPlus';
    case LinearCartLarge = 'solar-linear-CartLarge';
    case LinearBagCross = 'solar-linear-BagCross';
    case LinearBagMusic2 = 'solar-linear-BagMusic2';
    case LinearBag5 = 'solar-linear-Bag5';
    case LinearBag4 = 'solar-linear-Bag4';
    case LinearCartLarge4 = 'solar-linear-CartLarge4';
    case LinearCartLarge3 = 'solar-linear-CartLarge3';
    case LinearBag3 = 'solar-linear-Bag3';
    case LinearBag2 = 'solar-linear-Bag2';
    case LinearShop2 = 'solar-linear-Shop2';
    case LinearCartLarge2 = 'solar-linear-CartLarge2';
    case LinearBagSmile = 'solar-linear-BagSmile';
    case LinearCartCross = 'solar-linear-CartCross';
    case LinearInfoSquare = 'solar-linear-InfoSquare';
    case LinearFlashlightOn = 'solar-linear-FlashlightOn';
    case LinearXXX = 'solar-linear-XXX';
    case LinearFigma = 'solar-linear-Figma';
    case LinearFlashlight = 'solar-linear-Flashlight';
    case LinearGhost = 'solar-linear-Ghost';
    case LinearCupMusic = 'solar-linear-CupMusic';
    case LinearBatteryFullMinimalistic = 'solar-linear-BatteryFullMinimalistic';
    case LinearDangerCircle = 'solar-linear-DangerCircle';
    case LinearCheckSquare = 'solar-linear-CheckSquare';
    case LinearGhostSmile = 'solar-linear-GhostSmile';
    case LinearTarget = 'solar-linear-Target';
    case LinearBatteryHalfMinimalistic = 'solar-linear-BatteryHalfMinimalistic';
    case LinearScissors = 'solar-linear-Scissors';
    case LinearPinList = 'solar-linear-PinList';
    case LinearBatteryCharge = 'solar-linear-BatteryCharge';
    case LinearUmbrella = 'solar-linear-Umbrella';
    case LinearHomeSmile = 'solar-linear-HomeSmile';
    case LinearHome = 'solar-linear-Home';
    case LinearCopyright = 'solar-linear-Copyright';
    case LinearHomeWifi = 'solar-linear-HomeWifi';
    case LinearTShirt = 'solar-linear-TShirt';
    case LinearBatteryChargeMinimalistic = 'solar-linear-BatteryChargeMinimalistic';
    case LinearCupStar = 'solar-linear-CupStar';
    case LinearSpecialEffects = 'solar-linear-SpecialEffects';
    case LinearBody = 'solar-linear-Body';
    case LinearHamburgerMenu = 'solar-linear-HamburgerMenu';
    case LinearPower = 'solar-linear-Power';
    case LinearDatabase = 'solar-linear-Database';
    case LinearCursorSquare = 'solar-linear-CursorSquare';
    case LinearFuel = 'solar-linear-Fuel';
    case LinearMentionCircle = 'solar-linear-MentionCircle';
    case LinearConfettiMinimalistic = 'solar-linear-ConfettiMinimalistic';
    case LinearMenuDotsCircle = 'solar-linear-MenuDotsCircle';
    case LinearPaw = 'solar-linear-Paw';
    case LinearSubtitles = 'solar-linear-Subtitles';
    case LinearSliderVerticalMinimalistic = 'solar-linear-SliderVerticalMinimalistic';
    case LinearCrownMinimalistic = 'solar-linear-CrownMinimalistic';
    case LinearMenuDots = 'solar-linear-MenuDots';
    case LinearDelivery = 'solar-linear-Delivery';
    case LinearWaterdrop = 'solar-linear-Waterdrop';
    case LinearPerfume = 'solar-linear-Perfume';
    case LinearHomeAngle2 = 'solar-linear-HomeAngle2';
    case LinearHomeWifiAngle = 'solar-linear-HomeWifiAngle';
    case LinearQuestionCircle = 'solar-linear-QuestionCircle';
    case LinearTrashBinMinimalistic = 'solar-linear-TrashBinMinimalistic';
    case LinearMagicStick3 = 'solar-linear-MagicStick3';
    case LinearAddSquare = 'solar-linear-AddSquare';
    case LinearCrownStar = 'solar-linear-CrownStar';
    case LinearMagnet = 'solar-linear-Magnet';
    case LinearConfetti = 'solar-linear-Confetti';
    case LinearPin = 'solar-linear-Pin';
    case LinearMinusSquare = 'solar-linear-MinusSquare';
    case LinearBolt = 'solar-linear-Bolt';
    case LinearCloseCircle = 'solar-linear-CloseCircle';
    case LinearForbiddenCircle = 'solar-linear-ForbiddenCircle';
    case LinearMagicStick2 = 'solar-linear-MagicStick2';
    case LinearCrownLine = 'solar-linear-CrownLine';
    case LinearBoltCircle = 'solar-linear-BoltCircle';
    case LinearFlag = 'solar-linear-Flag';
    case LinearSliderHorizontal = 'solar-linear-SliderHorizontal';
    case LinearHighDefinition = 'solar-linear-HighDefinition';
    case LinearCursor = 'solar-linear-Cursor';
    case LinearFeed = 'solar-linear-Feed';
    case LinearTrafficEconomy = 'solar-linear-TrafficEconomy';
    case LinearAugmentedReality = 'solar-linear-AugmentedReality';
    case LinearIcon4K = 'solar-linear-Icon4K';
    case LinearMagnetWave = 'solar-linear-MagnetWave';
    case LinearHomeSmileAngle = 'solar-linear-HomeSmileAngle';
    case LinearSliderVertical = 'solar-linear-SliderVertical';
    case LinearCheckCircle = 'solar-linear-CheckCircle';
    case LinearCopy = 'solar-linear-Copy';
    case LinearDangerSquare = 'solar-linear-DangerSquare';
    case LinearSkirt = 'solar-linear-Skirt';
    case LinearGlasses = 'solar-linear-Glasses';
    case LinearHomeAdd = 'solar-linear-HomeAdd';
    case LinearSledgehammer = 'solar-linear-Sledgehammer';
    case LinearInfoCircle = 'solar-linear-InfoCircle';
    case LinearDangerTriangle = 'solar-linear-DangerTriangle';
    case LinearPinCircle = 'solar-linear-PinCircle';
    case LinearSmartHome = 'solar-linear-SmartHome';
    case LinearScissorsSquare = 'solar-linear-ScissorsSquare';
    case LinearSleeping = 'solar-linear-Sleeping';
    case LinearBox = 'solar-linear-Box';
    case LinearCrown = 'solar-linear-Crown';
    case LinearBroom = 'solar-linear-Broom';
    case LinearPostsCarouselHorizontal = 'solar-linear-PostsCarouselHorizontal';
    case LinearFlag2 = 'solar-linear-Flag2';
    case LinearPlate = 'solar-linear-Plate';
    case LinearTrashBinTrash = 'solar-linear-TrashBinTrash';
    case LinearCupFirst = 'solar-linear-CupFirst';
    case LinearSmartHomeAngle = 'solar-linear-SmartHomeAngle';
    case LinearPaperBin = 'solar-linear-PaperBin';
    case LinearBoxMinimalistic = 'solar-linear-BoxMinimalistic';
    case LinearDanger = 'solar-linear-Danger';
    case LinearMenuDotsSquare = 'solar-linear-MenuDotsSquare';
    case LinearHanger2 = 'solar-linear-Hanger2';
    case LinearBatteryHalf = 'solar-linear-BatteryHalf';
    case LinearHome2 = 'solar-linear-Home2';
    case LinearPostsCarouselVertical = 'solar-linear-PostsCarouselVertical';
    case LinearRevote = 'solar-linear-Revote';
    case LinearMentionSquare = 'solar-linear-MentionSquare';
    case LinearWinRar = 'solar-linear-WinRar';
    case LinearForbidden = 'solar-linear-Forbidden';
    case LinearQuestionSquare = 'solar-linear-QuestionSquare';
    case LinearHanger = 'solar-linear-Hanger';
    case LinearReorder = 'solar-linear-Reorder';
    case LinearHomeAddAngle = 'solar-linear-HomeAddAngle';
    case LinearMasks = 'solar-linear-Masks';
    case LinearGift = 'solar-linear-Gift';
    case LinearCreativeCommons = 'solar-linear-CreativeCommons';
    case LinearSliderMinimalisticHorizontal = 'solar-linear-SliderMinimalisticHorizontal';
    case LinearHomeAngle = 'solar-linear-HomeAngle';
    case LinearBatteryLowMinimalistic = 'solar-linear-BatteryLowMinimalistic';
    case LinearShare = 'solar-linear-Share';
    case LinearTrashBin2 = 'solar-linear-TrashBin2';
    case LinearSort = 'solar-linear-Sort';
    case LinearMinusCircle = 'solar-linear-MinusCircle';
    case LinearExplicit = 'solar-linear-Explicit';
    case LinearTraffic = 'solar-linear-Traffic';
    case LinearFilter = 'solar-linear-Filter';
    case LinearCloseSquare = 'solar-linear-CloseSquare';
    case LinearAddCircle = 'solar-linear-AddCircle';
    case LinearFerrisWheel = 'solar-linear-FerrisWheel';
    case LinearCup = 'solar-linear-Cup';
    case LinearBalloon = 'solar-linear-Balloon';
    case LinearHelp = 'solar-linear-Help';
    case LinearBatteryFull = 'solar-linear-BatteryFull';
    case LinearCat = 'solar-linear-Cat';
    case LinearMaskSad = 'solar-linear-MaskSad';
    case LinearHighQuality = 'solar-linear-HighQuality';
    case LinearMagicStick = 'solar-linear-MagicStick';
    case LinearCosmetic = 'solar-linear-Cosmetic';
    case LinearBatteryLow = 'solar-linear-BatteryLow';
    case LinearShareCircle = 'solar-linear-ShareCircle';
    case LinearMaskHapply = 'solar-linear-MaskHapply';
    case LinearAccessibility = 'solar-linear-Accessibility';
    case LinearTrashBinMinimalistic2 = 'solar-linear-TrashBinMinimalistic2';
    case LinearIncomingCallRounded = 'solar-linear-IncomingCallRounded';
    case LinearCallDropped = 'solar-linear-CallDropped';
    case LinearCallChat = 'solar-linear-CallChat';
    case LinearCallCancelRounded = 'solar-linear-CallCancelRounded';
    case LinearCallMedicineRounded = 'solar-linear-CallMedicineRounded';
    case LinearCallDroppedRounded = 'solar-linear-CallDroppedRounded';
    case LinearRecordSquare = 'solar-linear-RecordSquare';
    case LinearPhoneCalling = 'solar-linear-PhoneCalling';
    case LinearPhoneRounded = 'solar-linear-PhoneRounded';
    case LinearCallMedicine = 'solar-linear-CallMedicine';
    case LinearRecordMinimalistic = 'solar-linear-RecordMinimalistic';
    case LinearEndCall = 'solar-linear-EndCall';
    case LinearOutgoingCall = 'solar-linear-OutgoingCall';
    case LinearRecordCircle = 'solar-linear-RecordCircle';
    case LinearIncomingCall = 'solar-linear-IncomingCall';
    case LinearCallChatRounded = 'solar-linear-CallChatRounded';
    case LinearEndCallRounded = 'solar-linear-EndCallRounded';
    case LinearPhone = 'solar-linear-Phone';
    case LinearOutgoingCallRounded = 'solar-linear-OutgoingCallRounded';
    case LinearCallCancel = 'solar-linear-CallCancel';
    case LinearPhoneCallingRounded = 'solar-linear-PhoneCallingRounded';
    case LinearStationMinimalistic = 'solar-linear-StationMinimalistic';
    case LinearSidebarCode = 'solar-linear-SidebarCode';
    case LinearWiFiRouterMinimalistic = 'solar-linear-WiFiRouterMinimalistic';
    case LinearUSB = 'solar-linear-USB';
    case LinearSiderbar = 'solar-linear-Siderbar';
    case LinearCode2 = 'solar-linear-Code2';
    case LinearSlashCircle = 'solar-linear-SlashCircle';
    case LinearScreencast = 'solar-linear-Screencast';
    case LinearHashtagSquare = 'solar-linear-HashtagSquare';
    case LinearSidebarMinimalistic = 'solar-linear-SidebarMinimalistic';
    case LinearCode = 'solar-linear-Code';
    case LinearUsbSquare = 'solar-linear-UsbSquare';
    case LinearWiFiRouter = 'solar-linear-WiFiRouter';
    case LinearCodeCircle = 'solar-linear-CodeCircle';
    case LinearTranslation = 'solar-linear-Translation';
    case LinearBugMinimalistic = 'solar-linear-BugMinimalistic';
    case LinearStation = 'solar-linear-Station';
    case LinearProgramming = 'solar-linear-Programming';
    case LinearWiFiRouterRound = 'solar-linear-WiFiRouterRound';
    case LinearHashtag = 'solar-linear-Hashtag';
    case LinearBug = 'solar-linear-Bug';
    case LinearHashtagChat = 'solar-linear-HashtagChat';
    case LinearCommand = 'solar-linear-Command';
    case LinearTranslation2 = 'solar-linear-Translation2';
    case LinearHashtagCircle = 'solar-linear-HashtagCircle';
    case LinearScreencast2 = 'solar-linear-Screencast2';
    case LinearSlashSquare = 'solar-linear-SlashSquare';
    case LinearWindowFrame = 'solar-linear-WindowFrame';
    case LinearStructure = 'solar-linear-Structure';
    case LinearUsbCircle = 'solar-linear-UsbCircle';
    case LinearCodeSquare = 'solar-linear-CodeSquare';
    case LinearNotes = 'solar-linear-Notes';
    case LinearDocumentText = 'solar-linear-DocumentText';
    case LinearDocumentAdd = 'solar-linear-DocumentAdd';
    case LinearDocumentMedicine = 'solar-linear-DocumentMedicine';
    case LinearArchiveMinimalistic = 'solar-linear-ArchiveMinimalistic';
    case LinearClipboard = 'solar-linear-Clipboard';
    case LinearClipboardAdd = 'solar-linear-ClipboardAdd';
    case LinearArchive = 'solar-linear-Archive';
    case LinearClipboardHeart = 'solar-linear-ClipboardHeart';
    case LinearClipboardRemove = 'solar-linear-ClipboardRemove';
    case LinearClipboardText = 'solar-linear-ClipboardText';
    case LinearDocument = 'solar-linear-Document';
    case LinearNotesMinimalistic = 'solar-linear-NotesMinimalistic';
    case LinearArchiveUp = 'solar-linear-ArchiveUp';
    case LinearArchiveUpMinimlistic = 'solar-linear-ArchiveUpMinimlistic';
    case LinearArchiveCheck = 'solar-linear-ArchiveCheck';
    case LinearArchiveDown = 'solar-linear-ArchiveDown';
    case LinearArchiveDownMinimlistic = 'solar-linear-ArchiveDownMinimlistic';
    case LinearDocumentsMinimalistic = 'solar-linear-DocumentsMinimalistic';
    case LinearClipboardCheck = 'solar-linear-ClipboardCheck';
    case LinearClipboardList = 'solar-linear-ClipboardList';
    case LinearDocuments = 'solar-linear-Documents';
    case LinearNotebook = 'solar-linear-Notebook';
    case LinearGalleryRound = 'solar-linear-GalleryRound';
    case LinearPlayCircle = 'solar-linear-PlayCircle';
    case LinearStream = 'solar-linear-Stream';
    case LinearGalleryRemove = 'solar-linear-GalleryRemove';
    case LinearClapperboard = 'solar-linear-Clapperboard';
    case LinearPauseCircle = 'solar-linear-PauseCircle';
    case LinearRewind5SecondsBack = 'solar-linear-Rewind5SecondsBack';
    case LinearRepeat = 'solar-linear-Repeat';
    case LinearClapperboardEdit = 'solar-linear-ClapperboardEdit';
    case LinearVideoFrameCut = 'solar-linear-VideoFrameCut';
    case LinearPanorama = 'solar-linear-Panorama';
    case LinearPlayStream = 'solar-linear-PlayStream';
    case LinearClapperboardOpen = 'solar-linear-ClapperboardOpen';
    case LinearClapperboardText = 'solar-linear-ClapperboardText';
    case LinearLibrary = 'solar-linear-Library';
    case LinearReel2 = 'solar-linear-Reel2';
    case LinearVolumeSmall = 'solar-linear-VolumeSmall';
    case LinearVideoFrame = 'solar-linear-VideoFrame';
    case LinearMicrophoneLarge = 'solar-linear-MicrophoneLarge';
    case LinearRewindForward = 'solar-linear-RewindForward';
    case LinearRewindBackCircle = 'solar-linear-RewindBackCircle';
    case LinearMicrophone = 'solar-linear-Microphone';
    case LinearVideoFrameReplace = 'solar-linear-VideoFrameReplace';
    case LinearClapperboardPlay = 'solar-linear-ClapperboardPlay';
    case LinearGalleryDownload = 'solar-linear-GalleryDownload';
    case LinearMusicNote4 = 'solar-linear-MusicNote4';
    case LinearVideocameraRecord = 'solar-linear-VideocameraRecord';
    case LinearPlaybackSpeed = 'solar-linear-PlaybackSpeed';
    case LinearSoundwave = 'solar-linear-Soundwave';
    case LinearStopCircle = 'solar-linear-StopCircle';
    case LinearQuitFullScreenCircle = 'solar-linear-QuitFullScreenCircle';
    case LinearRewindBack = 'solar-linear-RewindBack';
    case LinearRepeatOne = 'solar-linear-RepeatOne';
    case LinearGalleryCheck = 'solar-linear-GalleryCheck';
    case LinearWallpaper = 'solar-linear-Wallpaper';
    case LinearRewindForwardCircle = 'solar-linear-RewindForwardCircle';
    case LinearGalleryEdit = 'solar-linear-GalleryEdit';
    case LinearGallery = 'solar-linear-Gallery';
    case LinearGalleryMinimalistic = 'solar-linear-GalleryMinimalistic';
    case LinearUploadTrack = 'solar-linear-UploadTrack';
    case LinearVolume = 'solar-linear-Volume';
    case LinearUploadTrack2 = 'solar-linear-UploadTrack2';
    case LinearMusicNotes = 'solar-linear-MusicNotes';
    case LinearMusicNote2 = 'solar-linear-MusicNote2';
    case LinearCameraAdd = 'solar-linear-CameraAdd';
    case LinearPodcast = 'solar-linear-Podcast';
    case LinearCameraRotate = 'solar-linear-CameraRotate';
    case LinearMusicNote3 = 'solar-linear-MusicNote3';
    case LinearStop = 'solar-linear-Stop';
    case LinearMuted = 'solar-linear-Muted';
    case LinearSkipNext = 'solar-linear-SkipNext';
    case LinearGallerySend = 'solar-linear-GallerySend';
    case LinearRecord = 'solar-linear-Record';
    case LinearFullScreenCircle = 'solar-linear-FullScreenCircle';
    case LinearVolumeCross = 'solar-linear-VolumeCross';
    case LinearSoundwaveCircle = 'solar-linear-SoundwaveCircle';
    case LinearSkipPrevious = 'solar-linear-SkipPrevious';
    case LinearRewind5SecondsForward = 'solar-linear-Rewind5SecondsForward';
    case LinearPlay = 'solar-linear-Play';
    case LinearPIP = 'solar-linear-PIP';
    case LinearMusicLibrary = 'solar-linear-MusicLibrary';
    case LinearVideoFrame2 = 'solar-linear-VideoFrame2';
    case LinearCamera = 'solar-linear-Camera';
    case LinearQuitPip = 'solar-linear-QuitPip';
    case LinearClapperboardOpenPlay = 'solar-linear-ClapperboardOpenPlay';
    case LinearRewind10SecondsBack = 'solar-linear-Rewind10SecondsBack';
    case LinearRepeatOneMinimalistic = 'solar-linear-RepeatOneMinimalistic';
    case LinearVinyl = 'solar-linear-Vinyl';
    case LinearVideoLibrary = 'solar-linear-VideoLibrary';
    case LinearGalleryWide = 'solar-linear-GalleryWide';
    case LinearReel = 'solar-linear-Reel';
    case LinearToPip = 'solar-linear-ToPip';
    case LinearPip2 = 'solar-linear-Pip2';
    case LinearFullScreen = 'solar-linear-FullScreen';
    case LinearCameraMinimalistic = 'solar-linear-CameraMinimalistic';
    case LinearVideoFrameCut2 = 'solar-linear-VideoFrameCut2';
    case LinearGalleryCircle = 'solar-linear-GalleryCircle';
    case LinearVideoFramePlayHorizontal = 'solar-linear-VideoFramePlayHorizontal';
    case LinearMusicNoteSlider2 = 'solar-linear-MusicNoteSlider2';
    case LinearMusicNoteSlider = 'solar-linear-MusicNoteSlider';
    case LinearVideocameraAdd = 'solar-linear-VideocameraAdd';
    case LinearQuitFullScreenSquare = 'solar-linear-QuitFullScreenSquare';
    case LinearAlbum = 'solar-linear-Album';
    case LinearGalleryAdd = 'solar-linear-GalleryAdd';
    case LinearCameraSquare = 'solar-linear-CameraSquare';
    case LinearRewind15SecondsBack = 'solar-linear-Rewind15SecondsBack';
    case LinearRewind15SecondsForward = 'solar-linear-Rewind15SecondsForward';
    case LinearVinylRecord = 'solar-linear-VinylRecord';
    case LinearShuffle = 'solar-linear-Shuffle';
    case LinearPause = 'solar-linear-Pause';
    case LinearMusicNote = 'solar-linear-MusicNote';
    case LinearQuitFullScreen = 'solar-linear-QuitFullScreen';
    case LinearMicrophone2 = 'solar-linear-Microphone2';
    case LinearVideocamera = 'solar-linear-Videocamera';
    case LinearGalleryFavourite = 'solar-linear-GalleryFavourite';
    case LinearMusicLibrary2 = 'solar-linear-MusicLibrary2';
    case LinearVideoFramePlayVertical = 'solar-linear-VideoFramePlayVertical';
    case LinearFullScreenSquare = 'solar-linear-FullScreenSquare';
    case LinearRewind10SecondsForward = 'solar-linear-Rewind10SecondsForward';
    case LinearVolumeLoud = 'solar-linear-VolumeLoud';
    case LinearMicrophone3 = 'solar-linear-Microphone3';
    case LinearSoundwaveSquare = 'solar-linear-SoundwaveSquare';
    case LinearCardholder = 'solar-linear-Cardholder';
    case LinearBillList = 'solar-linear-BillList';
    case LinearSaleSquare = 'solar-linear-SaleSquare';
    case LinearDollar = 'solar-linear-Dollar';
    case LinearTicket = 'solar-linear-Ticket';
    case LinearTag = 'solar-linear-Tag';
    case LinearCashOut = 'solar-linear-CashOut';
    case LinearWallet2 = 'solar-linear-Wallet2';
    case LinearRuble = 'solar-linear-Ruble';
    case LinearCardTransfer = 'solar-linear-CardTransfer';
    case LinearEuro = 'solar-linear-Euro';
    case LinearSale = 'solar-linear-Sale';
    case LinearCardSearch = 'solar-linear-CardSearch';
    case LinearWallet = 'solar-linear-Wallet';
    case LinearBillCross = 'solar-linear-BillCross';
    case LinearTicketSale = 'solar-linear-TicketSale';
    case LinearSafeSquare = 'solar-linear-SafeSquare';
    case LinearCard = 'solar-linear-Card';
    case LinearSafe2 = 'solar-linear-Safe2';
    case LinearDollarMinimalistic = 'solar-linear-DollarMinimalistic';
    case LinearTagPrice = 'solar-linear-TagPrice';
    case LinearMoneyBag = 'solar-linear-MoneyBag';
    case LinearBill = 'solar-linear-Bill';
    case LinearCardSend = 'solar-linear-CardSend';
    case LinearCardRecive = 'solar-linear-CardRecive';
    case LinearBanknote2 = 'solar-linear-Banknote2';
    case LinearTagHorizontal = 'solar-linear-TagHorizontal';
    case LinearBillCheck = 'solar-linear-BillCheck';
    case LinearTickerStar = 'solar-linear-TickerStar';
    case LinearBanknote = 'solar-linear-Banknote';
    case LinearVerifiedCheck = 'solar-linear-VerifiedCheck';
    case LinearWadOfMoney = 'solar-linear-WadOfMoney';
    case LinearCard2 = 'solar-linear-Card2';
    case LinearSafeCircle = 'solar-linear-SafeCircle';
    case LinearWalletMoney = 'solar-linear-WalletMoney';
    case LinearList = 'solar-linear-List';
    case LinearListDownMinimalistic = 'solar-linear-ListDownMinimalistic';
    case LinearPlaylist2 = 'solar-linear-Playlist2';
    case LinearChecklistMinimalistic = 'solar-linear-ChecklistMinimalistic';
    case LinearPlaaylistMinimalistic = 'solar-linear-PlaaylistMinimalistic';
    case LinearListHeart = 'solar-linear-ListHeart';
    case LinearListArrowDown = 'solar-linear-ListArrowDown';
    case LinearListArrowUp = 'solar-linear-ListArrowUp';
    case LinearListUpMinimalistic = 'solar-linear-ListUpMinimalistic';
    case LinearPlaylist = 'solar-linear-Playlist';
    case LinearListUp = 'solar-linear-ListUp';
    case LinearListCrossMinimalistic = 'solar-linear-ListCrossMinimalistic';
    case LinearListCross = 'solar-linear-ListCross';
    case LinearListArrowDownMinimalistic = 'solar-linear-ListArrowDownMinimalistic';
    case LinearSortByAlphabet = 'solar-linear-SortByAlphabet';
    case LinearChecklist = 'solar-linear-Checklist';
    case LinearSortFromBottomToTop = 'solar-linear-SortFromBottomToTop';
    case LinearListCheck = 'solar-linear-ListCheck';
    case LinearPlaylistMinimalistic2 = 'solar-linear-PlaylistMinimalistic2';
    case LinearPlaylistMinimalistic3 = 'solar-linear-PlaylistMinimalistic3';
    case LinearList1 = 'solar-linear-List1';
    case LinearSortFromTopToBottom = 'solar-linear-SortFromTopToBottom';
    case LinearSortByTime = 'solar-linear-SortByTime';
    case LinearListDown = 'solar-linear-ListDown';
    case LinearListHeartMinimalistic = 'solar-linear-ListHeartMinimalistic';
    case LinearListCheckMinimalistic = 'solar-linear-ListCheckMinimalistic';
    case LinearListArrowUpMinimalistic = 'solar-linear-ListArrowUpMinimalistic';
    case LinearUserCrossRounded = 'solar-linear-UserCrossRounded';
    case LinearUser = 'solar-linear-User';
    case LinearUsersGroupRounded = 'solar-linear-UsersGroupRounded';
    case LinearUserPlusRounded = 'solar-linear-UserPlusRounded';
    case LinearUserBlock = 'solar-linear-UserBlock';
    case LinearUserMinus = 'solar-linear-UserMinus';
    case LinearUserHands = 'solar-linear-UserHands';
    case LinearUserHeart = 'solar-linear-UserHeart';
    case LinearUserMinusRounded = 'solar-linear-UserMinusRounded';
    case LinearUserCross = 'solar-linear-UserCross';
    case LinearUserSpeakRounded = 'solar-linear-UserSpeakRounded';
    case LinearUserId = 'solar-linear-UserId';
    case LinearUserBlockRounded = 'solar-linear-UserBlockRounded';
    case LinearUserHeartRounded = 'solar-linear-UserHeartRounded';
    case LinearUsersGroupTwoRounded = 'solar-linear-UsersGroupTwoRounded';
    case LinearUserHandUp = 'solar-linear-UserHandUp';
    case LinearUserCircle = 'solar-linear-UserCircle';
    case LinearUserRounded = 'solar-linear-UserRounded';
    case LinearUserCheck = 'solar-linear-UserCheck';
    case LinearUserPlus = 'solar-linear-UserPlus';
    case LinearUserCheckRounded = 'solar-linear-UserCheckRounded';
    case LinearUserSpeak = 'solar-linear-UserSpeak';
    case LinearVirus = 'solar-linear-Virus';
    case LinearAdhesivePlaster2 = 'solar-linear-AdhesivePlaster2';
    case LinearDropper = 'solar-linear-Dropper';
    case LinearPulse2 = 'solar-linear-Pulse2';
    case LinearBoneBroken = 'solar-linear-BoneBroken';
    case LinearHeartPulse2 = 'solar-linear-HeartPulse2';
    case LinearMedicalKit = 'solar-linear-MedicalKit';
    case LinearTestTube = 'solar-linear-TestTube';
    case LinearHealth = 'solar-linear-Health';
    case LinearDropperMinimalistic2 = 'solar-linear-DropperMinimalistic2';
    case LinearDNA = 'solar-linear-DNA';
    case LinearDropper3 = 'solar-linear-Dropper3';
    case LinearThermometer = 'solar-linear-Thermometer';
    case LinearDropper2 = 'solar-linear-Dropper2';
    case LinearJarOfPills2 = 'solar-linear-JarOfPills2';
    case LinearBoneCrack = 'solar-linear-BoneCrack';
    case LinearJarOfPills = 'solar-linear-JarOfPills';
    case LinearSyringe = 'solar-linear-Syringe';
    case LinearStethoscope = 'solar-linear-Stethoscope';
    case LinearBenzeneRing = 'solar-linear-BenzeneRing';
    case LinearBacteria = 'solar-linear-Bacteria';
    case LinearAdhesivePlaster = 'solar-linear-AdhesivePlaster';
    case LinearBone = 'solar-linear-Bone';
    case LinearBones = 'solar-linear-Bones';
    case LinearPill = 'solar-linear-Pill';
    case LinearPills = 'solar-linear-Pills';
    case LinearHeartPulse = 'solar-linear-HeartPulse';
    case LinearTestTubeMinimalistic = 'solar-linear-TestTubeMinimalistic';
    case LinearPills2 = 'solar-linear-Pills2';
    case LinearPulse = 'solar-linear-Pulse';
    case LinearDropperMinimalistic = 'solar-linear-DropperMinimalistic';
    case LinearPills3 = 'solar-linear-Pills3';
    case LinearWhisk = 'solar-linear-Whisk';
    case LinearBottle = 'solar-linear-Bottle';
    case LinearOvenMittsMinimalistic = 'solar-linear-OvenMittsMinimalistic';
    case LinearChefHatMinimalistic = 'solar-linear-ChefHatMinimalistic';
    case LinearTeaCup = 'solar-linear-TeaCup';
    case LinearWineglassTriangle = 'solar-linear-WineglassTriangle';
    case LinearOvenMitts = 'solar-linear-OvenMitts';
    case LinearCupPaper = 'solar-linear-CupPaper';
    case LinearLadle = 'solar-linear-Ladle';
    case LinearCorkscrew = 'solar-linear-Corkscrew';
    case LinearDonutBitten = 'solar-linear-DonutBitten';
    case LinearWineglass = 'solar-linear-Wineglass';
    case LinearDonut = 'solar-linear-Donut';
    case LinearCupHot = 'solar-linear-CupHot';
    case LinearChefHatHeart = 'solar-linear-ChefHatHeart';
    case LinearChefHat = 'solar-linear-ChefHat';
    case LinearRollingPin = 'solar-linear-RollingPin';
    case LinearCodeFile = 'solar-linear-CodeFile';
    case LinearFileCorrupted = 'solar-linear-FileCorrupted';
    case LinearFile = 'solar-linear-File';
    case LinearFileRight = 'solar-linear-FileRight';
    case LinearFileFavourite = 'solar-linear-FileFavourite';
    case LinearFileDownload = 'solar-linear-FileDownload';
    case LinearZipFile = 'solar-linear-ZipFile';
    case LinearFileText = 'solar-linear-FileText';
    case LinearFileSmile = 'solar-linear-FileSmile)';
    case LinearFileCheck = 'solar-linear-FileCheck';
    case LinearFileSend = 'solar-linear-FileSend';
    case LinearFileLeft = 'solar-linear-FileLeft';
    case LinearFigmaFile = 'solar-linear-FigmaFile';
    case LinearFileRemove = 'solar-linear-FileRemove';
    case LinearCloudFile = 'solar-linear-CloudFile';
    case LinearSuspension = 'solar-linear-Suspension';
    case LinearSpedometerMax = 'solar-linear-SpedometerMax';
    case LinearTransmissionCircle = 'solar-linear-TransmissionCircle';
    case LinearGasStation = 'solar-linear-GasStation';
    case LinearWheel = 'solar-linear-Wheel';
    case LinearTransmission = 'solar-linear-Transmission';
    case LinearKickScooter = 'solar-linear-KickScooter';
    case LinearSpedometerLow = 'solar-linear-SpedometerLow';
    case LinearSpedometerMiddle = 'solar-linear-SpedometerMiddle';
    case LinearWheelAngle = 'solar-linear-WheelAngle';
    case LinearTram = 'solar-linear-Tram';
    case LinearTransmissionSquare = 'solar-linear-TransmissionSquare';
    case LinearScooter = 'solar-linear-Scooter';
    case LinearShockAbsorber = 'solar-linear-ShockAbsorber';
    case LinearBus = 'solar-linear-Bus';
    case LinearSuspensionCross = 'solar-linear-SuspensionCross';
    case LinearSuspensionBolt = 'solar-linear-SuspensionBolt';
    case LinearElectricRefueling = 'solar-linear-ElectricRefueling';
    case LinearAccumulator = 'solar-linear-Accumulator';
    case LinearHandPills = 'solar-linear-HandPills';
    case LinearHandMoney = 'solar-linear-HandMoney';
    case LinearHandShake = 'solar-linear-HandShake';
    case LinearHandHeart = 'solar-linear-HandHeart';
    case LinearHandStars = 'solar-linear-HandStars';
    case LinearRemoveFolder = 'solar-linear-RemoveFolder';
    case LinearFolderFavouritestar = 'solar-linear-FolderFavourite(star)';
    case LinearAddFolder = 'solar-linear-AddFolder';
    case LinearFolderCheck = 'solar-linear-FolderCheck';
    case LinearFolderFavouritebookmark = 'solar-linear-FolderFavourite(bookmark)';
    case LinearFolder2 = 'solar-linear-Folder2';
    case LinearFolderSecurity = 'solar-linear-FolderSecurity';
    case LinearFolderCloud = 'solar-linear-FolderCloud';
    case LinearMoveToFolder = 'solar-linear-MoveToFolder';
    case LinearFolderError = 'solar-linear-FolderError';
    case LinearFolderPathConnect = 'solar-linear-FolderPathConnect';
    case LinearFolderOpen = 'solar-linear-FolderOpen';
    case LinearFolder = 'solar-linear-Folder';
    case LinearFolderWithFiles = 'solar-linear-FolderWithFiles';
    case LinearCloudCheck = 'solar-linear-CloudCheck';
    case LinearTemperature = 'solar-linear-Temperature';
    case LinearWind = 'solar-linear-Wind';
    case LinearCloudSnowfall = 'solar-linear-CloudSnowfall';
    case LinearSunrise = 'solar-linear-Sunrise';
    case LinearSun2 = 'solar-linear-Sun2';
    case LinearCloudSun = 'solar-linear-CloudSun';
    case LinearCloudBoltMinimalistic = 'solar-linear-CloudBoltMinimalistic';
    case LinearCloudDownload = 'solar-linear-CloudDownload';
    case LinearClouds = 'solar-linear-Clouds';
    case LinearTornado = 'solar-linear-Tornado';
    case LinearMoonSleep = 'solar-linear-MoonSleep';
    case LinearCloudUpload = 'solar-linear-CloudUpload';
    case LinearCloudRain = 'solar-linear-CloudRain';
    case LinearFog = 'solar-linear-Fog';
    case LinearSnowflake = 'solar-linear-Snowflake';
    case LinearMoonFog = 'solar-linear-MoonFog';
    case LinearCloudMinus = 'solar-linear-CloudMinus';
    case LinearCloudBolt = 'solar-linear-CloudBolt';
    case LinearCloudWaterdrop = 'solar-linear-CloudWaterdrop';
    case LinearSunset = 'solar-linear-Sunset';
    case LinearWaterdrops = 'solar-linear-Waterdrops';
    case LinearMoonStars = 'solar-linear-MoonStars';
    case LinearCloudPlus = 'solar-linear-CloudPlus';
    case LinearSun = 'solar-linear-Sun';
    case LinearCloudWaterdrops = 'solar-linear-CloudWaterdrops';
    case LinearCloudSun2 = 'solar-linear-CloudSun2';
    case LinearCloudyMoon = 'solar-linear-CloudyMoon';
    case LinearTornadoSmall = 'solar-linear-TornadoSmall';
    case LinearCloud = 'solar-linear-Cloud';
    case LinearSunFog = 'solar-linear-SunFog';
    case LinearCloundCross = 'solar-linear-CloundCross';
    case LinearCloudSnowfallMinimalistic = 'solar-linear-CloudSnowfallMinimalistic';
    case LinearCloudStorm = 'solar-linear-CloudStorm';
    case LinearMoon = 'solar-linear-Moon';
    case LinearRefreshCircle = 'solar-linear-RefreshCircle';
    case LinearSquareArrowRightDown = 'solar-linear-SquareArrowRightDown';
    case LinearRoundArrowLeftDown = 'solar-linear-RoundArrowLeftDown';
    case LinearRestart = 'solar-linear-Restart';
    case LinearRoundAltArrowDown = 'solar-linear-RoundAltArrowDown';
    case LinearRoundSortVertical = 'solar-linear-RoundSortVertical';
    case LinearSquareAltArrowUp = 'solar-linear-SquareAltArrowUp';
    case LinearArrowLeftUp = 'solar-linear-ArrowLeftUp';
    case LinearSortHorizontal = 'solar-linear-SortHorizontal';
    case LinearTransferHorizontal = 'solar-linear-TransferHorizontal';
    case LinearSquareDoubleAltArrowUp = 'solar-linear-SquareDoubleAltArrowUp';
    case LinearRoundArrowLeftUp = 'solar-linear-RoundArrowLeftUp';
    case LinearAltArrowRight = 'solar-linear-AltArrowRight';
    case LinearRoundDoubleAltArrowUp = 'solar-linear-RoundDoubleAltArrowUp';
    case LinearRestartCircle = 'solar-linear-RestartCircle';
    case LinearSquareArrowDown = 'solar-linear-SquareArrowDown';
    case LinearSortVertical = 'solar-linear-SortVertical';
    case LinearSquareSortHorizontal = 'solar-linear-SquareSortHorizontal';
    case LinearDoubleAltArrowLeft = 'solar-linear-DoubleAltArrowLeft';
    case LinearSquareAltArrowDown = 'solar-linear-SquareAltArrowDown';
    case LinearSquareAltArrowRight = 'solar-linear-SquareAltArrowRight';
    case LinearSquareArrowUp = 'solar-linear-SquareArrowUp';
    case LinearDoubleAltArrowRight = 'solar-linear-DoubleAltArrowRight';
    case LinearRoundTransferVertical = 'solar-linear-RoundTransferVertical';
    case LinearArrowLeft = 'solar-linear-ArrowLeft';
    case LinearRoundDoubleAltArrowRight = 'solar-linear-RoundDoubleAltArrowRight';
    case LinearSquareDoubleAltArrowLeft = 'solar-linear-SquareDoubleAltArrowLeft';
    case LinearAltArrowDown = 'solar-linear-AltArrowDown';
    case LinearRoundTransferHorizontal = 'solar-linear-RoundTransferHorizontal';
    case LinearRoundArrowRightDown = 'solar-linear-RoundArrowRightDown';
    case LinearArrowUp = 'solar-linear-ArrowUp';
    case LinearRoundArrowLeft = 'solar-linear-RoundArrowLeft';
    case LinearDoubleAltArrowUp = 'solar-linear-DoubleAltArrowUp';
    case LinearRoundArrowRight = 'solar-linear-RoundArrowRight';
    case LinearSquareTransferHorizontal = 'solar-linear-SquareTransferHorizontal';
    case LinearArrowRight = 'solar-linear-ArrowRight';
    case LinearRoundDoubleAltArrowLeft = 'solar-linear-RoundDoubleAltArrowLeft';
    case LinearRoundArrowUp = 'solar-linear-RoundArrowUp';
    case LinearSquareSortVertical = 'solar-linear-SquareSortVertical';
    case LinearAltArrowLeft = 'solar-linear-AltArrowLeft';
    case LinearSquareDoubleAltArrowRight = 'solar-linear-SquareDoubleAltArrowRight';
    case LinearRefresh = 'solar-linear-Refresh';
    case LinearTransferVertical = 'solar-linear-TransferVertical';
    case LinearRefreshSquare = 'solar-linear-RefreshSquare';
    case LinearSquareTransferVertical = 'solar-linear-SquareTransferVertical';
    case LinearSquareDoubleAltArrowDown = 'solar-linear-SquareDoubleAltArrowDown';
    case LinearRoundArrowRightUp = 'solar-linear-RoundArrowRightUp';
    case LinearArrowDown = 'solar-linear-ArrowDown';
    case LinearRestartSquare = 'solar-linear-RestartSquare';
    case LinearSquareArrowRight = 'solar-linear-SquareArrowRight';
    case LinearRoundDoubleAltArrowDown = 'solar-linear-RoundDoubleAltArrowDown';
    case LinearSquareArrowLeftUp = 'solar-linear-SquareArrowLeftUp';
    case LinearRoundArrowDown = 'solar-linear-RoundArrowDown';
    case LinearSquareArrowRightUp = 'solar-linear-SquareArrowRightUp';
    case LinearRoundTransferDiagonal = 'solar-linear-RoundTransferDiagonal';
    case LinearArrowRightDown = 'solar-linear-ArrowRightDown';
    case LinearArrowLeftDown = 'solar-linear-ArrowLeftDown';
    case LinearRoundAltArrowLeft = 'solar-linear-RoundAltArrowLeft';
    case LinearArrowRightUp = 'solar-linear-ArrowRightUp';
    case LinearSquareArrowLeftDown = 'solar-linear-SquareArrowLeftDown';
    case LinearRoundAltArrowUp = 'solar-linear-RoundAltArrowUp';
    case LinearAltArrowUp = 'solar-linear-AltArrowUp';
    case LinearSquareAltArrowLeft = 'solar-linear-SquareAltArrowLeft';
    case LinearRoundSortHorizontal = 'solar-linear-RoundSortHorizontal';
    case LinearDoubleAltArrowDown = 'solar-linear-DoubleAltArrowDown';
    case LinearRoundAltArrowRight = 'solar-linear-RoundAltArrowRight';
    case LinearSquareArrowLeft = 'solar-linear-SquareArrowLeft';
    case LinearTuningSquare2 = 'solar-linear-TuningSquare2';
    case LinearWidgetAdd = 'solar-linear-WidgetAdd';
    case LinearTuningSquare = 'solar-linear-TuningSquare';
    case LinearSettingsMinimalistic = 'solar-linear-SettingsMinimalistic';
    case LinearWidget6 = 'solar-linear-Widget6';
    case LinearWidget4 = 'solar-linear-Widget4';
    case LinearSettings = 'solar-linear-Settings';
    case LinearWidget5 = 'solar-linear-Widget5';
    case LinearWidget2 = 'solar-linear-Widget2';
    case LinearWidget3 = 'solar-linear-Widget3';
    case LinearTuning2 = 'solar-linear-Tuning2';
    case LinearTuning3 = 'solar-linear-Tuning3';
    case LinearWidget = 'solar-linear-Widget';
    case LinearTuning4 = 'solar-linear-Tuning4';
    case LinearTuning = 'solar-linear-Tuning';
    case LinearDiagramDown = 'solar-linear-DiagramDown';
    case LinearChart2 = 'solar-linear-Chart2';
    case LinearChart = 'solar-linear-Chart';
    case LinearDiagramUp = 'solar-linear-DiagramUp';
    case LinearGraphNew = 'solar-linear-GraphNew';
    case LinearCourseUp = 'solar-linear-CourseUp';
    case LinearGraphDownNew = 'solar-linear-GraphDownNew';
    case LinearPieChart3 = 'solar-linear-PieChart3';
    case LinearPieChart2 = 'solar-linear-PieChart2';
    case LinearGraphNewUp = 'solar-linear-GraphNewUp';
    case LinearPieChart = 'solar-linear-PieChart';
    case LinearRoundGraph = 'solar-linear-RoundGraph';
    case LinearGraphUp = 'solar-linear-GraphUp';
    case LinearChartSquare = 'solar-linear-ChartSquare';
    case LinearCourseDown = 'solar-linear-CourseDown';
    case LinearChatSquare2 = 'solar-linear-ChatSquare2';
    case LinearGraphDown = 'solar-linear-GraphDown';
    case LinearGraph = 'solar-linear-Graph';
    case LinearPresentationGraph = 'solar-linear-PresentationGraph';
    case LinearMaximizeSquare3 = 'solar-linear-MaximizeSquare3';
    case LinearMaximizeSquareMinimalistic = 'solar-linear-MaximizeSquareMinimalistic';
    case LinearMaximizeSquare2 = 'solar-linear-MaximizeSquare2';
    case LinearMinimizeSquare = 'solar-linear-MinimizeSquare';
    case LinearDownloadSquare = 'solar-linear-DownloadSquare';
    case LinearUndoLeftRoundSquare = 'solar-linear-UndoLeftRoundSquare';
    case LinearReply = 'solar-linear-Reply';
    case LinearLogout = 'solar-linear-Logout';
    case LinearReciveSquare = 'solar-linear-ReciveSquare';
    case LinearExport = 'solar-linear-Export';
    case LinearSendTwiceSquare = 'solar-linear-SendTwiceSquare';
    case LinearUndoLeftRound = 'solar-linear-UndoLeftRound';
    case LinearForward2 = 'solar-linear-Forward2';
    case LinearMaximize = 'solar-linear-Maximize';
    case LinearUndoRightRound = 'solar-linear-UndoRightRound';
    case LinearMinimizeSquare2 = 'solar-linear-MinimizeSquare2';
    case LinearMinimizeSquare3 = 'solar-linear-MinimizeSquare3';
    case LinearUploadTwiceSquare = 'solar-linear-UploadTwiceSquare';
    case LinearMinimize = 'solar-linear-Minimize';
    case LinearCircleTopUp = 'solar-linear-CircleTopUp';
    case LinearUploadMinimalistic = 'solar-linear-UploadMinimalistic';
    case LinearDownload = 'solar-linear-Download';
    case LinearImport = 'solar-linear-Import';
    case LinearLogin = 'solar-linear-Login';
    case LinearUndoLeft = 'solar-linear-UndoLeft';
    case LinearSquareTopUp = 'solar-linear-SquareTopUp';
    case LinearDownloadTwiceSquare = 'solar-linear-DownloadTwiceSquare';
    case LinearCircleBottomDown = 'solar-linear-CircleBottomDown';
    case LinearMaximizeSquare = 'solar-linear-MaximizeSquare';
    case LinearUploadSquare = 'solar-linear-UploadSquare';
    case LinearUndoRightSquare = 'solar-linear-UndoRightSquare';
    case LinearReciveTwiceSquare = 'solar-linear-ReciveTwiceSquare';
    case LinearCircleTopDown = 'solar-linear-CircleTopDown';
    case LinearArrowToDownLeft = 'solar-linear-ArrowToDownLeft';
    case LinearLogout2 = 'solar-linear-Logout2';
    case LinearLogout3 = 'solar-linear-Logout3';
    case LinearScale = 'solar-linear-Scale';
    case LinearArrowToDownRight = 'solar-linear-ArrowToDownRight';
    case LinearDownloadMinimalistic = 'solar-linear-DownloadMinimalistic';
    case LinearMinimizeSquareMinimalistic = 'solar-linear-MinimizeSquareMinimalistic';
    case LinearReply2 = 'solar-linear-Reply2';
    case LinearSquareBottomUp = 'solar-linear-SquareBottomUp';
    case LinearUndoRight = 'solar-linear-UndoRight';
    case LinearUndoLeftSquare = 'solar-linear-UndoLeftSquare';
    case LinearSendSquare = 'solar-linear-SendSquare';
    case LinearExit = 'solar-linear-Exit';
    case LinearSquareBottomDown = 'solar-linear-SquareBottomDown';
    case LinearUndoRightRoundSquare = 'solar-linear-UndoRightRoundSquare';
    case LinearArrowToTopLeft = 'solar-linear-ArrowToTopLeft';
    case LinearCircleBottomUp = 'solar-linear-CircleBottomUp';
    case LinearScreenShare = 'solar-linear-ScreenShare';
    case LinearUpload = 'solar-linear-Upload';
    case LinearSquareTopDown = 'solar-linear-SquareTopDown';
    case LinearArrowToTopRight = 'solar-linear-ArrowToTopRight';
    case LinearLogin3 = 'solar-linear-Login3';
    case LinearLogin2 = 'solar-linear-Login2';
    case LinearCity = 'solar-linear-City';
    case LinearBuildings = 'solar-linear-Buildings';
    case LinearBuildings3 = 'solar-linear-Buildings3';
    case LinearBuildings2 = 'solar-linear-Buildings2';
    case LinearHospital = 'solar-linear-Hospital';
    case LinearGarage = 'solar-linear-Garage';
    case LinearPassport = 'solar-linear-Passport';
    case LinearDiplomaVerified = 'solar-linear-DiplomaVerified';
    case LinearCaseRound = 'solar-linear-CaseRound';
    case LinearBackpack = 'solar-linear-Backpack';
    case LinearBook2 = 'solar-linear-Book2';
    case LinearSquareAcademicCap2 = 'solar-linear-SquareAcademicCap2';
    case LinearCaseRoundMinimalistic = 'solar-linear-CaseRoundMinimalistic';
    case LinearCase = 'solar-linear-Case';
    case LinearBookBookmarkMinimalistic = 'solar-linear-BookBookmarkMinimalistic';
    case LinearBookmarkOpened = 'solar-linear-BookmarkOpened';
    case LinearDiploma = 'solar-linear-Diploma';
    case LinearBook = 'solar-linear-Book';
    case LinearSquareAcademicCap = 'solar-linear-SquareAcademicCap';
    case LinearBookmarkCircle = 'solar-linear-BookmarkCircle';
    case LinearCalculatorMinimalistic = 'solar-linear-CalculatorMinimalistic';
    case LinearNotebookSquare = 'solar-linear-NotebookSquare';
    case LinearBookMinimalistic = 'solar-linear-BookMinimalistic';
    case LinearCaseMinimalistic = 'solar-linear-CaseMinimalistic';
    case LinearNotebookBookmark = 'solar-linear-NotebookBookmark';
    case LinearPassportMinimalistic = 'solar-linear-PassportMinimalistic';
    case LinearBookBookmark = 'solar-linear-BookBookmark';
    case LinearBookmarkSquareMinimalistic = 'solar-linear-BookmarkSquareMinimalistic';
    case LinearBookmark = 'solar-linear-Bookmark';
    case LinearPlusMinus = 'solar-linear-PlusMinus';
    case LinearCalculator = 'solar-linear-Calculator';
    case LinearBookmarkSquare = 'solar-linear-BookmarkSquare';
    case LinearNotebookMinimalistic = 'solar-linear-NotebookMinimalistic';
    case LinearFireSquare = 'solar-linear-FireSquare';
    case LinearSuitcaseLines = 'solar-linear-SuitcaseLines';
    case LinearFire = 'solar-linear-Fire';
    case LinearBonfire = 'solar-linear-Bonfire';
    case LinearSuitcaseTag = 'solar-linear-SuitcaseTag';
    case LinearLeaf = 'solar-linear-Leaf';
    case LinearSuitcase = 'solar-linear-Suitcase';
    case LinearFlame = 'solar-linear-Flame';
    case LinearFireMinimalistic = 'solar-linear-FireMinimalistic';
    case LinearBellBing = 'solar-linear-BellBing';
    case LinearNotificationLinesRemove = 'solar-linear-NotificationLinesRemove';
    case LinearNotificationUnread = 'solar-linear-NotificationUnread';
    case LinearBell = 'solar-linear-Bell';
    case LinearNotificationRemove = 'solar-linear-NotificationRemove';
    case LinearNotificationUnreadLines = 'solar-linear-NotificationUnreadLines';
    case LinearBellOff = 'solar-linear-BellOff';
    case LinearLightning = 'solar-linear-Lightning';
    case LinearLightbulbMinimalistic = 'solar-linear-LightbulbMinimalistic';
    case LinearServerSquareCloud = 'solar-linear-ServerSquareCloud';
    case LinearLightbulbBolt = 'solar-linear-LightbulbBolt';
    case LinearAirbudsCharge = 'solar-linear-AirbudsCharge';
    case LinearServerPath = 'solar-linear-ServerPath';
    case LinearSimCardMinimalistic = 'solar-linear-SimCardMinimalistic';
    case LinearSmartphone = 'solar-linear-Smartphone';
    case LinearTurntable = 'solar-linear-Turntable';
    case LinearAirbudsCheck = 'solar-linear-AirbudsCheck';
    case LinearMouseMinimalistic = 'solar-linear-MouseMinimalistic';
    case LinearSmartphoneRotateAngle = 'solar-linear-SmartphoneRotateAngle';
    case LinearRadioMinimalistic = 'solar-linear-RadioMinimalistic';
    case LinearAirbuds = 'solar-linear-Airbuds';
    case LinearSmartphoneRotateOrientation = 'solar-linear-SmartphoneRotateOrientation';
    case LinearIPhone = 'solar-linear-IPhone';
    case LinearSimCard = 'solar-linear-SimCard';
    case LinearFlashDrive = 'solar-linear-FlashDrive';
    case LinearDevices = 'solar-linear-Devices';
    case LinearSimCards = 'solar-linear-SimCards';
    case LinearAirbudsCaseOpen = 'solar-linear-AirbudsCaseOpen';
    case LinearTurntableMusicNote = 'solar-linear-TurntableMusicNote';
    case LinearKeyboard = 'solar-linear-Keyboard';
    case LinearGamepadCharge = 'solar-linear-GamepadCharge';
    case LinearBoombox = 'solar-linear-Boombox';
    case LinearSmartSpeakerMinimalistic = 'solar-linear-SmartSpeakerMinimalistic';
    case LinearTelescope = 'solar-linear-Telescope';
    case LinearMonitorCamera = 'solar-linear-MonitorCamera';
    case LinearLaptopMinimalistic = 'solar-linear-LaptopMinimalistic';
    case LinearServer2 = 'solar-linear-Server2';
    case LinearSmartSpeaker = 'solar-linear-SmartSpeaker';
    case LinearProjector = 'solar-linear-Projector';
    case LinearServer = 'solar-linear-Server';
    case LinearTV = 'solar-linear-TV';
    case LinearCassette2 = 'solar-linear-Cassette2';
    case LinearRadio = 'solar-linear-Radio';
    case LinearSmartphoneVibration = 'solar-linear-SmartphoneVibration';
    case LinearAirbudsLeft = 'solar-linear-AirbudsLeft';
    case LinearHeadphonesRound = 'solar-linear-HeadphonesRound';
    case LinearGameboy = 'solar-linear-Gameboy';
    case LinearHeadphonesRoundSound = 'solar-linear-HeadphonesRoundSound';
    case LinearCPU = 'solar-linear-CPU';
    case LinearPrinter2 = 'solar-linear-Printer2';
    case LinearHeadphonesSquare = 'solar-linear-HeadphonesSquare';
    case LinearServerSquareUpdate = 'solar-linear-ServerSquareUpdate';
    case LinearPrinterMinimalistic = 'solar-linear-PrinterMinimalistic';
    case LinearBluetooth = 'solar-linear-Bluetooth';
    case LinearWirelessCharge = 'solar-linear-WirelessCharge';
    case LinearBluetoothCircle = 'solar-linear-BluetoothCircle';
    case LinearAirbudsCaseMinimalistic = 'solar-linear-AirbudsCaseMinimalistic';
    case LinearLightbulb = 'solar-linear-Lightbulb';
    case LinearAirbudsRemove = 'solar-linear-AirbudsRemove';
    case LinearSmartphoneRotate2 = 'solar-linear-SmartphoneRotate2';
    case LinearSsdSquare = 'solar-linear-SsdSquare';
    case LinearPrinter = 'solar-linear-Printer';
    case LinearSmartphone2 = 'solar-linear-Smartphone2';
    case LinearServerMinimalistic = 'solar-linear-ServerMinimalistic';
    case LinearHeadphonesSquareSound = 'solar-linear-HeadphonesSquareSound';
    case LinearDiskette = 'solar-linear-Diskette';
    case LinearBluetoothWave = 'solar-linear-BluetoothWave';
    case LinearSmartSpeaker2 = 'solar-linear-SmartSpeaker2';
    case LinearLaptop3 = 'solar-linear-Laptop3';
    case LinearLaptop2 = 'solar-linear-Laptop2';
    case LinearMouseCircle = 'solar-linear-MouseCircle';
    case LinearTurntableMinimalistic = 'solar-linear-TurntableMinimalistic';
    case LinearSmartphoneUpdate = 'solar-linear-SmartphoneUpdate';
    case LinearGamepadMinimalistic = 'solar-linear-GamepadMinimalistic';
    case LinearSdCard = 'solar-linear-SdCard';
    case LinearPlugCircle = 'solar-linear-PlugCircle';
    case LinearAirbudsCase = 'solar-linear-AirbudsCase';
    case LinearSsdRound = 'solar-linear-SsdRound';
    case LinearLaptop = 'solar-linear-Laptop';
    case LinearAirbudsRight = 'solar-linear-AirbudsRight';
    case LinearDisplay = 'solar-linear-Display';
    case LinearMonitorSmartphone = 'solar-linear-MonitorSmartphone';
    case LinearSocket = 'solar-linear-Socket';
    case LinearGamepadOld = 'solar-linear-GamepadOld';
    case LinearCpuBolt = 'solar-linear-CpuBolt';
    case LinearAirbudsCaseCharge = 'solar-linear-AirbudsCaseCharge';
    case LinearTablet = 'solar-linear-Tablet';
    case LinearWeigher = 'solar-linear-Weigher';
    case LinearServerSquare = 'solar-linear-ServerSquare';
    case LinearMouse = 'solar-linear-Mouse';
    case LinearGamepadNoCharge = 'solar-linear-GamepadNoCharge';
    case LinearBluetoothSquare = 'solar-linear-BluetoothSquare';
    case LinearCloudStorage = 'solar-linear-CloudStorage';
    case LinearGamepad = 'solar-linear-Gamepad';
    case LinearMonitor = 'solar-linear-Monitor';
    case LinearCassette = 'solar-linear-Cassette';
    // Broken Style (1183 icons)
    case BrokenFacemaskCircle = 'solar-broken-FacemaskCircle';
    case BrokenConfoundedCircle = 'solar-broken-ConfoundedCircle';
    case BrokenSadSquare = 'solar-broken-SadSquare';
    case BrokenSleepingCircle = 'solar-broken-SleepingCircle';
    case BrokenFaceScanCircle = 'solar-broken-FaceScanCircle';
    case BrokenSmileCircle = 'solar-broken-SmileCircle';
    case BrokenStickerSmileCircle = 'solar-broken-StickerSmileCircle';
    case BrokenStickerSquare = 'solar-broken-StickerSquare';
    case BrokenEmojiFunnyCircle = 'solar-broken-EmojiFunnyCircle';
    case BrokenExpressionlessSquare = 'solar-broken-ExpressionlessSquare';
    case BrokenSleepingSquare = 'solar-broken-SleepingSquare';
    case BrokenSadCircle = 'solar-broken-SadCircle';
    case BrokenFacemaskSquare = 'solar-broken-FacemaskSquare';
    case BrokenConfoundedSquare = 'solar-broken-ConfoundedSquare';
    case BrokenFaceScanSquare = 'solar-broken-FaceScanSquare';
    case BrokenSmileSquare = 'solar-broken-SmileSquare';
    case BrokenStickerSmileCircle2 = 'solar-broken-StickerSmileCircle2';
    case BrokenStickerSmileSquare = 'solar-broken-StickerSmileSquare';
    case BrokenEmojiFunnySquare = 'solar-broken-EmojiFunnySquare';
    case BrokenStickerCircle = 'solar-broken-StickerCircle';
    case BrokenExpressionlessCircle = 'solar-broken-ExpressionlessCircle';
    case BrokenLike = 'solar-broken-Like';
    case BrokenMedalStarSquare = 'solar-broken-MedalStarSquare';
    case BrokenDislike = 'solar-broken-Dislike';
    case BrokenStarShine = 'solar-broken-StarShine';
    case BrokenHeartAngle = 'solar-broken-HeartAngle';
    case BrokenMedalRibbon = 'solar-broken-MedalRibbon';
    case BrokenHeartShine = 'solar-broken-HeartShine';
    case BrokenMedalStarCircle = 'solar-broken-MedalStarCircle';
    case BrokenMedalRibbonsStar = 'solar-broken-MedalRibbonsStar';
    case BrokenStar = 'solar-broken-Star';
    case BrokenHeartUnlock = 'solar-broken-HeartUnlock';
    case BrokenMedalRibbonStar = 'solar-broken-MedalRibbonStar';
    case BrokenHeartLock = 'solar-broken-HeartLock';
    case BrokenHeartBroken = 'solar-broken-HeartBroken';
    case BrokenHearts = 'solar-broken-Hearts';
    case BrokenMedalStar = 'solar-broken-MedalStar';
    case BrokenHeart = 'solar-broken-Heart';
    case BrokenCloset = 'solar-broken-Closet';
    case BrokenBed = 'solar-broken-Bed';
    case BrokenWashingMachine = 'solar-broken-WashingMachine';
    case BrokenBedsideTable = 'solar-broken-BedsideTable';
    case BrokenSofa3 = 'solar-broken-Sofa3';
    case BrokenSofa2 = 'solar-broken-Sofa2';
    case BrokenChair2 = 'solar-broken-Chair2';
    case BrokenBath = 'solar-broken-Bath';
    case BrokenSmartVacuumCleaner2 = 'solar-broken-SmartVacuumCleaner2';
    case BrokenCondicioner = 'solar-broken-Condicioner';
    case BrokenSmartVacuumCleaner = 'solar-broken-SmartVacuumCleaner';
    case BrokenRemoteController2 = 'solar-broken-RemoteController2';
    case BrokenFloorLampMinimalistic = 'solar-broken-FloorLampMinimalistic';
    case BrokenLamp = 'solar-broken-Lamp';
    case BrokenBarChair = 'solar-broken-BarChair';
    case BrokenBedsideTable2 = 'solar-broken-BedsideTable2';
    case BrokenCloset2 = 'solar-broken-Closet2';
    case BrokenBedsideTable3 = 'solar-broken-BedsideTable3';
    case BrokenSpeaker = 'solar-broken-Speaker';
    case BrokenVolumeKnob = 'solar-broken-VolumeKnob';
    case BrokenArmchair = 'solar-broken-Armchair';
    case BrokenSpeakerMinimalistic = 'solar-broken-SpeakerMinimalistic';
    case BrokenRemoteController = 'solar-broken-RemoteController';
    case BrokenTrellis = 'solar-broken-Trellis';
    case BrokenFloorLamp = 'solar-broken-FloorLamp';
    case BrokenCondicioner2 = 'solar-broken-Condicioner2';
    case BrokenBedsideTable4 = 'solar-broken-BedsideTable4';
    case BrokenArmchair2 = 'solar-broken-Armchair2';
    case BrokenWashingMachineMinimalistic = 'solar-broken-WashingMachineMinimalistic';
    case BrokenChair = 'solar-broken-Chair';
    case BrokenRemoteControllerMinimalistic = 'solar-broken-RemoteControllerMinimalistic';
    case BrokenChandelier = 'solar-broken-Chandelier';
    case BrokenFridge = 'solar-broken-Fridge';
    case BrokenMirror = 'solar-broken-Mirror';
    case BrokenSofa = 'solar-broken-Sofa';
    case BrokenEarth = 'solar-broken-Earth';
    case BrokenStarsLine = 'solar-broken-StarsLine';
    case BrokenStarFall2 = 'solar-broken-StarFall2';
    case BrokenStarFall = 'solar-broken-StarFall';
    case BrokenBlackHole3 = 'solar-broken-BlackHole3';
    case BrokenWomen = 'solar-broken-Women';
    case BrokenBlackHole = 'solar-broken-BlackHole';
    case BrokenStarRings = 'solar-broken-StarRings';
    case BrokenBlackHole2 = 'solar-broken-BlackHole2';
    case BrokenStarFallMinimalistic2 = 'solar-broken-StarFallMinimalistic2';
    case BrokenPlanet = 'solar-broken-Planet';
    case BrokenSatellite = 'solar-broken-Satellite';
    case BrokenMen = 'solar-broken-Men';
    case BrokenRocket2 = 'solar-broken-Rocket2';
    case BrokenStars = 'solar-broken-Stars';
    case BrokenStarAngle = 'solar-broken-StarAngle';
    case BrokenInfinity = 'solar-broken-Infinity';
    case BrokenUfo2 = 'solar-broken-Ufo2';
    case BrokenUfo3 = 'solar-broken-Ufo3';
    case BrokenStarRing = 'solar-broken-StarRing';
    case BrokenPlanet2 = 'solar-broken-Planet2';
    case BrokenPlanet3 = 'solar-broken-Planet3';
    case BrokenAsteroid = 'solar-broken-Asteroid';
    case BrokenStarsMinimalistic = 'solar-broken-StarsMinimalistic';
    case BrokenUFO = 'solar-broken-UFO';
    case BrokenPlanet4 = 'solar-broken-Planet4';
    case BrokenRocket = 'solar-broken-Rocket';
    case BrokenStarFallMinimalistic = 'solar-broken-StarFallMinimalistic';
    case BrokenStarRainbow = 'solar-broken-StarRainbow';
    case BrokenAtom = 'solar-broken-Atom';
    case BrokenStarCircle = 'solar-broken-StarCircle';
    case BrokenCompassBig = 'solar-broken-CompassBig';
    case BrokenMapPointSchool = 'solar-broken-MapPointSchool';
    case BrokenSignpost = 'solar-broken-Signpost';
    case BrokenMapArrowDown = 'solar-broken-MapArrowDown';
    case BrokenMap = 'solar-broken-Map';
    case BrokenMapArrowUp = 'solar-broken-MapArrowUp';
    case BrokenPointOnMapPerspective = 'solar-broken-PointOnMapPerspective';
    case BrokenRadar = 'solar-broken-Radar';
    case BrokenStreets = 'solar-broken-Streets';
    case BrokenMapPointWave = 'solar-broken-MapPointWave';
    case BrokenPeopleNearby = 'solar-broken-PeopleNearby';
    case BrokenStreetsMapPoint = 'solar-broken-StreetsMapPoint';
    case BrokenMapPointSearch = 'solar-broken-MapPointSearch';
    case BrokenGPS = 'solar-broken-GPS';
    case BrokenMapArrowSquare = 'solar-broken-MapArrowSquare';
    case BrokenBranchingPathsDown = 'solar-broken-BranchingPathsDown';
    case BrokenMapPointRotate = 'solar-broken-MapPointRotate';
    case BrokenGlobal = 'solar-broken-Global';
    case BrokenCompassSquare = 'solar-broken-CompassSquare';
    case BrokenRouting3 = 'solar-broken-Routing3';
    case BrokenRouting2 = 'solar-broken-Routing2';
    case BrokenMapPointRemove = 'solar-broken-MapPointRemove';
    case BrokenGlobus = 'solar-broken-Globus';
    case BrokenSignpost2 = 'solar-broken-Signpost2';
    case BrokenRadar2 = 'solar-broken-Radar2';
    case BrokenStreetsNavigation = 'solar-broken-StreetsNavigation';
    case BrokenMapPoint = 'solar-broken-MapPoint';
    case BrokenMapPointHospital = 'solar-broken-MapPointHospital';
    case BrokenCompass = 'solar-broken-Compass';
    case BrokenMapPointAdd = 'solar-broken-MapPointAdd';
    case BrokenBranchingPathsUp = 'solar-broken-BranchingPathsUp';
    case BrokenMapPointFavourite = 'solar-broken-MapPointFavourite';
    case BrokenRoute = 'solar-broken-Route';
    case BrokenPointOnMap = 'solar-broken-PointOnMap';
    case BrokenMapArrowRight = 'solar-broken-MapArrowRight';
    case BrokenRouting = 'solar-broken-Routing';
    case BrokenMapArrowLeft = 'solar-broken-MapArrowLeft';
    case BrokenIncognito = 'solar-broken-Incognito';
    case BrokenLockPassword = 'solar-broken-LockPassword';
    case BrokenShieldNetwork = 'solar-broken-ShieldNetwork';
    case BrokenKeyMinimalisticSquare = 'solar-broken-KeyMinimalisticSquare';
    case BrokenLockKeyholeUnlocked = 'solar-broken-LockKeyholeUnlocked';
    case BrokenLock = 'solar-broken-Lock';
    case BrokenShieldKeyhole = 'solar-broken-ShieldKeyhole';
    case BrokenEyeClosed = 'solar-broken-EyeClosed';
    case BrokenKey = 'solar-broken-Key';
    case BrokenShieldMinus = 'solar-broken-ShieldMinus';
    case BrokenShield = 'solar-broken-Shield';
    case BrokenLockUnlocked = 'solar-broken-LockUnlocked';
    case BrokenBombMinimalistic = 'solar-broken-BombMinimalistic';
    case BrokenShieldStar = 'solar-broken-ShieldStar';
    case BrokenBomb = 'solar-broken-Bomb';
    case BrokenKeySquare = 'solar-broken-KeySquare';
    case BrokenLockKeyholeMinimalisticUnlocked = 'solar-broken-LockKeyholeMinimalisticUnlocked';
    case BrokenShieldCross = 'solar-broken-ShieldCross';
    case BrokenObjectScan = 'solar-broken-ObjectScan';
    case BrokenPasswordMinimalisticInput = 'solar-broken-PasswordMinimalisticInput';
    case BrokenLockPasswordUnlocked = 'solar-broken-LockPasswordUnlocked';
    case BrokenSiren = 'solar-broken-Siren';
    case BrokenShieldMinimalistic = 'solar-broken-ShieldMinimalistic';
    case BrokenEyeScan = 'solar-broken-EyeScan';
    case BrokenKeyMinimalisticSquare2 = 'solar-broken-KeyMinimalisticSquare2';
    case BrokenScanner2 = 'solar-broken-Scanner2';
    case BrokenKeyMinimalisticSquare3 = 'solar-broken-KeyMinimalisticSquare3';
    case BrokenKeyMinimalistic2 = 'solar-broken-KeyMinimalistic2';
    case BrokenCodeScan = 'solar-broken-CodeScan';
    case BrokenShieldPlus = 'solar-broken-ShieldPlus';
    case BrokenPasswordMinimalistic = 'solar-broken-PasswordMinimalistic';
    case BrokenEye = 'solar-broken-Eye';
    case BrokenQrCode = 'solar-broken-QrCode';
    case BrokenShieldCheck = 'solar-broken-ShieldCheck';
    case BrokenKeyMinimalistic = 'solar-broken-KeyMinimalistic';
    case BrokenLockKeyhole = 'solar-broken-LockKeyhole';
    case BrokenShieldUser = 'solar-broken-ShieldUser';
    case BrokenKeySquare2 = 'solar-broken-KeySquare2';
    case BrokenBombEmoji = 'solar-broken-BombEmoji';
    case BrokenScanner = 'solar-broken-Scanner';
    case BrokenShieldUp = 'solar-broken-ShieldUp';
    case BrokenSirenRounded = 'solar-broken-SirenRounded';
    case BrokenLockKeyholeMinimalistic = 'solar-broken-LockKeyholeMinimalistic';
    case BrokenPassword = 'solar-broken-Password';
    case BrokenShieldKeyholeMinimalistic = 'solar-broken-ShieldKeyholeMinimalistic';
    case BrokenShieldWarning = 'solar-broken-ShieldWarning';
    case BrokenPallete2 = 'solar-broken-Pallete2';
    case BrokenAlignVerticalSpacing = 'solar-broken-AlignVerticalSpacing';
    case BrokenAlignVerticalCenter = 'solar-broken-AlignVerticalCenter';
    case BrokenCropMinimalistic = 'solar-broken-CropMinimalistic';
    case BrokenMirrorRight = 'solar-broken-MirrorRight';
    case BrokenAlignBottom = 'solar-broken-AlignBottom';
    case BrokenRadialBlur = 'solar-broken-RadialBlur';
    case BrokenCrop = 'solar-broken-Crop';
    case BrokenAlignHorizontaSpacing = 'solar-broken-AlignHorizontaSpacing';
    case BrokenRulerPen = 'solar-broken-RulerPen';
    case BrokenThreeSquares = 'solar-broken-ThreeSquares';
    case BrokenPaintRoller = 'solar-broken-PaintRoller';
    case BrokenLayers = 'solar-broken-Layers';
    case BrokenFilters = 'solar-broken-Filters';
    case BrokenRulerCrossPen = 'solar-broken-RulerCrossPen';
    case BrokenFlipHorizontal = 'solar-broken-FlipHorizontal';
    case BrokenAlignLeft = 'solar-broken-AlignLeft';
    case BrokenRuler = 'solar-broken-Ruler';
    case BrokenPalette = 'solar-broken-Palette';
    case BrokenAlignTop = 'solar-broken-AlignTop';
    case BrokenAlignHorizontalCenter = 'solar-broken-AlignHorizontalCenter';
    case BrokenAlignRight = 'solar-broken-AlignRight';
    case BrokenRulerAngular = 'solar-broken-RulerAngular';
    case BrokenPipette = 'solar-broken-Pipette';
    case BrokenFlipVertical = 'solar-broken-FlipVertical';
    case BrokenMirrorLeft = 'solar-broken-MirrorLeft';
    case BrokenLayersMinimalistic = 'solar-broken-LayersMinimalistic';
    case BrokenColourTuneing = 'solar-broken-ColourTuneing';
    case BrokenPaletteRound = 'solar-broken-PaletteRound';
    case BrokenEraser = 'solar-broken-Eraser';
    case BrokenTextItalicCircle = 'solar-broken-TextItalicCircle';
    case BrokenLinkRound = 'solar-broken-LinkRound';
    case BrokenTextItalic = 'solar-broken-TextItalic';
    case BrokenLinkBrokenMinimalistic = 'solar-broken-LinkBrokenMinimalistic';
    case BrokenTextUnderlineCross = 'solar-broken-TextUnderlineCross';
    case BrokenLink = 'solar-broken-Link';
    case BrokenEraserCircle = 'solar-broken-EraserCircle';
    case BrokenLinkCircle = 'solar-broken-LinkCircle';
    case BrokenTextBoldCircle = 'solar-broken-TextBoldCircle';
    case BrokenTextField = 'solar-broken-TextField';
    case BrokenTextSquare = 'solar-broken-TextSquare';
    case BrokenTextSquare2 = 'solar-broken-TextSquare2';
    case BrokenLinkRoundAngle = 'solar-broken-LinkRoundAngle';
    case BrokenTextUnderlineCircle = 'solar-broken-TextUnderlineCircle';
    case BrokenTextCrossCircle = 'solar-broken-TextCrossCircle';
    case BrokenTextItalicSquare = 'solar-broken-TextItalicSquare';
    case BrokenParagraphSpacing = 'solar-broken-ParagraphSpacing';
    case BrokenText = 'solar-broken-Text';
    case BrokenLinkBroken = 'solar-broken-LinkBroken';
    case BrokenTextCross = 'solar-broken-TextCross';
    case BrokenTextUnderline = 'solar-broken-TextUnderline';
    case BrokenLinkMinimalistic = 'solar-broken-LinkMinimalistic';
    case BrokenLinkMinimalistic2 = 'solar-broken-LinkMinimalistic2';
    case BrokenTextBold = 'solar-broken-TextBold';
    case BrokenTextSelection = 'solar-broken-TextSelection';
    case BrokenTextFieldFocus = 'solar-broken-TextFieldFocus';
    case BrokenTextBoldSquare = 'solar-broken-TextBoldSquare';
    case BrokenEraserSquare = 'solar-broken-EraserSquare';
    case BrokenLinkSquare = 'solar-broken-LinkSquare';
    case BrokenTextCircle = 'solar-broken-TextCircle';
    case BrokenBackspace = 'solar-broken-Backspace';
    case BrokenTextCrossSquare = 'solar-broken-TextCrossSquare';
    case BrokenInboxUnread = 'solar-broken-InboxUnread';
    case BrokenChatUnread = 'solar-broken-ChatUnread';
    case BrokenChatRound = 'solar-broken-ChatRound';
    case BrokenUnread = 'solar-broken-Unread';
    case BrokenMailbox = 'solar-broken-Mailbox';
    case BrokenLetter = 'solar-broken-Letter';
    case BrokenPenNewRound = 'solar-broken-PenNewRound';
    case BrokenMultipleForwardRight = 'solar-broken-MultipleForwardRight';
    case BrokenMultipleForwardLeft = 'solar-broken-MultipleForwardLeft';
    case BrokenInboxArchive = 'solar-broken-InboxArchive';
    case BrokenInbox = 'solar-broken-Inbox';
    case BrokenPen2 = 'solar-broken-Pen2';
    case BrokenPenNewSquare = 'solar-broken-PenNewSquare';
    case BrokenPen = 'solar-broken-Pen';
    case BrokenChatDots = 'solar-broken-ChatDots';
    case BrokenChatSquareCall = 'solar-broken-ChatSquareCall';
    case BrokenSquareShareLine = 'solar-broken-SquareShareLine';
    case BrokenChatRoundCheck = 'solar-broken-ChatRoundCheck';
    case BrokenInboxOut = 'solar-broken-InboxOut';
    case BrokenPlain3 = 'solar-broken-Plain3';
    case BrokenChatRoundDots = 'solar-broken-ChatRoundDots';
    case BrokenChatRoundLike = 'solar-broken-ChatRoundLike';
    case BrokenPlain2 = 'solar-broken-Plain2';
    case BrokenChatRoundUnread = 'solar-broken-ChatRoundUnread';
    case BrokenChatSquareLike = 'solar-broken-ChatSquareLike';
    case BrokenPaperclip = 'solar-broken-Paperclip';
    case BrokenChatSquareCheck = 'solar-broken-ChatSquareCheck';
    case BrokenChatSquare = 'solar-broken-ChatSquare';
    case BrokenLetterOpened = 'solar-broken-LetterOpened';
    case BrokenSquareForward = 'solar-broken-SquareForward';
    case BrokenLetterUnread = 'solar-broken-LetterUnread';
    case BrokenPaperclipRounded2 = 'solar-broken-PaperclipRounded2';
    case BrokenChatRoundCall = 'solar-broken-ChatRoundCall';
    case BrokenInboxLine = 'solar-broken-InboxLine';
    case BrokenChatRoundVideo = 'solar-broken-ChatRoundVideo';
    case BrokenChatRoundMoney = 'solar-broken-ChatRoundMoney';
    case BrokenInboxIn = 'solar-broken-InboxIn';
    case BrokenCheckRead = 'solar-broken-CheckRead';
    case BrokenChatRoundLine = 'solar-broken-ChatRoundLine';
    case BrokenForward = 'solar-broken-Forward';
    case BrokenPaperclip2 = 'solar-broken-Paperclip2';
    case BrokenDialog2 = 'solar-broken-Dialog2';
    case BrokenDialog = 'solar-broken-Dialog';
    case BrokenPaperclipRounded = 'solar-broken-PaperclipRounded';
    case BrokenPlain = 'solar-broken-Plain';
    case BrokenChatSquareArrow = 'solar-broken-ChatSquareArrow';
    case BrokenChatSquareCode = 'solar-broken-ChatSquareCode';
    case BrokenChatLine = 'solar-broken-ChatLine';
    case BrokenTennis = 'solar-broken-Tennis';
    case BrokenBicyclingRound = 'solar-broken-BicyclingRound';
    case BrokenBalls = 'solar-broken-Balls';
    case BrokenMeditationRound = 'solar-broken-MeditationRound';
    case BrokenStretchingRound = 'solar-broken-StretchingRound';
    case BrokenDumbbells2 = 'solar-broken-Dumbbells2';
    case BrokenMeditation = 'solar-broken-Meditation';
    case BrokenRunning2 = 'solar-broken-Running2';
    case BrokenRugby = 'solar-broken-Rugby';
    case BrokenBodyShapeMinimalistic = 'solar-broken-BodyShapeMinimalistic';
    case BrokenStretching = 'solar-broken-Stretching';
    case BrokenBowling = 'solar-broken-Bowling';
    case BrokenRanking = 'solar-broken-Ranking';
    case BrokenTreadmillRound = 'solar-broken-TreadmillRound';
    case BrokenVolleyball = 'solar-broken-Volleyball';
    case BrokenDumbbellLargeMinimalistic = 'solar-broken-DumbbellLargeMinimalistic';
    case BrokenRunningRound = 'solar-broken-RunningRound';
    case BrokenHiking = 'solar-broken-Hiking';
    case BrokenHikingMinimalistic = 'solar-broken-HikingMinimalistic';
    case BrokenWaterSun = 'solar-broken-WaterSun';
    case BrokenGolf = 'solar-broken-Golf';
    case BrokenSkateboarding = 'solar-broken-Skateboarding';
    case BrokenDumbbells = 'solar-broken-Dumbbells';
    case BrokenWalkingRound = 'solar-broken-WalkingRound';
    case BrokenRunning = 'solar-broken-Running';
    case BrokenTreadmill = 'solar-broken-Treadmill';
    case BrokenSkateboard = 'solar-broken-Skateboard';
    case BrokenDumbbellSmall = 'solar-broken-DumbbellSmall';
    case BrokenBasketball = 'solar-broken-Basketball';
    case BrokenFootball = 'solar-broken-Football';
    case BrokenDumbbell = 'solar-broken-Dumbbell';
    case BrokenBodyShape = 'solar-broken-BodyShape';
    case BrokenWater = 'solar-broken-Water';
    case BrokenSkateboardingRound = 'solar-broken-SkateboardingRound';
    case BrokenHikingRound = 'solar-broken-HikingRound';
    case BrokenVolleyball2 = 'solar-broken-Volleyball2';
    case BrokenTennis2 = 'solar-broken-Tennis2';
    case BrokenSwimming = 'solar-broken-Swimming';
    case BrokenBicycling = 'solar-broken-Bicycling';
    case BrokenWalking = 'solar-broken-Walking';
    case BrokenDumbbellLarge = 'solar-broken-DumbbellLarge';
    case BrokenCalendarMark = 'solar-broken-CalendarMark';
    case BrokenHistory2 = 'solar-broken-History2';
    case BrokenWatchSquareMinimalisticCharge = 'solar-broken-WatchSquareMinimalisticCharge';
    case BrokenHistory3 = 'solar-broken-History3';
    case BrokenHourglass = 'solar-broken-Hourglass';
    case BrokenCalendarSearch = 'solar-broken-CalendarSearch';
    case BrokenStopwatchPlay = 'solar-broken-StopwatchPlay';
    case BrokenWatchRound = 'solar-broken-WatchRound';
    case BrokenCalendarAdd = 'solar-broken-CalendarAdd';
    case BrokenCalendarDate = 'solar-broken-CalendarDate';
    case BrokenStopwatch = 'solar-broken-Stopwatch';
    case BrokenAlarmPause = 'solar-broken-AlarmPause';
    case BrokenAlarmTurnOff = 'solar-broken-AlarmTurnOff';
    case BrokenClockSquare = 'solar-broken-ClockSquare';
    case BrokenStopwatchPause = 'solar-broken-StopwatchPause';
    case BrokenCalendarMinimalistic = 'solar-broken-CalendarMinimalistic';
    case BrokenAlarmAdd = 'solar-broken-AlarmAdd';
    case BrokenAlarmPlay = 'solar-broken-AlarmPlay';
    case BrokenHourglassLine = 'solar-broken-HourglassLine';
    case BrokenAlarmSleep = 'solar-broken-AlarmSleep';
    case BrokenAlarmRemove = 'solar-broken-AlarmRemove';
    case BrokenCalendar = 'solar-broken-Calendar';
    case BrokenClockCircle = 'solar-broken-ClockCircle';
    case BrokenHistory = 'solar-broken-History';
    case BrokenAlarm = 'solar-broken-Alarm';
    case BrokenWatchSquare = 'solar-broken-WatchSquare';
    case BrokenWatchSquareMinimalistic = 'solar-broken-WatchSquareMinimalistic';
    case BrokenMagniferBug = 'solar-broken-MagniferBug';
    case BrokenMagnifer = 'solar-broken-Magnifer';
    case BrokenMagniferZoomIn = 'solar-broken-MagniferZoomIn';
    case BrokenRoundedMagnifer = 'solar-broken-RoundedMagnifer';
    case BrokenRoundedMagniferZoomIn = 'solar-broken-RoundedMagniferZoomIn';
    case BrokenMinimalisticMagniferBug = 'solar-broken-MinimalisticMagniferBug';
    case BrokenRoundedMagniferBug = 'solar-broken-RoundedMagniferBug';
    case BrokenMinimalisticMagniferZoomOut = 'solar-broken-MinimalisticMagniferZoomOut';
    case BrokenMinimalisticMagnifer = 'solar-broken-MinimalisticMagnifer';
    case BrokenRoundedMagniferZoomOut = 'solar-broken-RoundedMagniferZoomOut';
    case BrokenMinimalisticMagniferZoomIn = 'solar-broken-MinimalisticMagniferZoomIn';
    case BrokenMagniferZoomOut = 'solar-broken-MagniferZoomOut';
    case BrokenBagCheck = 'solar-broken-BagCheck';
    case BrokenShopMinimalistic = 'solar-broken-ShopMinimalistic';
    case BrokenShop = 'solar-broken-Shop';
    case BrokenCartCheck = 'solar-broken-CartCheck';
    case BrokenCart = 'solar-broken-Cart';
    case BrokenCart3 = 'solar-broken-Cart3';
    case BrokenCart2 = 'solar-broken-Cart2';
    case BrokenBagMusic = 'solar-broken-BagMusic';
    case BrokenCartLargeMinimalistic = 'solar-broken-CartLargeMinimalistic';
    case BrokenCart5 = 'solar-broken-Cart5';
    case BrokenCart4 = 'solar-broken-Cart4';
    case BrokenBag = 'solar-broken-Bag';
    case BrokenBagHeart = 'solar-broken-BagHeart';
    case BrokenCartPlus = 'solar-broken-CartPlus';
    case BrokenCartLarge = 'solar-broken-CartLarge';
    case BrokenBagCross = 'solar-broken-BagCross';
    case BrokenBagMusic2 = 'solar-broken-BagMusic2';
    case BrokenBag5 = 'solar-broken-Bag5';
    case BrokenBag4 = 'solar-broken-Bag4';
    case BrokenCartLarge4 = 'solar-broken-CartLarge4';
    case BrokenCartLarge3 = 'solar-broken-CartLarge3';
    case BrokenBag3 = 'solar-broken-Bag3';
    case BrokenBag2 = 'solar-broken-Bag2';
    case BrokenShop2 = 'solar-broken-Shop2';
    case BrokenCartLarge2 = 'solar-broken-CartLarge2';
    case BrokenBagSmile = 'solar-broken-BagSmile';
    case BrokenCartCross = 'solar-broken-CartCross';
    case BrokenInfoSquare = 'solar-broken-InfoSquare';
    case BrokenFlashlightOn = 'solar-broken-FlashlightOn';
    case BrokenXXX = 'solar-broken-XXX';
    case BrokenFigma = 'solar-broken-Figma';
    case BrokenFlashlight = 'solar-broken-Flashlight';
    case BrokenGhost = 'solar-broken-Ghost';
    case BrokenCupMusic = 'solar-broken-CupMusic';
    case BrokenBatteryFullMinimalistic = 'solar-broken-BatteryFullMinimalistic';
    case BrokenDangerCircle = 'solar-broken-DangerCircle';
    case BrokenCheckSquare = 'solar-broken-CheckSquare';
    case BrokenGhostSmile = 'solar-broken-GhostSmile';
    case BrokenTarget = 'solar-broken-Target';
    case BrokenBatteryHalfMinimalistic = 'solar-broken-BatteryHalfMinimalistic';
    case BrokenScissors = 'solar-broken-Scissors';
    case BrokenPinList = 'solar-broken-PinList';
    case BrokenBatteryCharge = 'solar-broken-BatteryCharge';
    case BrokenUmbrella = 'solar-broken-Umbrella';
    case BrokenHomeSmile = 'solar-broken-HomeSmile';
    case BrokenHome = 'solar-broken-Home';
    case BrokenCopyright = 'solar-broken-Copyright';
    case BrokenHomeWifi = 'solar-broken-HomeWifi';
    case BrokenTShirt = 'solar-broken-TShirt';
    case BrokenBatteryChargeMinimalistic = 'solar-broken-BatteryChargeMinimalistic';
    case BrokenCupStar = 'solar-broken-CupStar';
    case BrokenSpecialEffects = 'solar-broken-SpecialEffects';
    case BrokenBody = 'solar-broken-Body';
    case BrokenHamburgerMenu = 'solar-broken-HamburgerMenu';
    case BrokenPower = 'solar-broken-Power';
    case BrokenDatabase = 'solar-broken-Database';
    case BrokenCursorSquare = 'solar-broken-CursorSquare';
    case BrokenFuel = 'solar-broken-Fuel';
    case BrokenMentionCircle = 'solar-broken-MentionCircle';
    case BrokenConfettiMinimalistic = 'solar-broken-ConfettiMinimalistic';
    case BrokenMenuDotsCircle = 'solar-broken-MenuDotsCircle';
    case BrokenPaw = 'solar-broken-Paw';
    case BrokenSubtitles = 'solar-broken-Subtitles';
    case BrokenSliderVerticalMinimalistic = 'solar-broken-SliderVerticalMinimalistic';
    case BrokenCrownMinimalistic = 'solar-broken-CrownMinimalistic';
    case BrokenMenuDots = 'solar-broken-MenuDots';
    case BrokenDelivery = 'solar-broken-Delivery';
    case BrokenWaterdrop = 'solar-broken-Waterdrop';
    case BrokenPerfume = 'solar-broken-Perfume';
    case BrokenHomeAngle2 = 'solar-broken-HomeAngle2';
    case BrokenHomeWifiAngle = 'solar-broken-HomeWifiAngle';
    case BrokenQuestionCircle = 'solar-broken-QuestionCircle';
    case BrokenTrashBinMinimalistic = 'solar-broken-TrashBinMinimalistic';
    case BrokenMagicStick3 = 'solar-broken-MagicStick3';
    case BrokenAddSquare = 'solar-broken-AddSquare';
    case BrokenCrownStar = 'solar-broken-CrownStar';
    case BrokenMagnet = 'solar-broken-Magnet';
    case BrokenConfetti = 'solar-broken-Confetti';
    case BrokenPin = 'solar-broken-Pin';
    case BrokenMinusSquare = 'solar-broken-MinusSquare';
    case BrokenBolt = 'solar-broken-Bolt';
    case BrokenCloseCircle = 'solar-broken-CloseCircle';
    case BrokenForbiddenCircle = 'solar-broken-ForbiddenCircle';
    case BrokenMagicStick2 = 'solar-broken-MagicStick2';
    case BrokenCrownLine = 'solar-broken-CrownLine';
    case BrokenBoltCircle = 'solar-broken-BoltCircle';
    case BrokenFlag = 'solar-broken-Flag';
    case BrokenSliderHorizontal = 'solar-broken-SliderHorizontal';
    case BrokenHighDefinition = 'solar-broken-HighDefinition';
    case BrokenCursor = 'solar-broken-Cursor';
    case BrokenFeed = 'solar-broken-Feed';
    case BrokenTrafficEconomy = 'solar-broken-TrafficEconomy';
    case BrokenAugmentedReality = 'solar-broken-AugmentedReality';
    case BrokenIcon4K = 'solar-broken-Icon4K';
    case BrokenMagnetWave = 'solar-broken-MagnetWave';
    case BrokenHomeSmileAngle = 'solar-broken-HomeSmileAngle';
    case BrokenSliderVertical = 'solar-broken-SliderVertical';
    case BrokenCheckCircle = 'solar-broken-CheckCircle';
    case BrokenCopy = 'solar-broken-Copy';
    case BrokenDangerSquare = 'solar-broken-DangerSquare';
    case BrokenSkirt = 'solar-broken-Skirt';
    case BrokenGlasses = 'solar-broken-Glasses';
    case BrokenHomeAdd = 'solar-broken-HomeAdd';
    case BrokenSledgehammer = 'solar-broken-Sledgehammer';
    case BrokenInfoCircle = 'solar-broken-InfoCircle';
    case BrokenDangerTriangle = 'solar-broken-DangerTriangle';
    case BrokenPinCircle = 'solar-broken-PinCircle';
    case BrokenSmartHome = 'solar-broken-SmartHome';
    case BrokenScissorsSquare = 'solar-broken-ScissorsSquare';
    case BrokenSleeping = 'solar-broken-Sleeping';
    case BrokenBox = 'solar-broken-Box';
    case BrokenCrown = 'solar-broken-Crown';
    case BrokenBroom = 'solar-broken-Broom';
    case BrokenPostsCarouselHorizontal = 'solar-broken-PostsCarouselHorizontal';
    case BrokenFlag2 = 'solar-broken-Flag2';
    case BrokenPlate = 'solar-broken-Plate';
    case BrokenTrashBinTrash = 'solar-broken-TrashBinTrash';
    case BrokenCupFirst = 'solar-broken-CupFirst';
    case BrokenSmartHomeAngle = 'solar-broken-SmartHomeAngle';
    case BrokenPaperBin = 'solar-broken-PaperBin';
    case BrokenBoxMinimalistic = 'solar-broken-BoxMinimalistic';
    case BrokenDanger = 'solar-broken-Danger';
    case BrokenMenuDotsSquare = 'solar-broken-MenuDotsSquare';
    case BrokenHanger2 = 'solar-broken-Hanger2';
    case BrokenBatteryHalf = 'solar-broken-BatteryHalf';
    case BrokenHome2 = 'solar-broken-Home2';
    case BrokenPostsCarouselVertical = 'solar-broken-PostsCarouselVertical';
    case BrokenRevote = 'solar-broken-Revote';
    case BrokenMentionSquare = 'solar-broken-MentionSquare';
    case BrokenWinRar = 'solar-broken-WinRar';
    case BrokenForbidden = 'solar-broken-Forbidden';
    case BrokenQuestionSquare = 'solar-broken-QuestionSquare';
    case BrokenHanger = 'solar-broken-Hanger';
    case BrokenReorder = 'solar-broken-Reorder';
    case BrokenHomeAddAngle = 'solar-broken-HomeAddAngle';
    case BrokenMasks = 'solar-broken-Masks';
    case BrokenGift = 'solar-broken-Gift';
    case BrokenCreativeCommons = 'solar-broken-CreativeCommons';
    case BrokenSliderMinimalisticHorizontal = 'solar-broken-SliderMinimalisticHorizontal';
    case BrokenHomeAngle = 'solar-broken-HomeAngle';
    case BrokenBatteryLowMinimalistic = 'solar-broken-BatteryLowMinimalistic';
    case BrokenShare = 'solar-broken-Share';
    case BrokenTrashBin2 = 'solar-broken-TrashBin2';
    case BrokenSort = 'solar-broken-Sort';
    case BrokenMinusCircle = 'solar-broken-MinusCircle';
    case BrokenExplicit = 'solar-broken-Explicit';
    case BrokenTraffic = 'solar-broken-Traffic';
    case BrokenFilter = 'solar-broken-Filter';
    case BrokenCloseSquare = 'solar-broken-CloseSquare';
    case BrokenAddCircle = 'solar-broken-AddCircle';
    case BrokenFerrisWheel = 'solar-broken-FerrisWheel';
    case BrokenCup = 'solar-broken-Cup';
    case BrokenBalloon = 'solar-broken-Balloon';
    case BrokenHelp = 'solar-broken-Help';
    case BrokenBatteryFull = 'solar-broken-BatteryFull';
    case BrokenCat = 'solar-broken-Cat';
    case BrokenMaskSad = 'solar-broken-MaskSad';
    case BrokenHighQuality = 'solar-broken-HighQuality';
    case BrokenMagicStick = 'solar-broken-MagicStick';
    case BrokenCosmetic = 'solar-broken-Cosmetic';
    case BrokenBatteryLow = 'solar-broken-BatteryLow';
    case BrokenShareCircle = 'solar-broken-ShareCircle';
    case BrokenMaskHapply = 'solar-broken-MaskHapply';
    case BrokenAccessibility = 'solar-broken-Accessibility';
    case BrokenTrashBinMinimalistic2 = 'solar-broken-TrashBinMinimalistic2';
    case BrokenIncomingCallRounded = 'solar-broken-IncomingCallRounded';
    case BrokenCallDropped = 'solar-broken-CallDropped';
    case BrokenCallChat = 'solar-broken-CallChat';
    case BrokenCallCancelRounded = 'solar-broken-CallCancelRounded';
    case BrokenCallMedicineRounded = 'solar-broken-CallMedicineRounded';
    case BrokenCallDroppedRounded = 'solar-broken-CallDroppedRounded';
    case BrokenRecordSquare = 'solar-broken-RecordSquare';
    case BrokenPhoneCalling = 'solar-broken-PhoneCalling';
    case BrokenPhoneRounded = 'solar-broken-PhoneRounded';
    case BrokenCallMedicine = 'solar-broken-CallMedicine';
    case BrokenRecordMinimalistic = 'solar-broken-RecordMinimalistic';
    case BrokenEndCall = 'solar-broken-EndCall';
    case BrokenOutgoingCall = 'solar-broken-OutgoingCall';
    case BrokenRecordCircle = 'solar-broken-RecordCircle';
    case BrokenIncomingCall = 'solar-broken-IncomingCall';
    case BrokenCallChatRounded = 'solar-broken-CallChatRounded';
    case BrokenEndCallRounded = 'solar-broken-EndCallRounded';
    case BrokenPhone = 'solar-broken-Phone';
    case BrokenOutgoingCallRounded = 'solar-broken-OutgoingCallRounded';
    case BrokenCallCancel = 'solar-broken-CallCancel';
    case BrokenPhoneCallingRounded = 'solar-broken-PhoneCallingRounded';
    case BrokenStationMinimalistic = 'solar-broken-StationMinimalistic';
    case BrokenSidebarCode = 'solar-broken-SidebarCode';
    case BrokenWiFiRouterMinimalistic = 'solar-broken-WiFiRouterMinimalistic';
    case BrokenUSB = 'solar-broken-USB';
    case BrokenSiderbar = 'solar-broken-Siderbar';
    case BrokenCode2 = 'solar-broken-Code2';
    case BrokenSlashCircle = 'solar-broken-SlashCircle';
    case BrokenScreencast = 'solar-broken-Screencast';
    case BrokenHashtagSquare = 'solar-broken-HashtagSquare';
    case BrokenSidebarMinimalistic = 'solar-broken-SidebarMinimalistic';
    case BrokenCode = 'solar-broken-Code';
    case BrokenUsbSquare = 'solar-broken-UsbSquare';
    case BrokenWiFiRouter = 'solar-broken-WiFiRouter';
    case BrokenCodeCircle = 'solar-broken-CodeCircle';
    case BrokenTranslation = 'solar-broken-Translation';
    case BrokenBugMinimalistic = 'solar-broken-BugMinimalistic';
    case BrokenStation = 'solar-broken-Station';
    case BrokenProgramming = 'solar-broken-Programming';
    case BrokenWiFiRouterRound = 'solar-broken-WiFiRouterRound';
    case BrokenHashtag = 'solar-broken-Hashtag';
    case BrokenBug = 'solar-broken-Bug';
    case BrokenHashtagChat = 'solar-broken-HashtagChat';
    case BrokenCommand = 'solar-broken-Command';
    case BrokenTranslation2 = 'solar-broken-Translation2';
    case BrokenHashtagCircle = 'solar-broken-HashtagCircle';
    case BrokenScreencast2 = 'solar-broken-Screencast2';
    case BrokenSlashSquare = 'solar-broken-SlashSquare';
    case BrokenWindowFrame = 'solar-broken-WindowFrame';
    case BrokenStructure = 'solar-broken-Structure';
    case BrokenUsbCircle = 'solar-broken-UsbCircle';
    case BrokenCodeSquare = 'solar-broken-CodeSquare';
    case BrokenNotes = 'solar-broken-Notes';
    case BrokenDocumentText = 'solar-broken-DocumentText';
    case BrokenDocumentAdd = 'solar-broken-DocumentAdd';
    case BrokenDocumentMedicine = 'solar-broken-DocumentMedicine';
    case BrokenArchiveMinimalistic = 'solar-broken-ArchiveMinimalistic';
    case BrokenClipboard = 'solar-broken-Clipboard';
    case BrokenClipboardAdd = 'solar-broken-ClipboardAdd';
    case BrokenArchive = 'solar-broken-Archive';
    case BrokenClipboardHeart = 'solar-broken-ClipboardHeart';
    case BrokenClipboardRemove = 'solar-broken-ClipboardRemove';
    case BrokenClipboardText = 'solar-broken-ClipboardText';
    case BrokenDocument = 'solar-broken-Document';
    case BrokenNotesMinimalistic = 'solar-broken-NotesMinimalistic';
    case BrokenArchiveUp = 'solar-broken-ArchiveUp';
    case BrokenArchiveUpMinimlistic = 'solar-broken-ArchiveUpMinimlistic';
    case BrokenArchiveCheck = 'solar-broken-ArchiveCheck';
    case BrokenArchiveDown = 'solar-broken-ArchiveDown';
    case BrokenArchiveDownMinimlistic = 'solar-broken-ArchiveDownMinimlistic';
    case BrokenDocumentsMinimalistic = 'solar-broken-DocumentsMinimalistic';
    case BrokenClipboardCheck = 'solar-broken-ClipboardCheck';
    case BrokenClipboardList = 'solar-broken-ClipboardList';
    case BrokenDocuments = 'solar-broken-Documents';
    case BrokenNotebook = 'solar-broken-Notebook';
    case BrokenGalleryRound = 'solar-broken-GalleryRound';
    case BrokenPlayCircle = 'solar-broken-PlayCircle';
    case BrokenStream = 'solar-broken-Stream';
    case BrokenGalleryRemove = 'solar-broken-GalleryRemove';
    case BrokenClapperboard = 'solar-broken-Clapperboard';
    case BrokenPauseCircle = 'solar-broken-PauseCircle';
    case BrokenRewind5SecondsBack = 'solar-broken-Rewind5SecondsBack';
    case BrokenRepeat = 'solar-broken-Repeat';
    case BrokenClapperboardEdit = 'solar-broken-ClapperboardEdit';
    case BrokenVideoFrameCut = 'solar-broken-VideoFrameCut';
    case BrokenPanorama = 'solar-broken-Panorama';
    case BrokenPlayStream = 'solar-broken-PlayStream';
    case BrokenClapperboardOpen = 'solar-broken-ClapperboardOpen';
    case BrokenClapperboardText = 'solar-broken-ClapperboardText';
    case BrokenLibrary = 'solar-broken-Library';
    case BrokenReel2 = 'solar-broken-Reel2';
    case BrokenVolumeSmall = 'solar-broken-VolumeSmall';
    case BrokenVideoFrame = 'solar-broken-VideoFrame';
    case BrokenMicrophoneLarge = 'solar-broken-MicrophoneLarge';
    case BrokenRewindForward = 'solar-broken-RewindForward';
    case BrokenRewindBackCircle = 'solar-broken-RewindBackCircle';
    case BrokenMicrophone = 'solar-broken-Microphone';
    case BrokenVideoFrameReplace = 'solar-broken-VideoFrameReplace';
    case BrokenClapperboardPlay = 'solar-broken-ClapperboardPlay';
    case BrokenGalleryDownload = 'solar-broken-GalleryDownload';
    case BrokenMusicNote4 = 'solar-broken-MusicNote4';
    case BrokenVideocameraRecord = 'solar-broken-VideocameraRecord';
    case BrokenPlaybackSpeed = 'solar-broken-PlaybackSpeed';
    case BrokenSoundwave = 'solar-broken-Soundwave';
    case BrokenStopCircle = 'solar-broken-StopCircle';
    case BrokenQuitFullScreenCircle = 'solar-broken-QuitFullScreenCircle';
    case BrokenRewindBack = 'solar-broken-RewindBack';
    case BrokenRepeatOne = 'solar-broken-RepeatOne';
    case BrokenGalleryCheck = 'solar-broken-GalleryCheck';
    case BrokenWallpaper = 'solar-broken-Wallpaper';
    case BrokenRewindForwardCircle = 'solar-broken-RewindForwardCircle';
    case BrokenGalleryEdit = 'solar-broken-GalleryEdit';
    case BrokenGallery = 'solar-broken-Gallery';
    case BrokenGalleryMinimalistic = 'solar-broken-GalleryMinimalistic';
    case BrokenUploadTrack = 'solar-broken-UploadTrack';
    case BrokenVolume = 'solar-broken-Volume';
    case BrokenUploadTrack2 = 'solar-broken-UploadTrack2';
    case BrokenMusicNotes = 'solar-broken-MusicNotes';
    case BrokenMusicNote2 = 'solar-broken-MusicNote2';
    case BrokenCameraAdd = 'solar-broken-CameraAdd';
    case BrokenPodcast = 'solar-broken-Podcast';
    case BrokenCameraRotate = 'solar-broken-CameraRotate';
    case BrokenMusicNote3 = 'solar-broken-MusicNote3';
    case BrokenStop = 'solar-broken-Stop';
    case BrokenMuted = 'solar-broken-Muted';
    case BrokenSkipNext = 'solar-broken-SkipNext';
    case BrokenGallerySend = 'solar-broken-GallerySend';
    case BrokenRecord = 'solar-broken-Record';
    case BrokenFullScreenCircle = 'solar-broken-FullScreenCircle';
    case BrokenVolumeCross = 'solar-broken-VolumeCross';
    case BrokenSoundwaveCircle = 'solar-broken-SoundwaveCircle';
    case BrokenSkipPrevious = 'solar-broken-SkipPrevious';
    case BrokenRewind5SecondsForward = 'solar-broken-Rewind5SecondsForward';
    case BrokenPlay = 'solar-broken-Play';
    case BrokenPIP = 'solar-broken-PIP';
    case BrokenMusicLibrary = 'solar-broken-MusicLibrary';
    case BrokenVideoFrame2 = 'solar-broken-VideoFrame2';
    case BrokenCamera = 'solar-broken-Camera';
    case BrokenQuitPip = 'solar-broken-QuitPip';
    case BrokenClapperboardOpenPlay = 'solar-broken-ClapperboardOpenPlay';
    case BrokenRewind10SecondsBack = 'solar-broken-Rewind10SecondsBack';
    case BrokenRepeatOneMinimalistic = 'solar-broken-RepeatOneMinimalistic';
    case BrokenVinyl = 'solar-broken-Vinyl';
    case BrokenVideoLibrary = 'solar-broken-VideoLibrary';
    case BrokenGalleryWide = 'solar-broken-GalleryWide';
    case BrokenReel = 'solar-broken-Reel';
    case BrokenToPip = 'solar-broken-ToPip';
    case BrokenPip2 = 'solar-broken-Pip2';
    case BrokenFullScreen = 'solar-broken-FullScreen';
    case BrokenCameraMinimalistic = 'solar-broken-CameraMinimalistic';
    case BrokenVideoFrameCut2 = 'solar-broken-VideoFrameCut2';
    case BrokenGalleryCircle = 'solar-broken-GalleryCircle';
    case BrokenVideoFramePlayHorizontal = 'solar-broken-VideoFramePlayHorizontal';
    case BrokenMusicNoteSlider2 = 'solar-broken-MusicNoteSlider2';
    case BrokenMusicNoteSlider = 'solar-broken-MusicNoteSlider';
    case BrokenVideocameraAdd = 'solar-broken-VideocameraAdd';
    case BrokenQuitFullScreenSquare = 'solar-broken-QuitFullScreenSquare';
    case BrokenAlbum = 'solar-broken-Album';
    case BrokenGalleryAdd = 'solar-broken-GalleryAdd';
    case BrokenCameraSquare = 'solar-broken-CameraSquare';
    case BrokenRewind15SecondsBack = 'solar-broken-Rewind15SecondsBack';
    case BrokenRewind15SecondsForward = 'solar-broken-Rewind15SecondsForward';
    case BrokenVinylRecord = 'solar-broken-VinylRecord';
    case BrokenShuffle = 'solar-broken-Shuffle';
    case BrokenPause = 'solar-broken-Pause';
    case BrokenMusicNote = 'solar-broken-MusicNote';
    case BrokenQuitFullScreen = 'solar-broken-QuitFullScreen';
    case BrokenMicrophone2 = 'solar-broken-Microphone2';
    case BrokenVideocamera = 'solar-broken-Videocamera';
    case BrokenGalleryFavourite = 'solar-broken-GalleryFavourite';
    case BrokenMusicLibrary2 = 'solar-broken-MusicLibrary2';
    case BrokenVideoFramePlayVertical = 'solar-broken-VideoFramePlayVertical';
    case BrokenFullScreenSquare = 'solar-broken-FullScreenSquare';
    case BrokenRewind10SecondsForward = 'solar-broken-Rewind10SecondsForward';
    case BrokenVolumeLoud = 'solar-broken-VolumeLoud';
    case BrokenMicrophone3 = 'solar-broken-Microphone3';
    case BrokenSoundwaveSquare = 'solar-broken-SoundwaveSquare';
    case BrokenCardholder = 'solar-broken-Cardholder';
    case BrokenBillList = 'solar-broken-BillList';
    case BrokenSaleSquare = 'solar-broken-SaleSquare';
    case BrokenDollar = 'solar-broken-Dollar';
    case BrokenTicket = 'solar-broken-Ticket';
    case BrokenTag = 'solar-broken-Tag';
    case BrokenCashOut = 'solar-broken-CashOut';
    case BrokenWallet2 = 'solar-broken-Wallet2';
    case BrokenRuble = 'solar-broken-Ruble';
    case BrokenCardTransfer = 'solar-broken-CardTransfer';
    case BrokenEuro = 'solar-broken-Euro';
    case BrokenSale = 'solar-broken-Sale';
    case BrokenCardSearch = 'solar-broken-CardSearch';
    case BrokenWallet = 'solar-broken-Wallet';
    case BrokenBillCross = 'solar-broken-BillCross';
    case BrokenTicketSale = 'solar-broken-TicketSale';
    case BrokenSafeSquare = 'solar-broken-SafeSquare';
    case BrokenCard = 'solar-broken-Card';
    case BrokenSafe2 = 'solar-broken-Safe2';
    case BrokenDollarMinimalistic = 'solar-broken-DollarMinimalistic';
    case BrokenTagPrice = 'solar-broken-TagPrice';
    case BrokenMoneyBag = 'solar-broken-MoneyBag';
    case BrokenBill = 'solar-broken-Bill';
    case BrokenCardSend = 'solar-broken-CardSend';
    case BrokenCardRecive = 'solar-broken-CardRecive';
    case BrokenBanknote2 = 'solar-broken-Banknote2';
    case BrokenTagHorizontal = 'solar-broken-TagHorizontal';
    case BrokenBillCheck = 'solar-broken-BillCheck';
    case BrokenTickerStar = 'solar-broken-TickerStar';
    case BrokenBanknote = 'solar-broken-Banknote';
    case BrokenVerifiedCheck = 'solar-broken-VerifiedCheck';
    case BrokenWadOfMoney = 'solar-broken-WadOfMoney';
    case BrokenCard2 = 'solar-broken-Card2';
    case BrokenSafeCircle = 'solar-broken-SafeCircle';
    case BrokenWalletMoney = 'solar-broken-WalletMoney';
    case BrokenList = 'solar-broken-List';
    case BrokenListDownMinimalistic = 'solar-broken-ListDownMinimalistic';
    case BrokenPlaylist2 = 'solar-broken-Playlist2';
    case BrokenChecklistMinimalistic = 'solar-broken-ChecklistMinimalistic';
    case BrokenPlaaylistMinimalistic = 'solar-broken-PlaaylistMinimalistic';
    case BrokenListHeart = 'solar-broken-ListHeart';
    case BrokenListArrowDown = 'solar-broken-ListArrowDown';
    case BrokenListArrowUp = 'solar-broken-ListArrowUp';
    case BrokenListUpMinimalistic = 'solar-broken-ListUpMinimalistic';
    case BrokenPlaylist = 'solar-broken-Playlist';
    case BrokenListUp = 'solar-broken-ListUp';
    case BrokenListCrossMinimalistic = 'solar-broken-ListCrossMinimalistic';
    case BrokenListCross = 'solar-broken-ListCross';
    case BrokenListArrowDownMinimalistic = 'solar-broken-ListArrowDownMinimalistic';
    case BrokenSortByAlphabet = 'solar-broken-SortByAlphabet';
    case BrokenChecklist = 'solar-broken-Checklist';
    case BrokenSortFromBottomToTop = 'solar-broken-SortFromBottomToTop';
    case BrokenListCheck = 'solar-broken-ListCheck';
    case BrokenPlaylistMinimalistic2 = 'solar-broken-PlaylistMinimalistic2';
    case BrokenPlaylistMinimalistic3 = 'solar-broken-PlaylistMinimalistic3';
    case BrokenList1 = 'solar-broken-List1';
    case BrokenSortFromTopToBottom = 'solar-broken-SortFromTopToBottom';
    case BrokenSortByTime = 'solar-broken-SortByTime';
    case BrokenListDown = 'solar-broken-ListDown';
    case BrokenListHeartMinimalistic = 'solar-broken-ListHeartMinimalistic';
    case BrokenListCheckMinimalistic = 'solar-broken-ListCheckMinimalistic';
    case BrokenListArrowUpMinimalistic = 'solar-broken-ListArrowUpMinimalistic';
    case BrokenVirus = 'solar-broken-Virus';
    case BrokenAdhesivePlaster2 = 'solar-broken-AdhesivePlaster2';
    case BrokenDropper = 'solar-broken-Dropper';
    case BrokenPulse2 = 'solar-broken-Pulse2';
    case BrokenBoneBroken = 'solar-broken-BoneBroken';
    case BrokenHeartPulse2 = 'solar-broken-HeartPulse2';
    case BrokenMedicalKit = 'solar-broken-MedicalKit';
    case BrokenTestTube = 'solar-broken-TestTube';
    case BrokenHealth = 'solar-broken-Health';
    case BrokenDropperMinimalistic2 = 'solar-broken-DropperMinimalistic2';
    case BrokenDNA = 'solar-broken-DNA';
    case BrokenDropper3 = 'solar-broken-Dropper3';
    case BrokenThermometer = 'solar-broken-Thermometer';
    case BrokenDropper2 = 'solar-broken-Dropper2';
    case BrokenJarOfPills2 = 'solar-broken-JarOfPills2';
    case BrokenBoneCrack = 'solar-broken-BoneCrack';
    case BrokenJarOfPills = 'solar-broken-JarOfPills';
    case BrokenSyringe = 'solar-broken-Syringe';
    case BrokenStethoscope = 'solar-broken-Stethoscope';
    case BrokenBenzeneRing = 'solar-broken-BenzeneRing';
    case BrokenBacteria = 'solar-broken-Bacteria';
    case BrokenAdhesivePlaster = 'solar-broken-AdhesivePlaster';
    case BrokenBone = 'solar-broken-Bone';
    case BrokenBones = 'solar-broken-Bones';
    case BrokenPill = 'solar-broken-Pill';
    case BrokenPills = 'solar-broken-Pills';
    case BrokenHeartPulse = 'solar-broken-HeartPulse';
    case BrokenTestTubeMinimalistic = 'solar-broken-TestTubeMinimalistic';
    case BrokenPills2 = 'solar-broken-Pills2';
    case BrokenPulse = 'solar-broken-Pulse';
    case BrokenDropperMinimalistic = 'solar-broken-DropperMinimalistic';
    case BrokenPills3 = 'solar-broken-Pills3';
    case BrokenWhisk = 'solar-broken-Whisk';
    case BrokenBottle = 'solar-broken-Bottle';
    case BrokenOvenMittsMinimalistic = 'solar-broken-OvenMittsMinimalistic';
    case BrokenChefHatMinimalistic = 'solar-broken-ChefHatMinimalistic';
    case BrokenTeaCup = 'solar-broken-TeaCup';
    case BrokenWineglassTriangle = 'solar-broken-WineglassTriangle';
    case BrokenOvenMitts = 'solar-broken-OvenMitts';
    case BrokenCupPaper = 'solar-broken-CupPaper';
    case BrokenLadle = 'solar-broken-Ladle';
    case BrokenCorkscrew = 'solar-broken-Corkscrew';
    case BrokenDonutBitten = 'solar-broken-DonutBitten';
    case BrokenWineglass = 'solar-broken-Wineglass';
    case BrokenDonut = 'solar-broken-Donut';
    case BrokenCupHot = 'solar-broken-CupHot';
    case BrokenChefHatHeart = 'solar-broken-ChefHatHeart';
    case BrokenChefHat = 'solar-broken-ChefHat';
    case BrokenRollingPin = 'solar-broken-RollingPin';
    case BrokenCodeFile = 'solar-broken-CodeFile';
    case BrokenFileCorrupted = 'solar-broken-FileCorrupted';
    case BrokenFile = 'solar-broken-File';
    case BrokenFileRight = 'solar-broken-FileRight';
    case BrokenFileFavourite = 'solar-broken-FileFavourite';
    case BrokenFileDownload = 'solar-broken-FileDownload';
    case BrokenZipFile = 'solar-broken-ZipFile';
    case BrokenFileText = 'solar-broken-FileText';
    case BrokenFileSmile = 'solar-broken-FileSmile)';
    case BrokenFileCheck = 'solar-broken-FileCheck';
    case BrokenFileSend = 'solar-broken-FileSend';
    case BrokenFileLeft = 'solar-broken-FileLeft';
    case BrokenFigmaFile = 'solar-broken-FigmaFile';
    case BrokenFileRemove = 'solar-broken-FileRemove';
    case BrokenCloudFile = 'solar-broken-CloudFile';
    case BrokenRemoveFolder = 'solar-broken-RemoveFolder';
    case BrokenFolderFavouritestar = 'solar-broken-FolderFavourite(star)';
    case BrokenAddFolder = 'solar-broken-AddFolder';
    case BrokenFolderCheck = 'solar-broken-FolderCheck';
    case BrokenFolderFavouritebookmark = 'solar-broken-FolderFavourite(bookmark)';
    case BrokenFolder2 = 'solar-broken-Folder2';
    case BrokenFolderSecurity = 'solar-broken-FolderSecurity';
    case BrokenFolderCloud = 'solar-broken-FolderCloud';
    case BrokenMoveToFolder = 'solar-broken-MoveToFolder';
    case BrokenFolderError = 'solar-broken-FolderError';
    case BrokenFolderPathConnect = 'solar-broken-FolderPathConnect';
    case BrokenFolderOpen = 'solar-broken-FolderOpen';
    case BrokenFolder = 'solar-broken-Folder';
    case BrokenFolderWithFiles = 'solar-broken-FolderWithFiles';
    case BrokenCloudCheck = 'solar-broken-CloudCheck';
    case BrokenTemperature = 'solar-broken-Temperature';
    case BrokenWind = 'solar-broken-Wind';
    case BrokenCloudSnowfall = 'solar-broken-CloudSnowfall';
    case BrokenSunrise = 'solar-broken-Sunrise';
    case BrokenSun2 = 'solar-broken-Sun2';
    case BrokenCloudSun = 'solar-broken-CloudSun';
    case BrokenCloudBoltMinimalistic = 'solar-broken-CloudBoltMinimalistic';
    case BrokenCloudDownload = 'solar-broken-CloudDownload';
    case BrokenClouds = 'solar-broken-Clouds';
    case BrokenTornado = 'solar-broken-Tornado';
    case BrokenMoonSleep = 'solar-broken-MoonSleep';
    case BrokenCloudUpload = 'solar-broken-CloudUpload';
    case BrokenCloudRain = 'solar-broken-CloudRain';
    case BrokenFog = 'solar-broken-Fog';
    case BrokenSnowflake = 'solar-broken-Snowflake';
    case BrokenMoonFog = 'solar-broken-MoonFog';
    case BrokenCloudMinus = 'solar-broken-CloudMinus';
    case BrokenCloudBolt = 'solar-broken-CloudBolt';
    case BrokenCloudWaterdrop = 'solar-broken-CloudWaterdrop';
    case BrokenSunset = 'solar-broken-Sunset';
    case BrokenWaterdrops = 'solar-broken-Waterdrops';
    case BrokenMoonStars = 'solar-broken-MoonStars';
    case BrokenCloudPlus = 'solar-broken-CloudPlus';
    case BrokenSun = 'solar-broken-Sun';
    case BrokenCloudWaterdrops = 'solar-broken-CloudWaterdrops';
    case BrokenCloudSun2 = 'solar-broken-CloudSun2';
    case BrokenCloudyMoon = 'solar-broken-CloudyMoon';
    case BrokenTornadoSmall = 'solar-broken-TornadoSmall';
    case BrokenCloud = 'solar-broken-Cloud';
    case BrokenSunFog = 'solar-broken-SunFog';
    case BrokenCloundCross = 'solar-broken-CloundCross';
    case BrokenCloudSnowfallMinimalistic = 'solar-broken-CloudSnowfallMinimalistic';
    case BrokenCloudStorm = 'solar-broken-CloudStorm';
    case BrokenMoon = 'solar-broken-Moon';
    case BrokenRefreshCircle = 'solar-broken-RefreshCircle';
    case BrokenSquareArrowRightDown = 'solar-broken-SquareArrowRightDown';
    case BrokenRoundArrowLeftDown = 'solar-broken-RoundArrowLeftDown';
    case BrokenRestart = 'solar-broken-Restart';
    case BrokenRoundAltArrowDown = 'solar-broken-RoundAltArrowDown';
    case BrokenRoundSortVertical = 'solar-broken-RoundSortVertical';
    case BrokenSquareAltArrowUp = 'solar-broken-SquareAltArrowUp';
    case BrokenArrowLeftUp = 'solar-broken-ArrowLeftUp';
    case BrokenSortHorizontal = 'solar-broken-SortHorizontal';
    case BrokenTransferHorizontal = 'solar-broken-TransferHorizontal';
    case BrokenSquareDoubleAltArrowUp = 'solar-broken-SquareDoubleAltArrowUp';
    case BrokenRoundArrowLeftUp = 'solar-broken-RoundArrowLeftUp';
    case BrokenAltArrowRight = 'solar-broken-AltArrowRight';
    case BrokenRoundDoubleAltArrowUp = 'solar-broken-RoundDoubleAltArrowUp';
    case BrokenRestartCircle = 'solar-broken-RestartCircle';
    case BrokenSquareArrowDown = 'solar-broken-SquareArrowDown';
    case BrokenSortVertical = 'solar-broken-SortVertical';
    case BrokenSquareSortHorizontal = 'solar-broken-SquareSortHorizontal';
    case BrokenDoubleAltArrowLeft = 'solar-broken-DoubleAltArrowLeft';
    case BrokenSquareAltArrowDown = 'solar-broken-SquareAltArrowDown';
    case BrokenSquareAltArrowRight = 'solar-broken-SquareAltArrowRight';
    case BrokenSquareArrowUp = 'solar-broken-SquareArrowUp';
    case BrokenDoubleAltArrowRight = 'solar-broken-DoubleAltArrowRight';
    case BrokenRoundTransferVertical = 'solar-broken-RoundTransferVertical';
    case BrokenArrowLeft = 'solar-broken-ArrowLeft';
    case BrokenRoundDoubleAltArrowRight = 'solar-broken-RoundDoubleAltArrowRight';
    case BrokenSquareDoubleAltArrowLeft = 'solar-broken-SquareDoubleAltArrowLeft';
    case BrokenAltArrowDown = 'solar-broken-AltArrowDown';
    case BrokenRoundTransferHorizontal = 'solar-broken-RoundTransferHorizontal';
    case BrokenRoundArrowRightDown = 'solar-broken-RoundArrowRightDown';
    case BrokenArrowUp = 'solar-broken-ArrowUp';
    case BrokenRoundArrowLeft = 'solar-broken-RoundArrowLeft';
    case BrokenDoubleAltArrowUp = 'solar-broken-DoubleAltArrowUp';
    case BrokenRoundArrowRight = 'solar-broken-RoundArrowRight';
    case BrokenSquareTransferHorizontal = 'solar-broken-SquareTransferHorizontal';
    case BrokenArrowRight = 'solar-broken-ArrowRight';
    case BrokenRoundDoubleAltArrowLeft = 'solar-broken-RoundDoubleAltArrowLeft';
    case BrokenRoundArrowUp = 'solar-broken-RoundArrowUp';
    case BrokenSquareSortVertical = 'solar-broken-SquareSortVertical';
    case BrokenAltArrowLeft = 'solar-broken-AltArrowLeft';
    case BrokenSquareDoubleAltArrowRight = 'solar-broken-SquareDoubleAltArrowRight';
    case BrokenRefresh = 'solar-broken-Refresh';
    case BrokenTransferVertical = 'solar-broken-TransferVertical';
    case BrokenRefreshSquare = 'solar-broken-RefreshSquare';
    case BrokenSquareTransferVertical = 'solar-broken-SquareTransferVertical';
    case BrokenSquareDoubleAltArrowDown = 'solar-broken-SquareDoubleAltArrowDown';
    case BrokenRoundArrowRightUp = 'solar-broken-RoundArrowRightUp';
    case BrokenArrowDown = 'solar-broken-ArrowDown';
    case BrokenRestartSquare = 'solar-broken-RestartSquare';
    case BrokenSquareArrowRight = 'solar-broken-SquareArrowRight';
    case BrokenRoundDoubleAltArrowDown = 'solar-broken-RoundDoubleAltArrowDown';
    case BrokenSquareArrowLeftUp = 'solar-broken-SquareArrowLeftUp';
    case BrokenRoundArrowDown = 'solar-broken-RoundArrowDown';
    case BrokenSquareArrowRightUp = 'solar-broken-SquareArrowRightUp';
    case BrokenRoundTransferDiagonal = 'solar-broken-RoundTransferDiagonal';
    case BrokenArrowRightDown = 'solar-broken-ArrowRightDown';
    case BrokenArrowLeftDown = 'solar-broken-ArrowLeftDown';
    case BrokenRoundAltArrowLeft = 'solar-broken-RoundAltArrowLeft';
    case BrokenArrowRightUp = 'solar-broken-ArrowRightUp';
    case BrokenSquareArrowLeftDown = 'solar-broken-SquareArrowLeftDown';
    case BrokenRoundAltArrowUp = 'solar-broken-RoundAltArrowUp';
    case BrokenAltArrowUp = 'solar-broken-AltArrowUp';
    case BrokenSquareAltArrowLeft = 'solar-broken-SquareAltArrowLeft';
    case BrokenRoundSortHorizontal = 'solar-broken-RoundSortHorizontal';
    case BrokenDoubleAltArrowDown = 'solar-broken-DoubleAltArrowDown';
    case BrokenRoundAltArrowRight = 'solar-broken-RoundAltArrowRight';
    case BrokenSquareArrowLeft = 'solar-broken-SquareArrowLeft';
    case BrokenTuningSquare2 = 'solar-broken-TuningSquare2';
    case BrokenWidgetAdd = 'solar-broken-WidgetAdd';
    case BrokenTuningSquare = 'solar-broken-TuningSquare';
    case BrokenSettingsMinimalistic = 'solar-broken-SettingsMinimalistic';
    case BrokenWidget6 = 'solar-broken-Widget6';
    case BrokenWidget4 = 'solar-broken-Widget4';
    case BrokenSettings = 'solar-broken-Settings';
    case BrokenWidget5 = 'solar-broken-Widget5';
    case BrokenWidget2 = 'solar-broken-Widget2';
    case BrokenWidget3 = 'solar-broken-Widget3';
    case BrokenTuning2 = 'solar-broken-Tuning2';
    case BrokenTuning3 = 'solar-broken-Tuning3';
    case BrokenWidget = 'solar-broken-Widget';
    case BrokenTuning4 = 'solar-broken-Tuning4';
    case BrokenTuning = 'solar-broken-Tuning';
    case BrokenDiagramDown = 'solar-broken-DiagramDown';
    case BrokenChart2 = 'solar-broken-Chart2';
    case BrokenChart = 'solar-broken-Chart';
    case BrokenDiagramUp = 'solar-broken-DiagramUp';
    case BrokenGraphNew = 'solar-broken-GraphNew';
    case BrokenCourseUp = 'solar-broken-CourseUp';
    case BrokenGraphDownNew = 'solar-broken-GraphDownNew';
    case BrokenPieChart3 = 'solar-broken-PieChart3';
    case BrokenPieChart2 = 'solar-broken-PieChart2';
    case BrokenGraphNewUp = 'solar-broken-GraphNewUp';
    case BrokenPieChart = 'solar-broken-PieChart';
    case BrokenRoundGraph = 'solar-broken-RoundGraph';
    case BrokenGraphUp = 'solar-broken-GraphUp';
    case BrokenChartSquare = 'solar-broken-ChartSquare';
    case BrokenCourseDown = 'solar-broken-CourseDown';
    case BrokenChatSquare2 = 'solar-broken-ChatSquare2';
    case BrokenGraphDown = 'solar-broken-GraphDown';
    case BrokenGraph = 'solar-broken-Graph';
    case BrokenPresentationGraph = 'solar-broken-PresentationGraph';
    case BrokenMaximizeSquare3 = 'solar-broken-MaximizeSquare3';
    case BrokenMaximizeSquareMinimalistic = 'solar-broken-MaximizeSquareMinimalistic';
    case BrokenMaximizeSquare2 = 'solar-broken-MaximizeSquare2';
    case BrokenMinimizeSquare = 'solar-broken-MinimizeSquare';
    case BrokenDownloadSquare = 'solar-broken-DownloadSquare';
    case BrokenUndoLeftRoundSquare = 'solar-broken-UndoLeftRoundSquare';
    case BrokenReply = 'solar-broken-Reply';
    case BrokenLogout = 'solar-broken-Logout';
    case BrokenReciveSquare = 'solar-broken-ReciveSquare';
    case BrokenExport = 'solar-broken-Export';
    case BrokenSendTwiceSquare = 'solar-broken-SendTwiceSquare';
    case BrokenUndoLeftRound = 'solar-broken-UndoLeftRound';
    case BrokenForward2 = 'solar-broken-Forward2';
    case BrokenMaximize = 'solar-broken-Maximize';
    case BrokenUndoRightRound = 'solar-broken-UndoRightRound';
    case BrokenMinimizeSquare2 = 'solar-broken-MinimizeSquare2';
    case BrokenMinimizeSquare3 = 'solar-broken-MinimizeSquare3';
    case BrokenUploadTwiceSquare = 'solar-broken-UploadTwiceSquare';
    case BrokenMinimize = 'solar-broken-Minimize';
    case BrokenCircleTopUp = 'solar-broken-CircleTopUp';
    case BrokenUploadMinimalistic = 'solar-broken-UploadMinimalistic';
    case BrokenDownload = 'solar-broken-Download';
    case BrokenImport = 'solar-broken-Import';
    case BrokenLogin = 'solar-broken-Login';
    case BrokenUndoLeft = 'solar-broken-UndoLeft';
    case BrokenSquareTopUp = 'solar-broken-SquareTopUp';
    case BrokenDownloadTwiceSquare = 'solar-broken-DownloadTwiceSquare';
    case BrokenCircleBottomDown = 'solar-broken-CircleBottomDown';
    case BrokenMaximizeSquare = 'solar-broken-MaximizeSquare';
    case BrokenUploadSquare = 'solar-broken-UploadSquare';
    case BrokenUndoRightSquare = 'solar-broken-UndoRightSquare';
    case BrokenReciveTwiceSquare = 'solar-broken-ReciveTwiceSquare';
    case BrokenCircleTopDown = 'solar-broken-CircleTopDown';
    case BrokenArrowToDownLeft = 'solar-broken-ArrowToDownLeft';
    case BrokenLogout2 = 'solar-broken-Logout2';
    case BrokenLogout3 = 'solar-broken-Logout3';
    case BrokenScale = 'solar-broken-Scale';
    case BrokenArrowToDownRight = 'solar-broken-ArrowToDownRight';
    case BrokenDownloadMinimalistic = 'solar-broken-DownloadMinimalistic';
    case BrokenMinimizeSquareMinimalistic = 'solar-broken-MinimizeSquareMinimalistic';
    case BrokenReply2 = 'solar-broken-Reply2';
    case BrokenSquareBottomUp = 'solar-broken-SquareBottomUp';
    case BrokenUndoRight = 'solar-broken-UndoRight';
    case BrokenUndoLeftSquare = 'solar-broken-UndoLeftSquare';
    case BrokenSendSquare = 'solar-broken-SendSquare';
    case BrokenExit = 'solar-broken-Exit';
    case BrokenSquareBottomDown = 'solar-broken-SquareBottomDown';
    case BrokenUndoRightRoundSquare = 'solar-broken-UndoRightRoundSquare';
    case BrokenArrowToTopLeft = 'solar-broken-ArrowToTopLeft';
    case BrokenCircleBottomUp = 'solar-broken-CircleBottomUp';
    case BrokenScreenShare = 'solar-broken-ScreenShare';
    case BrokenUpload = 'solar-broken-Upload';
    case BrokenSquareTopDown = 'solar-broken-SquareTopDown';
    case BrokenArrowToTopRight = 'solar-broken-ArrowToTopRight';
    case BrokenLogin3 = 'solar-broken-Login3';
    case BrokenLogin2 = 'solar-broken-Login2';
    case BrokenPassport = 'solar-broken-Passport';
    case BrokenDiplomaVerified = 'solar-broken-DiplomaVerified';
    case BrokenCaseRound = 'solar-broken-CaseRound';
    case BrokenBackpack = 'solar-broken-Backpack';
    case BrokenBook2 = 'solar-broken-Book2';
    case BrokenSquareAcademicCap2 = 'solar-broken-SquareAcademicCap2';
    case BrokenCaseRoundMinimalistic = 'solar-broken-CaseRoundMinimalistic';
    case BrokenCase = 'solar-broken-Case';
    case BrokenBookBookmarkMinimalistic = 'solar-broken-BookBookmarkMinimalistic';
    case BrokenBookmarkOpened = 'solar-broken-BookmarkOpened';
    case BrokenDiploma = 'solar-broken-Diploma';
    case BrokenBook = 'solar-broken-Book';
    case BrokenSquareAcademicCap = 'solar-broken-SquareAcademicCap';
    case BrokenBookmarkCircle = 'solar-broken-BookmarkCircle';
    case BrokenCalculatorMinimalistic = 'solar-broken-CalculatorMinimalistic';
    case BrokenNotebookSquare = 'solar-broken-NotebookSquare';
    case BrokenBookMinimalistic = 'solar-broken-BookMinimalistic';
    case BrokenCaseMinimalistic = 'solar-broken-CaseMinimalistic';
    case BrokenNotebookBookmark = 'solar-broken-NotebookBookmark';
    case BrokenPassportMinimalistic = 'solar-broken-PassportMinimalistic';
    case BrokenBookBookmark = 'solar-broken-BookBookmark';
    case BrokenBookmarkSquareMinimalistic = 'solar-broken-BookmarkSquareMinimalistic';
    case BrokenBookmark = 'solar-broken-Bookmark';
    case BrokenPlusMinus = 'solar-broken-PlusMinus';
    case BrokenCalculator = 'solar-broken-Calculator';
    case BrokenBookmarkSquare = 'solar-broken-BookmarkSquare';
    case BrokenNotebookMinimalistic = 'solar-broken-NotebookMinimalistic';
    case BrokenFireSquare = 'solar-broken-FireSquare';
    case BrokenSuitcaseLines = 'solar-broken-SuitcaseLines';
    case BrokenFire = 'solar-broken-Fire';
    case BrokenBonfire = 'solar-broken-Bonfire';
    case BrokenSuitcaseTag = 'solar-broken-SuitcaseTag';
    case BrokenLeaf = 'solar-broken-Leaf';
    case BrokenSuitcase = 'solar-broken-Suitcase';
    case BrokenFlame = 'solar-broken-Flame';
    case BrokenFireMinimalistic = 'solar-broken-FireMinimalistic';
    case BrokenBellBing = 'solar-broken-BellBing';
    case BrokenNotificationLinesRemove = 'solar-broken-NotificationLinesRemove';
    case BrokenNotificationUnread = 'solar-broken-NotificationUnread';
    case BrokenBell = 'solar-broken-Bell';
    case BrokenNotificationRemove = 'solar-broken-NotificationRemove';
    case BrokenNotificationUnreadLines = 'solar-broken-NotificationUnreadLines';
    case BrokenBellOff = 'solar-broken-BellOff';
    case BrokenLightning = 'solar-broken-Lightning';
    case BrokenLightbulbMinimalistic = 'solar-broken-LightbulbMinimalistic';
    case BrokenServerSquareCloud = 'solar-broken-ServerSquareCloud';
    case BrokenLightbulbBolt = 'solar-broken-LightbulbBolt';
    case BrokenAirbudsCharge = 'solar-broken-AirbudsCharge';
    case BrokenServerPath = 'solar-broken-ServerPath';
    case BrokenSimCardMinimalistic = 'solar-broken-SimCardMinimalistic';
    case BrokenSmartphone = 'solar-broken-Smartphone';
    case BrokenTurntable = 'solar-broken-Turntable';
    case BrokenAirbudsCheck = 'solar-broken-AirbudsCheck';
    case BrokenMouseMinimalistic = 'solar-broken-MouseMinimalistic';
    case BrokenSmartphoneRotateAngle = 'solar-broken-SmartphoneRotateAngle';
    case BrokenRadioMinimalistic = 'solar-broken-RadioMinimalistic';
    case BrokenAirbuds = 'solar-broken-Airbuds';
    case BrokenSmartphoneRotateOrientation = 'solar-broken-SmartphoneRotateOrientation';
    case BrokenIPhone = 'solar-broken-IPhone';
    case BrokenSimCard = 'solar-broken-SimCard';
    case BrokenFlashDrive = 'solar-broken-FlashDrive';
    case BrokenDevices = 'solar-broken-Devices';
    case BrokenSimCards = 'solar-broken-SimCards';
    case BrokenAirbudsCaseOpen = 'solar-broken-AirbudsCaseOpen';
    case BrokenTurntableMusicNote = 'solar-broken-TurntableMusicNote';
    case BrokenKeyboard = 'solar-broken-Keyboard';
    case BrokenGamepadCharge = 'solar-broken-GamepadCharge';
    case BrokenBoombox = 'solar-broken-Boombox';
    case BrokenSmartSpeakerMinimalistic = 'solar-broken-SmartSpeakerMinimalistic';
    case BrokenTelescope = 'solar-broken-Telescope';
    case BrokenMonitorCamera = 'solar-broken-MonitorCamera';
    case BrokenLaptopMinimalistic = 'solar-broken-LaptopMinimalistic';
    case BrokenServer2 = 'solar-broken-Server2';
    case BrokenSmartSpeaker = 'solar-broken-SmartSpeaker';
    case BrokenProjector = 'solar-broken-Projector';
    case BrokenServer = 'solar-broken-Server';
    case BrokenTV = 'solar-broken-TV';
    case BrokenCassette2 = 'solar-broken-Cassette2';
    case BrokenRadio = 'solar-broken-Radio';
    case BrokenSmartphoneVibration = 'solar-broken-SmartphoneVibration';
    case BrokenAirbudsLeft = 'solar-broken-AirbudsLeft';
    case BrokenHeadphonesRound = 'solar-broken-HeadphonesRound';
    case BrokenGameboy = 'solar-broken-Gameboy';
    case BrokenHeadphonesRoundSound = 'solar-broken-HeadphonesRoundSound';
    case BrokenCPU = 'solar-broken-CPU';
    case BrokenPrinter2 = 'solar-broken-Printer2';
    case BrokenHeadphonesSquare = 'solar-broken-HeadphonesSquare';
    case BrokenServerSquareUpdate = 'solar-broken-ServerSquareUpdate';
    case BrokenPrinterMinimalistic = 'solar-broken-PrinterMinimalistic';
    case BrokenBluetooth = 'solar-broken-Bluetooth';
    case BrokenWirelessCharge = 'solar-broken-WirelessCharge';
    case BrokenBluetoothCircle = 'solar-broken-BluetoothCircle';
    case BrokenAirbudsCaseMinimalistic = 'solar-broken-AirbudsCaseMinimalistic';
    case BrokenLightbulb = 'solar-broken-Lightbulb';
    case BrokenAirbudsRemove = 'solar-broken-AirbudsRemove';
    case BrokenSmartphoneRotate2 = 'solar-broken-SmartphoneRotate2';
    case BrokenSsdSquare = 'solar-broken-SsdSquare';
    case BrokenPrinter = 'solar-broken-Printer';
    case BrokenSmartphone2 = 'solar-broken-Smartphone2';
    case BrokenServerMinimalistic = 'solar-broken-ServerMinimalistic';
    case BrokenHeadphonesSquareSound = 'solar-broken-HeadphonesSquareSound';
    case BrokenDiskette = 'solar-broken-Diskette';
    case BrokenBluetoothWave = 'solar-broken-BluetoothWave';
    case BrokenSmartSpeaker2 = 'solar-broken-SmartSpeaker2';
    case BrokenLaptop3 = 'solar-broken-Laptop3';
    case BrokenLaptop2 = 'solar-broken-Laptop2';
    case BrokenMouseCircle = 'solar-broken-MouseCircle';
    case BrokenTurntableMinimalistic = 'solar-broken-TurntableMinimalistic';
    case BrokenSmartphoneUpdate = 'solar-broken-SmartphoneUpdate';
    case BrokenGamepadMinimalistic = 'solar-broken-GamepadMinimalistic';
    case BrokenSdCard = 'solar-broken-SdCard';
    case BrokenPlugCircle = 'solar-broken-PlugCircle';
    case BrokenAirbudsCase = 'solar-broken-AirbudsCase';
    case BrokenSsdRound = 'solar-broken-SsdRound';
    case BrokenLaptop = 'solar-broken-Laptop';
    case BrokenAirbudsRight = 'solar-broken-AirbudsRight';
    case BrokenDisplay = 'solar-broken-Display';
    case BrokenMonitorSmartphone = 'solar-broken-MonitorSmartphone';
    case BrokenSocket = 'solar-broken-Socket';
    case BrokenGamepadOld = 'solar-broken-GamepadOld';
    case BrokenCpuBolt = 'solar-broken-CpuBolt';
    case BrokenAirbudsCaseCharge = 'solar-broken-AirbudsCaseCharge';
    case BrokenTablet = 'solar-broken-Tablet';
    case BrokenWeigher = 'solar-broken-Weigher';
    case BrokenServerSquare = 'solar-broken-ServerSquare';
    case BrokenMouse = 'solar-broken-Mouse';
    case BrokenGamepadNoCharge = 'solar-broken-GamepadNoCharge';
    case BrokenBluetoothSquare = 'solar-broken-BluetoothSquare';
    case BrokenCloudStorage = 'solar-broken-CloudStorage';
    case BrokenGamepad = 'solar-broken-Gamepad';
    case BrokenMonitor = 'solar-broken-Monitor';
    case BrokenCassette = 'solar-broken-Cassette';
    // Bold Duotone Style (1205 icons)
    case BoldduotoneFacemaskCircle = 'solar-bold-duotone-FacemaskCircle';
    case BoldduotoneConfoundedCircle = 'solar-bold-duotone-ConfoundedCircle';
    case BoldduotoneSadSquare = 'solar-bold-duotone-SadSquare';
    case BoldduotoneSleepingCircle = 'solar-bold-duotone-SleepingCircle';
    case BoldduotoneFaceScanCircle = 'solar-bold-duotone-FaceScanCircle';
    case BoldduotoneSmileCircle = 'solar-bold-duotone-SmileCircle';
    case BoldduotoneStickerSmileCircle = 'solar-bold-duotone-StickerSmileCircle';
    case BoldduotoneStickerSquare = 'solar-bold-duotone-StickerSquare';
    case BoldduotoneEmojiFunnyCircle = 'solar-bold-duotone-EmojiFunnyCircle';
    case BoldduotoneExpressionlessSquare = 'solar-bold-duotone-ExpressionlessSquare';
    case BoldduotoneSleepingSquare = 'solar-bold-duotone-SleepingSquare';
    case BoldduotoneSadCircle = 'solar-bold-duotone-SadCircle';
    case BoldduotoneFacemaskSquare = 'solar-bold-duotone-FacemaskSquare';
    case BoldduotoneConfoundedSquare = 'solar-bold-duotone-ConfoundedSquare';
    case BoldduotoneFaceScanSquare = 'solar-bold-duotone-FaceScanSquare';
    case BoldduotoneSmileSquare = 'solar-bold-duotone-SmileSquare';
    case BoldduotoneStickerSmileCircle2 = 'solar-bold-duotone-StickerSmileCircle2';
    case BoldduotoneStickerSmileSquare = 'solar-bold-duotone-StickerSmileSquare';
    case BoldduotoneEmojiFunnySquare = 'solar-bold-duotone-EmojiFunnySquare';
    case BoldduotoneStickerCircle = 'solar-bold-duotone-StickerCircle';
    case BoldduotoneExpressionlessCircle = 'solar-bold-duotone-ExpressionlessCircle';
    case BoldduotoneLike = 'solar-bold-duotone-Like';
    case BoldduotoneMedalStarSquare = 'solar-bold-duotone-MedalStarSquare';
    case BoldduotoneDislike = 'solar-bold-duotone-Dislike';
    case BoldduotoneStarShine = 'solar-bold-duotone-StarShine';
    case BoldduotoneHeartAngle = 'solar-bold-duotone-HeartAngle';
    case BoldduotoneMedalRibbon = 'solar-bold-duotone-MedalRibbon';
    case BoldduotoneHeartShine = 'solar-bold-duotone-HeartShine';
    case BoldduotoneMedalStarCircle = 'solar-bold-duotone-MedalStarCircle';
    case BoldduotoneMedalRibbonsStar = 'solar-bold-duotone-MedalRibbonsStar';
    case BoldduotoneStar = 'solar-bold-duotone-Star';
    case BoldduotoneHeartUnlock = 'solar-bold-duotone-HeartUnlock';
    case BoldduotoneMedalRibbonStar = 'solar-bold-duotone-MedalRibbonStar';
    case BoldduotoneHeartLock = 'solar-bold-duotone-HeartLock';
    case BoldduotoneHeartBroken = 'solar-bold-duotone-HeartBroken';
    case BoldduotoneHearts = 'solar-bold-duotone-Hearts';
    case BoldduotoneMedalStar = 'solar-bold-duotone-MedalStar';
    case BoldduotoneHeart = 'solar-bold-duotone-Heart';
    case BoldduotoneCloset = 'solar-bold-duotone-Closet';
    case BoldduotoneBed = 'solar-bold-duotone-Bed';
    case BoldduotoneWashingMachine = 'solar-bold-duotone-WashingMachine';
    case BoldduotoneBedsideTable = 'solar-bold-duotone-BedsideTable';
    case BoldduotoneSofa3 = 'solar-bold-duotone-Sofa3';
    case BoldduotoneSofa2 = 'solar-bold-duotone-Sofa2';
    case BoldduotoneChair2 = 'solar-bold-duotone-Chair2';
    case BoldduotoneBath = 'solar-bold-duotone-Bath';
    case BoldduotoneSmartVacuumCleaner2 = 'solar-bold-duotone-SmartVacuumCleaner2';
    case BoldduotoneCondicioner = 'solar-bold-duotone-Condicioner';
    case BoldduotoneSmartVacuumCleaner = 'solar-bold-duotone-SmartVacuumCleaner';
    case BoldduotoneRemoteController2 = 'solar-bold-duotone-RemoteController2';
    case BoldduotoneFloorLampMinimalistic = 'solar-bold-duotone-FloorLampMinimalistic';
    case BoldduotoneLamp = 'solar-bold-duotone-Lamp';
    case BoldduotoneBarChair = 'solar-bold-duotone-BarChair';
    case BoldduotoneBedsideTable2 = 'solar-bold-duotone-BedsideTable2';
    case BoldduotoneCloset2 = 'solar-bold-duotone-Closet2';
    case BoldduotoneBedsideTable3 = 'solar-bold-duotone-BedsideTable3';
    case BoldduotoneSpeaker = 'solar-bold-duotone-Speaker';
    case BoldduotoneVolumeKnob = 'solar-bold-duotone-VolumeKnob';
    case BoldduotoneArmchair = 'solar-bold-duotone-Armchair';
    case BoldduotoneSpeakerMinimalistic = 'solar-bold-duotone-SpeakerMinimalistic';
    case BoldduotoneRemoteController = 'solar-bold-duotone-RemoteController';
    case BoldduotoneTrellis = 'solar-bold-duotone-Trellis';
    case BoldduotoneFloorLamp = 'solar-bold-duotone-FloorLamp';
    case BoldduotoneCondicioner2 = 'solar-bold-duotone-Condicioner2';
    case BoldduotoneBedsideTable4 = 'solar-bold-duotone-BedsideTable4';
    case BoldduotoneArmchair2 = 'solar-bold-duotone-Armchair2';
    case BoldduotoneWashingMachineMinimalistic = 'solar-bold-duotone-WashingMachineMinimalistic';
    case BoldduotoneChair = 'solar-bold-duotone-Chair';
    case BoldduotoneRemoteControllerMinimalistic = 'solar-bold-duotone-RemoteControllerMinimalistic';
    case BoldduotoneChandelier = 'solar-bold-duotone-Chandelier';
    case BoldduotoneFridge = 'solar-bold-duotone-Fridge';
    case BoldduotoneMirror = 'solar-bold-duotone-Mirror';
    case BoldduotoneSofa = 'solar-bold-duotone-Sofa';
    case BoldduotoneEarth = 'solar-bold-duotone-Earth';
    case BoldduotoneStarsLine = 'solar-bold-duotone-StarsLine';
    case BoldduotoneStarFall2 = 'solar-bold-duotone-StarFall2';
    case BoldduotoneStarFall = 'solar-bold-duotone-StarFall';
    case BoldduotoneBlackHole3 = 'solar-bold-duotone-BlackHole3';
    case BoldduotoneWomen = 'solar-bold-duotone-Women';
    case BoldduotoneBlackHole = 'solar-bold-duotone-BlackHole';
    case BoldduotoneStarRings = 'solar-bold-duotone-StarRings';
    case BoldduotoneBlackHole2 = 'solar-bold-duotone-BlackHole2';
    case BoldduotoneStarFallMinimalistic2 = 'solar-bold-duotone-StarFallMinimalistic2';
    case BoldduotonePlanet = 'solar-bold-duotone-Planet';
    case BoldduotoneSatellite = 'solar-bold-duotone-Satellite';
    case BoldduotoneMen = 'solar-bold-duotone-Men';
    case BoldduotoneRocket2 = 'solar-bold-duotone-Rocket2';
    case BoldduotoneStars = 'solar-bold-duotone-Stars';
    case BoldduotoneStarAngle = 'solar-bold-duotone-StarAngle';
    case BoldduotoneInfinity = 'solar-bold-duotone-Infinity';
    case BoldduotoneUfo2 = 'solar-bold-duotone-Ufo2';
    case BoldduotoneUfo3 = 'solar-bold-duotone-Ufo3';
    case BoldduotoneStarRing = 'solar-bold-duotone-StarRing';
    case BoldduotonePlanet2 = 'solar-bold-duotone-Planet2';
    case BoldduotonePlanet3 = 'solar-bold-duotone-Planet3';
    case BoldduotoneAsteroid = 'solar-bold-duotone-Asteroid';
    case BoldduotoneStarsMinimalistic = 'solar-bold-duotone-StarsMinimalistic';
    case BoldduotoneUFO = 'solar-bold-duotone-UFO';
    case BoldduotonePlanet4 = 'solar-bold-duotone-Planet4';
    case BoldduotoneRocket = 'solar-bold-duotone-Rocket';
    case BoldduotoneStarFallMinimalistic = 'solar-bold-duotone-StarFallMinimalistic';
    case BoldduotoneStarRainbow = 'solar-bold-duotone-StarRainbow';
    case BoldduotoneAtom = 'solar-bold-duotone-Atom';
    case BoldduotoneStarCircle = 'solar-bold-duotone-StarCircle';
    case BoldduotoneCompassBig = 'solar-bold-duotone-CompassBig';
    case BoldduotoneMapPointSchool = 'solar-bold-duotone-MapPointSchool';
    case BoldduotoneSignpost = 'solar-bold-duotone-Signpost';
    case BoldduotoneMapArrowDown = 'solar-bold-duotone-MapArrowDown';
    case BoldduotoneMap = 'solar-bold-duotone-Map';
    case BoldduotoneMapArrowUp = 'solar-bold-duotone-MapArrowUp';
    case BoldduotonePointOnMapPerspective = 'solar-bold-duotone-PointOnMapPerspective';
    case BoldduotoneRadar = 'solar-bold-duotone-Radar';
    case BoldduotoneStreets = 'solar-bold-duotone-Streets';
    case BoldduotoneMapPointWave = 'solar-bold-duotone-MapPointWave';
    case BoldduotonePeopleNearby = 'solar-bold-duotone-PeopleNearby';
    case BoldduotoneStreetsMapPoint = 'solar-bold-duotone-StreetsMapPoint';
    case BoldduotoneMapPointSearch = 'solar-bold-duotone-MapPointSearch';
    case BoldduotoneGPS = 'solar-bold-duotone-GPS';
    case BoldduotoneMapArrowSquare = 'solar-bold-duotone-MapArrowSquare';
    case BoldduotoneBranchingPathsDown = 'solar-bold-duotone-BranchingPathsDown';
    case BoldduotoneMapPointRotate = 'solar-bold-duotone-MapPointRotate';
    case BoldduotoneGlobal = 'solar-bold-duotone-Global';
    case BoldduotoneCompassSquare = 'solar-bold-duotone-CompassSquare';
    case BoldduotoneRouting3 = 'solar-bold-duotone-Routing3';
    case BoldduotoneRouting2 = 'solar-bold-duotone-Routing2';
    case BoldduotoneMapPointRemove = 'solar-bold-duotone-MapPointRemove';
    case BoldduotoneGlobus = 'solar-bold-duotone-Globus';
    case BoldduotoneSignpost2 = 'solar-bold-duotone-Signpost2';
    case BoldduotoneRadar2 = 'solar-bold-duotone-Radar2';
    case BoldduotoneStreetsNavigation = 'solar-bold-duotone-StreetsNavigation';
    case BoldduotoneMapPoint = 'solar-bold-duotone-MapPoint';
    case BoldduotoneMapPointHospital = 'solar-bold-duotone-MapPointHospital';
    case BoldduotoneCompass = 'solar-bold-duotone-Compass';
    case BoldduotoneMapPointAdd = 'solar-bold-duotone-MapPointAdd';
    case BoldduotoneBranchingPathsUp = 'solar-bold-duotone-BranchingPathsUp';
    case BoldduotoneMapPointFavourite = 'solar-bold-duotone-MapPointFavourite';
    case BoldduotoneRoute = 'solar-bold-duotone-Route';
    case BoldduotonePointOnMap = 'solar-bold-duotone-PointOnMap';
    case BoldduotoneMapArrowRight = 'solar-bold-duotone-MapArrowRight';
    case BoldduotoneRouting = 'solar-bold-duotone-Routing';
    case BoldduotoneMapArrowLeft = 'solar-bold-duotone-MapArrowLeft';
    case BoldduotoneIncognito = 'solar-bold-duotone-Incognito';
    case BoldduotoneLockPassword = 'solar-bold-duotone-LockPassword';
    case BoldduotoneShieldNetwork = 'solar-bold-duotone-ShieldNetwork';
    case BoldduotoneKeyMinimalisticSquare = 'solar-bold-duotone-KeyMinimalisticSquare';
    case BoldduotoneLockKeyholeUnlocked = 'solar-bold-duotone-LockKeyholeUnlocked';
    case BoldduotoneLock = 'solar-bold-duotone-Lock';
    case BoldduotoneShieldKeyhole = 'solar-bold-duotone-ShieldKeyhole';
    case BoldduotoneEyeClosed = 'solar-bold-duotone-EyeClosed';
    case BoldduotoneKey = 'solar-bold-duotone-Key';
    case BoldduotoneShieldMinus = 'solar-bold-duotone-ShieldMinus';
    case BoldduotoneShield = 'solar-bold-duotone-Shield';
    case BoldduotoneLockUnlocked = 'solar-bold-duotone-LockUnlocked';
    case BoldduotoneBombMinimalistic = 'solar-bold-duotone-BombMinimalistic';
    case BoldduotoneShieldStar = 'solar-bold-duotone-ShieldStar';
    case BoldduotoneBomb = 'solar-bold-duotone-Bomb';
    case BoldduotoneKeySquare = 'solar-bold-duotone-KeySquare';
    case BoldduotoneLockKeyholeMinimalisticUnlocked = 'solar-bold-duotone-LockKeyholeMinimalisticUnlocked';
    case BoldduotoneShieldCross = 'solar-bold-duotone-ShieldCross';
    case BoldduotoneObjectScan = 'solar-bold-duotone-ObjectScan';
    case BoldduotonePasswordMinimalisticInput = 'solar-bold-duotone-PasswordMinimalisticInput';
    case BoldduotoneLockPasswordUnlocked = 'solar-bold-duotone-LockPasswordUnlocked';
    case BoldduotoneSiren = 'solar-bold-duotone-Siren';
    case BoldduotoneShieldMinimalistic = 'solar-bold-duotone-ShieldMinimalistic';
    case BoldduotoneEyeScan = 'solar-bold-duotone-EyeScan';
    case BoldduotoneKeyMinimalisticSquare2 = 'solar-bold-duotone-KeyMinimalisticSquare2';
    case BoldduotoneScanner2 = 'solar-bold-duotone-Scanner2';
    case BoldduotoneKeyMinimalisticSquare3 = 'solar-bold-duotone-KeyMinimalisticSquare3';
    case BoldduotoneKeyMinimalistic2 = 'solar-bold-duotone-KeyMinimalistic2';
    case BoldduotoneCodeScan = 'solar-bold-duotone-CodeScan';
    case BoldduotoneShieldPlus = 'solar-bold-duotone-ShieldPlus';
    case BoldduotonePasswordMinimalistic = 'solar-bold-duotone-PasswordMinimalistic';
    case BoldduotoneEye = 'solar-bold-duotone-Eye';
    case BoldduotoneQrCode = 'solar-bold-duotone-QrCode';
    case BoldduotoneShieldCheck = 'solar-bold-duotone-ShieldCheck';
    case BoldduotoneKeyMinimalistic = 'solar-bold-duotone-KeyMinimalistic';
    case BoldduotoneLockKeyhole = 'solar-bold-duotone-LockKeyhole';
    case BoldduotoneShieldUser = 'solar-bold-duotone-ShieldUser';
    case BoldduotoneKeySquare2 = 'solar-bold-duotone-KeySquare2';
    case BoldduotoneBombEmoji = 'solar-bold-duotone-BombEmoji';
    case BoldduotoneScanner = 'solar-bold-duotone-Scanner';
    case BoldduotoneShieldUp = 'solar-bold-duotone-ShieldUp';
    case BoldduotoneSirenRounded = 'solar-bold-duotone-SirenRounded';
    case BoldduotoneLockKeyholeMinimalistic = 'solar-bold-duotone-LockKeyholeMinimalistic';
    case BoldduotonePassword = 'solar-bold-duotone-Password';
    case BoldduotoneShieldKeyholeMinimalistic = 'solar-bold-duotone-ShieldKeyholeMinimalistic';
    case BoldduotoneShieldWarning = 'solar-bold-duotone-ShieldWarning';
    case BoldduotonePallete2 = 'solar-bold-duotone-Pallete2';
    case BoldduotoneAlignVerticalSpacing = 'solar-bold-duotone-AlignVerticalSpacing';
    case BoldduotoneAlignVerticalCenter = 'solar-bold-duotone-AlignVerticalCenter';
    case BoldduotoneCropMinimalistic = 'solar-bold-duotone-CropMinimalistic';
    case BoldduotoneMirrorRight = 'solar-bold-duotone-MirrorRight';
    case BoldduotoneAlignBottom = 'solar-bold-duotone-AlignBottom';
    case BoldduotoneRadialBlur = 'solar-bold-duotone-RadialBlur';
    case BoldduotoneCrop = 'solar-bold-duotone-Crop';
    case BoldduotoneAlignHorizontaSpacing = 'solar-bold-duotone-AlignHorizontaSpacing';
    case BoldduotoneRulerPen = 'solar-bold-duotone-RulerPen';
    case BoldduotoneThreeSquares = 'solar-bold-duotone-ThreeSquares';
    case BoldduotonePaintRoller = 'solar-bold-duotone-PaintRoller';
    case BoldduotoneLayers = 'solar-bold-duotone-Layers';
    case BoldduotoneFilters = 'solar-bold-duotone-Filters';
    case BoldduotoneRulerCrossPen = 'solar-bold-duotone-RulerCrossPen';
    case BoldduotoneFlipHorizontal = 'solar-bold-duotone-FlipHorizontal';
    case BoldduotoneAlignLeft = 'solar-bold-duotone-AlignLeft';
    case BoldduotoneRuler = 'solar-bold-duotone-Ruler';
    case BoldduotonePalette = 'solar-bold-duotone-Palette';
    case BoldduotoneAlignTop = 'solar-bold-duotone-AlignTop';
    case BoldduotoneAlignHorizontalCenter = 'solar-bold-duotone-AlignHorizontalCenter';
    case BoldduotoneAlignRight = 'solar-bold-duotone-AlignRight';
    case BoldduotoneRulerAngular = 'solar-bold-duotone-RulerAngular';
    case BoldduotonePipette = 'solar-bold-duotone-Pipette';
    case BoldduotoneFlipVertical = 'solar-bold-duotone-FlipVertical';
    case BoldduotoneMirrorLeft = 'solar-bold-duotone-MirrorLeft';
    case BoldduotoneLayersMinimalistic = 'solar-bold-duotone-LayersMinimalistic';
    case BoldduotoneColourTuneing = 'solar-bold-duotone-ColourTuneing';
    case BoldduotonePaletteRound = 'solar-bold-duotone-PaletteRound';
    case BoldduotoneEraser = 'solar-bold-duotone-Eraser';
    case BoldduotoneTextItalicCircle = 'solar-bold-duotone-TextItalicCircle';
    case BoldduotoneLinkRound = 'solar-bold-duotone-LinkRound';
    case BoldduotoneTextItalic = 'solar-bold-duotone-TextItalic';
    case BoldduotoneLinkBrokenMinimalistic = 'solar-bold-duotone-LinkBrokenMinimalistic';
    case BoldduotoneTextUnderlineCross = 'solar-bold-duotone-TextUnderlineCross';
    case BoldduotoneLink = 'solar-bold-duotone-Link';
    case BoldduotoneEraserCircle = 'solar-bold-duotone-EraserCircle';
    case BoldduotoneLinkCircle = 'solar-bold-duotone-LinkCircle';
    case BoldduotoneTextBoldCircle = 'solar-bold-duotone-TextBoldCircle';
    case BoldduotoneTextField = 'solar-bold-duotone-TextField';
    case BoldduotoneTextSquare = 'solar-bold-duotone-TextSquare';
    case BoldduotoneTextSquare2 = 'solar-bold-duotone-TextSquare2';
    case BoldduotoneLinkRoundAngle = 'solar-bold-duotone-LinkRoundAngle';
    case BoldduotoneTextUnderlineCircle = 'solar-bold-duotone-TextUnderlineCircle';
    case BoldduotoneTextCrossCircle = 'solar-bold-duotone-TextCrossCircle';
    case BoldduotoneTextItalicSquare = 'solar-bold-duotone-TextItalicSquare';
    case BoldduotoneParagraphSpacing = 'solar-bold-duotone-ParagraphSpacing';
    case BoldduotoneText = 'solar-bold-duotone-Text';
    case BoldduotoneLinkBroken = 'solar-bold-duotone-LinkBroken';
    case BoldduotoneTextCross = 'solar-bold-duotone-TextCross';
    case BoldduotoneTextUnderline = 'solar-bold-duotone-TextUnderline';
    case BoldduotoneLinkMinimalistic = 'solar-bold-duotone-LinkMinimalistic';
    case BoldduotoneLinkMinimalistic2 = 'solar-bold-duotone-LinkMinimalistic2';
    case BoldduotoneTextBold = 'solar-bold-duotone-TextBold';
    case BoldduotoneTextSelection = 'solar-bold-duotone-TextSelection';
    case BoldduotoneTextFieldFocus = 'solar-bold-duotone-TextFieldFocus';
    case BoldduotoneTextBoldSquare = 'solar-bold-duotone-TextBoldSquare';
    case BoldduotoneEraserSquare = 'solar-bold-duotone-EraserSquare';
    case BoldduotoneLinkSquare = 'solar-bold-duotone-LinkSquare';
    case BoldduotoneTextCircle = 'solar-bold-duotone-TextCircle';
    case BoldduotoneBackspace = 'solar-bold-duotone-Backspace';
    case BoldduotoneTextCrossSquare = 'solar-bold-duotone-TextCrossSquare';
    case BoldduotoneInboxUnread = 'solar-bold-duotone-InboxUnread';
    case BoldduotoneChatUnread = 'solar-bold-duotone-ChatUnread';
    case BoldduotoneChatRound = 'solar-bold-duotone-ChatRound';
    case BoldduotoneUnread = 'solar-bold-duotone-Unread';
    case BoldduotoneMailbox = 'solar-bold-duotone-Mailbox';
    case BoldduotoneLetter = 'solar-bold-duotone-Letter';
    case BoldduotonePenNewRound = 'solar-bold-duotone-PenNewRound';
    case BoldduotoneMultipleForwardRight = 'solar-bold-duotone-MultipleForwardRight';
    case BoldduotoneMultipleForwardLeft = 'solar-bold-duotone-MultipleForwardLeft';
    case BoldduotoneInboxArchive = 'solar-bold-duotone-InboxArchive';
    case BoldduotoneInbox = 'solar-bold-duotone-Inbox';
    case BoldduotonePen2 = 'solar-bold-duotone-Pen2';
    case BoldduotonePenNewSquare = 'solar-bold-duotone-PenNewSquare';
    case BoldduotonePen = 'solar-bold-duotone-Pen';
    case BoldduotoneChatDots = 'solar-bold-duotone-ChatDots';
    case BoldduotoneChatSquareCall = 'solar-bold-duotone-ChatSquareCall';
    case BoldduotoneSquareShareLine = 'solar-bold-duotone-SquareShareLine';
    case BoldduotoneChatRoundCheck = 'solar-bold-duotone-ChatRoundCheck';
    case BoldduotoneInboxOut = 'solar-bold-duotone-InboxOut';
    case BoldduotonePlain3 = 'solar-bold-duotone-Plain3';
    case BoldduotoneChatRoundDots = 'solar-bold-duotone-ChatRoundDots';
    case BoldduotoneChatRoundLike = 'solar-bold-duotone-ChatRoundLike';
    case BoldduotonePlain2 = 'solar-bold-duotone-Plain2';
    case BoldduotoneChatRoundUnread = 'solar-bold-duotone-ChatRoundUnread';
    case BoldduotoneChatSquareLike = 'solar-bold-duotone-ChatSquareLike';
    case BoldduotonePaperclip = 'solar-bold-duotone-Paperclip';
    case BoldduotoneChatSquareCheck = 'solar-bold-duotone-ChatSquareCheck';
    case BoldduotoneChatSquare = 'solar-bold-duotone-ChatSquare';
    case BoldduotoneLetterOpened = 'solar-bold-duotone-LetterOpened';
    case BoldduotoneSquareForward = 'solar-bold-duotone-SquareForward';
    case BoldduotoneLetterUnread = 'solar-bold-duotone-LetterUnread';
    case BoldduotonePaperclipRounded2 = 'solar-bold-duotone-PaperclipRounded2';
    case BoldduotoneChatRoundCall = 'solar-bold-duotone-ChatRoundCall';
    case BoldduotoneInboxLine = 'solar-bold-duotone-InboxLine';
    case BoldduotoneChatRoundVideo = 'solar-bold-duotone-ChatRoundVideo';
    case BoldduotoneChatRoundMoney = 'solar-bold-duotone-ChatRoundMoney';
    case BoldduotoneInboxIn = 'solar-bold-duotone-InboxIn';
    case BoldduotoneCheckRead = 'solar-bold-duotone-CheckRead';
    case BoldduotoneChatRoundLine = 'solar-bold-duotone-ChatRoundLine';
    case BoldduotoneForward = 'solar-bold-duotone-Forward';
    case BoldduotonePaperclip2 = 'solar-bold-duotone-Paperclip2';
    case BoldduotoneDialog2 = 'solar-bold-duotone-Dialog2';
    case BoldduotoneDialog = 'solar-bold-duotone-Dialog';
    case BoldduotonePaperclipRounded = 'solar-bold-duotone-PaperclipRounded';
    case BoldduotonePlain = 'solar-bold-duotone-Plain';
    case BoldduotoneChatSquareArrow = 'solar-bold-duotone-ChatSquareArrow';
    case BoldduotoneChatSquareCode = 'solar-bold-duotone-ChatSquareCode';
    case BoldduotoneChatLine = 'solar-bold-duotone-ChatLine';
    case BoldduotoneTennis = 'solar-bold-duotone-Tennis';
    case BoldduotoneBicyclingRound = 'solar-bold-duotone-BicyclingRound';
    case BoldduotoneBalls = 'solar-bold-duotone-Balls';
    case BoldduotoneMeditationRound = 'solar-bold-duotone-MeditationRound';
    case BoldduotoneStretchingRound = 'solar-bold-duotone-StretchingRound';
    case BoldduotoneDumbbells2 = 'solar-bold-duotone-Dumbbells2';
    case BoldduotoneMeditation = 'solar-bold-duotone-Meditation';
    case BoldduotoneRunning2 = 'solar-bold-duotone-Running2';
    case BoldduotoneRugby = 'solar-bold-duotone-Rugby';
    case BoldduotoneBodyShapeMinimalistic = 'solar-bold-duotone-BodyShapeMinimalistic';
    case BoldduotoneStretching = 'solar-bold-duotone-Stretching';
    case BoldduotoneBowling = 'solar-bold-duotone-Bowling';
    case BoldduotoneRanking = 'solar-bold-duotone-Ranking';
    case BoldduotoneTreadmillRound = 'solar-bold-duotone-TreadmillRound';
    case BoldduotoneVolleyball = 'solar-bold-duotone-Volleyball';
    case BoldduotoneDumbbellLargeMinimalistic = 'solar-bold-duotone-DumbbellLargeMinimalistic';
    case BoldduotoneRunningRound = 'solar-bold-duotone-RunningRound';
    case BoldduotoneHiking = 'solar-bold-duotone-Hiking';
    case BoldduotoneHikingMinimalistic = 'solar-bold-duotone-HikingMinimalistic';
    case BoldduotoneWaterSun = 'solar-bold-duotone-WaterSun';
    case BoldduotoneGolf = 'solar-bold-duotone-Golf';
    case BoldduotoneSkateboarding = 'solar-bold-duotone-Skateboarding';
    case BoldduotoneDumbbells = 'solar-bold-duotone-Dumbbells';
    case BoldduotoneWalkingRound = 'solar-bold-duotone-WalkingRound';
    case BoldduotoneRunning = 'solar-bold-duotone-Running';
    case BoldduotoneTreadmill = 'solar-bold-duotone-Treadmill';
    case BoldduotoneSkateboard = 'solar-bold-duotone-Skateboard';
    case BoldduotoneDumbbellSmall = 'solar-bold-duotone-DumbbellSmall';
    case BoldduotoneBasketball = 'solar-bold-duotone-Basketball';
    case BoldduotoneFootball = 'solar-bold-duotone-Football';
    case BoldduotoneDumbbell = 'solar-bold-duotone-Dumbbell';
    case BoldduotoneBodyShape = 'solar-bold-duotone-BodyShape';
    case BoldduotoneWater = 'solar-bold-duotone-Water';
    case BoldduotoneSkateboardingRound = 'solar-bold-duotone-SkateboardingRound';
    case BoldduotoneHikingRound = 'solar-bold-duotone-HikingRound';
    case BoldduotoneVolleyball2 = 'solar-bold-duotone-Volleyball2';
    case BoldduotoneTennis2 = 'solar-bold-duotone-Tennis2';
    case BoldduotoneSwimming = 'solar-bold-duotone-Swimming';
    case BoldduotoneBicycling = 'solar-bold-duotone-Bicycling';
    case BoldduotoneWalking = 'solar-bold-duotone-Walking';
    case BoldduotoneDumbbellLarge = 'solar-bold-duotone-DumbbellLarge';
    case BoldduotoneCalendarMark = 'solar-bold-duotone-CalendarMark';
    case BoldduotoneHistory2 = 'solar-bold-duotone-History2';
    case BoldduotoneWatchSquareMinimalisticCharge = 'solar-bold-duotone-WatchSquareMinimalisticCharge';
    case BoldduotoneHistory3 = 'solar-bold-duotone-History3';
    case BoldduotoneHourglass = 'solar-bold-duotone-Hourglass';
    case BoldduotoneCalendarSearch = 'solar-bold-duotone-CalendarSearch';
    case BoldduotoneStopwatchPlay = 'solar-bold-duotone-StopwatchPlay';
    case BoldduotoneWatchRound = 'solar-bold-duotone-WatchRound';
    case BoldduotoneCalendarAdd = 'solar-bold-duotone-CalendarAdd';
    case BoldduotoneCalendarDate = 'solar-bold-duotone-CalendarDate';
    case BoldduotoneStopwatch = 'solar-bold-duotone-Stopwatch';
    case BoldduotoneAlarmPause = 'solar-bold-duotone-AlarmPause';
    case BoldduotoneAlarmTurnOff = 'solar-bold-duotone-AlarmTurnOff';
    case BoldduotoneClockSquare = 'solar-bold-duotone-ClockSquare';
    case BoldduotoneStopwatchPause = 'solar-bold-duotone-StopwatchPause';
    case BoldduotoneCalendarMinimalistic = 'solar-bold-duotone-CalendarMinimalistic';
    case BoldduotoneAlarmAdd = 'solar-bold-duotone-AlarmAdd';
    case BoldduotoneAlarmPlay = 'solar-bold-duotone-AlarmPlay';
    case BoldduotoneHourglassLine = 'solar-bold-duotone-HourglassLine';
    case BoldduotoneAlarmSleep = 'solar-bold-duotone-AlarmSleep';
    case BoldduotoneAlarmRemove = 'solar-bold-duotone-AlarmRemove';
    case BoldduotoneCalendar = 'solar-bold-duotone-Calendar';
    case BoldduotoneClockCircle = 'solar-bold-duotone-ClockCircle';
    case BoldduotoneHistory = 'solar-bold-duotone-History';
    case BoldduotoneAlarm = 'solar-bold-duotone-Alarm';
    case BoldduotoneWatchSquare = 'solar-bold-duotone-WatchSquare';
    case BoldduotoneWatchSquareMinimalistic = 'solar-bold-duotone-WatchSquareMinimalistic';
    case BoldduotoneMagniferBug = 'solar-bold-duotone-MagniferBug';
    case BoldduotoneMagnifer = 'solar-bold-duotone-Magnifer';
    case BoldduotoneMagniferZoomIn = 'solar-bold-duotone-MagniferZoomIn';
    case BoldduotoneRoundedMagnifer = 'solar-bold-duotone-RoundedMagnifer';
    case BoldduotoneRoundedMagniferZoomIn = 'solar-bold-duotone-RoundedMagniferZoomIn';
    case BoldduotoneMinimalisticMagniferBug = 'solar-bold-duotone-MinimalisticMagniferBug';
    case BoldduotoneRoundedMagniferBug = 'solar-bold-duotone-RoundedMagniferBug';
    case BoldduotoneMinimalisticMagniferZoomOut = 'solar-bold-duotone-MinimalisticMagniferZoomOut';
    case BoldduotoneMinimalisticMagnifer = 'solar-bold-duotone-MinimalisticMagnifer';
    case BoldduotoneRoundedMagniferZoomOut = 'solar-bold-duotone-RoundedMagniferZoomOut';
    case BoldduotoneMinimalisticMagniferZoomIn = 'solar-bold-duotone-MinimalisticMagniferZoomIn';
    case BoldduotoneMagniferZoomOut = 'solar-bold-duotone-MagniferZoomOut';
    case BoldduotoneBagCheck = 'solar-bold-duotone-BagCheck';
    case BoldduotoneShopMinimalistic = 'solar-bold-duotone-ShopMinimalistic';
    case BoldduotoneShop = 'solar-bold-duotone-Shop';
    case BoldduotoneCartCheck = 'solar-bold-duotone-CartCheck';
    case BoldduotoneCart = 'solar-bold-duotone-Cart';
    case BoldduotoneCart3 = 'solar-bold-duotone-Cart3';
    case BoldduotoneCart2 = 'solar-bold-duotone-Cart2';
    case BoldduotoneBagMusic = 'solar-bold-duotone-BagMusic';
    case BoldduotoneCartLargeMinimalistic = 'solar-bold-duotone-CartLargeMinimalistic';
    case BoldduotoneCart5 = 'solar-bold-duotone-Cart5';
    case BoldduotoneCart4 = 'solar-bold-duotone-Cart4';
    case BoldduotoneBag = 'solar-bold-duotone-Bag';
    case BoldduotoneBagHeart = 'solar-bold-duotone-BagHeart';
    case BoldduotoneCartPlus = 'solar-bold-duotone-CartPlus';
    case BoldduotoneCartLarge = 'solar-bold-duotone-CartLarge';
    case BoldduotoneBagCross = 'solar-bold-duotone-BagCross';
    case BoldduotoneBagMusic2 = 'solar-bold-duotone-BagMusic2';
    case BoldduotoneBag5 = 'solar-bold-duotone-Bag5';
    case BoldduotoneBag4 = 'solar-bold-duotone-Bag4';
    case BoldduotoneCartLarge4 = 'solar-bold-duotone-CartLarge4';
    case BoldduotoneCartLarge3 = 'solar-bold-duotone-CartLarge3';
    case BoldduotoneBag3 = 'solar-bold-duotone-Bag3';
    case BoldduotoneBag2 = 'solar-bold-duotone-Bag2';
    case BoldduotoneShop2 = 'solar-bold-duotone-Shop2';
    case BoldduotoneCartLarge2 = 'solar-bold-duotone-CartLarge2';
    case BoldduotoneBagSmile = 'solar-bold-duotone-BagSmile';
    case BoldduotoneCartCross = 'solar-bold-duotone-CartCross';
    case BoldduotoneInfoSquare = 'solar-bold-duotone-InfoSquare';
    case BoldduotoneFlashlightOn = 'solar-bold-duotone-FlashlightOn';
    case BoldduotoneXXX = 'solar-bold-duotone-XXX';
    case BoldduotoneFigma = 'solar-bold-duotone-Figma';
    case BoldduotoneFlashlight = 'solar-bold-duotone-Flashlight';
    case BoldduotoneGhost = 'solar-bold-duotone-Ghost';
    case BoldduotoneCupMusic = 'solar-bold-duotone-CupMusic';
    case BoldduotoneBatteryFullMinimalistic = 'solar-bold-duotone-BatteryFullMinimalistic';
    case BoldduotoneDangerCircle = 'solar-bold-duotone-DangerCircle';
    case BoldduotoneCheckSquare = 'solar-bold-duotone-CheckSquare';
    case BoldduotoneGhostSmile = 'solar-bold-duotone-GhostSmile';
    case BoldduotoneTarget = 'solar-bold-duotone-Target';
    case BoldduotoneBatteryHalfMinimalistic = 'solar-bold-duotone-BatteryHalfMinimalistic';
    case BoldduotoneScissors = 'solar-bold-duotone-Scissors';
    case BoldduotonePinList = 'solar-bold-duotone-PinList';
    case BoldduotoneBatteryCharge = 'solar-bold-duotone-BatteryCharge';
    case BoldduotoneUmbrella = 'solar-bold-duotone-Umbrella';
    case BoldduotoneHomeSmile = 'solar-bold-duotone-HomeSmile';
    case BoldduotoneHome = 'solar-bold-duotone-Home';
    case BoldduotoneCopyright = 'solar-bold-duotone-Copyright';
    case BoldduotoneHomeWifi = 'solar-bold-duotone-HomeWifi';
    case BoldduotoneTShirt = 'solar-bold-duotone-TShirt';
    case BoldduotoneBatteryChargeMinimalistic = 'solar-bold-duotone-BatteryChargeMinimalistic';
    case BoldduotoneCupStar = 'solar-bold-duotone-CupStar';
    case BoldduotoneSpecialEffects = 'solar-bold-duotone-SpecialEffects';
    case BoldduotoneBody = 'solar-bold-duotone-Body';
    case BoldduotoneHamburgerMenu = 'solar-bold-duotone-HamburgerMenu';
    case BoldduotonePower = 'solar-bold-duotone-Power';
    case BoldduotoneDatabase = 'solar-bold-duotone-Database';
    case BoldduotoneCursorSquare = 'solar-bold-duotone-CursorSquare';
    case BoldduotoneFuel = 'solar-bold-duotone-Fuel';
    case BoldduotoneMentionCircle = 'solar-bold-duotone-MentionCircle';
    case BoldduotoneConfettiMinimalistic = 'solar-bold-duotone-ConfettiMinimalistic';
    case BoldduotoneMenuDotsCircle = 'solar-bold-duotone-MenuDotsCircle';
    case BoldduotonePaw = 'solar-bold-duotone-Paw';
    case BoldduotoneSubtitles = 'solar-bold-duotone-Subtitles';
    case BoldduotoneSliderVerticalMinimalistic = 'solar-bold-duotone-SliderVerticalMinimalistic';
    case BoldduotoneCrownMinimalistic = 'solar-bold-duotone-CrownMinimalistic';
    case BoldduotoneMenuDots = 'solar-bold-duotone-MenuDots';
    case BoldduotoneDelivery = 'solar-bold-duotone-Delivery';
    case BoldduotoneWaterdrop = 'solar-bold-duotone-Waterdrop';
    case BoldduotonePerfume = 'solar-bold-duotone-Perfume';
    case BoldduotoneHomeAngle2 = 'solar-bold-duotone-HomeAngle2';
    case BoldduotoneHomeWifiAngle = 'solar-bold-duotone-HomeWifiAngle';
    case BoldduotoneQuestionCircle = 'solar-bold-duotone-QuestionCircle';
    case BoldduotoneTrashBinMinimalistic = 'solar-bold-duotone-TrashBinMinimalistic';
    case BoldduotoneMagicStick3 = 'solar-bold-duotone-MagicStick3';
    case BoldduotoneAddSquare = 'solar-bold-duotone-AddSquare';
    case BoldduotoneCrownStar = 'solar-bold-duotone-CrownStar';
    case BoldduotoneMagnet = 'solar-bold-duotone-Magnet';
    case BoldduotoneConfetti = 'solar-bold-duotone-Confetti';
    case BoldduotonePin = 'solar-bold-duotone-Pin';
    case BoldduotoneMinusSquare = 'solar-bold-duotone-MinusSquare';
    case BoldduotoneBolt = 'solar-bold-duotone-Bolt';
    case BoldduotoneCloseCircle = 'solar-bold-duotone-CloseCircle';
    case BoldduotoneForbiddenCircle = 'solar-bold-duotone-ForbiddenCircle';
    case BoldduotoneMagicStick2 = 'solar-bold-duotone-MagicStick2';
    case BoldduotoneCrownLine = 'solar-bold-duotone-CrownLine';
    case BoldduotoneBoltCircle = 'solar-bold-duotone-BoltCircle';
    case BoldduotoneFlag = 'solar-bold-duotone-Flag';
    case BoldduotoneSliderHorizontal = 'solar-bold-duotone-SliderHorizontal';
    case BoldduotoneHighDefinition = 'solar-bold-duotone-HighDefinition';
    case BoldduotoneCursor = 'solar-bold-duotone-Cursor';
    case BoldduotoneFeed = 'solar-bold-duotone-Feed';
    case BoldduotoneTrafficEconomy = 'solar-bold-duotone-TrafficEconomy';
    case BoldduotoneAugmentedReality = 'solar-bold-duotone-AugmentedReality';
    case BoldduotoneIcon4K = 'solar-bold-duotone-Icon4K';
    case BoldduotoneMagnetWave = 'solar-bold-duotone-MagnetWave';
    case BoldduotoneHomeSmileAngle = 'solar-bold-duotone-HomeSmileAngle';
    case BoldduotoneSliderVertical = 'solar-bold-duotone-SliderVertical';
    case BoldduotoneCheckCircle = 'solar-bold-duotone-CheckCircle';
    case BoldduotoneCopy = 'solar-bold-duotone-Copy';
    case BoldduotoneDangerSquare = 'solar-bold-duotone-DangerSquare';
    case BoldduotoneSkirt = 'solar-bold-duotone-Skirt';
    case BoldduotoneGlasses = 'solar-bold-duotone-Glasses';
    case BoldduotoneHomeAdd = 'solar-bold-duotone-HomeAdd';
    case BoldduotoneSledgehammer = 'solar-bold-duotone-Sledgehammer';
    case BoldduotoneInfoCircle = 'solar-bold-duotone-InfoCircle';
    case BoldduotoneDangerTriangle = 'solar-bold-duotone-DangerTriangle';
    case BoldduotonePinCircle = 'solar-bold-duotone-PinCircle';
    case BoldduotoneSmartHome = 'solar-bold-duotone-SmartHome';
    case BoldduotoneScissorsSquare = 'solar-bold-duotone-ScissorsSquare';
    case BoldduotoneSleeping = 'solar-bold-duotone-Sleeping';
    case BoldduotoneBox = 'solar-bold-duotone-Box';
    case BoldduotoneCrown = 'solar-bold-duotone-Crown';
    case BoldduotoneBroom = 'solar-bold-duotone-Broom';
    case BoldduotonePostsCarouselHorizontal = 'solar-bold-duotone-PostsCarouselHorizontal';
    case BoldduotoneFlag2 = 'solar-bold-duotone-Flag2';
    case BoldduotonePlate = 'solar-bold-duotone-Plate';
    case BoldduotoneTrashBinTrash = 'solar-bold-duotone-TrashBinTrash';
    case BoldduotoneCupFirst = 'solar-bold-duotone-CupFirst';
    case BoldduotoneSmartHomeAngle = 'solar-bold-duotone-SmartHomeAngle';
    case BoldduotonePaperBin = 'solar-bold-duotone-PaperBin';
    case BoldduotoneBoxMinimalistic = 'solar-bold-duotone-BoxMinimalistic';
    case BoldduotoneDanger = 'solar-bold-duotone-Danger';
    case BoldduotoneMenuDotsSquare = 'solar-bold-duotone-MenuDotsSquare';
    case BoldduotoneHanger2 = 'solar-bold-duotone-Hanger2';
    case BoldduotoneBatteryHalf = 'solar-bold-duotone-BatteryHalf';
    case BoldduotoneHome2 = 'solar-bold-duotone-Home2';
    case BoldduotonePostsCarouselVertical = 'solar-bold-duotone-PostsCarouselVertical';
    case BoldduotoneRevote = 'solar-bold-duotone-Revote';
    case BoldduotoneMentionSquare = 'solar-bold-duotone-MentionSquare';
    case BoldduotoneWinRar = 'solar-bold-duotone-WinRar';
    case BoldduotoneForbidden = 'solar-bold-duotone-Forbidden';
    case BoldduotoneQuestionSquare = 'solar-bold-duotone-QuestionSquare';
    case BoldduotoneHanger = 'solar-bold-duotone-Hanger';
    case BoldduotoneReorder = 'solar-bold-duotone-Reorder';
    case BoldduotoneHomeAddAngle = 'solar-bold-duotone-HomeAddAngle';
    case BoldduotoneMasks = 'solar-bold-duotone-Masks';
    case BoldduotoneGift = 'solar-bold-duotone-Gift';
    case BoldduotoneCreativeCommons = 'solar-bold-duotone-CreativeCommons';
    case BoldduotoneSliderMinimalisticHorizontal = 'solar-bold-duotone-SliderMinimalisticHorizontal';
    case BoldduotoneHomeAngle = 'solar-bold-duotone-HomeAngle';
    case BoldduotoneBatteryLowMinimalistic = 'solar-bold-duotone-BatteryLowMinimalistic';
    case BoldduotoneShare = 'solar-bold-duotone-Share';
    case BoldduotoneTrashBin2 = 'solar-bold-duotone-TrashBin2';
    case BoldduotoneSort = 'solar-bold-duotone-Sort';
    case BoldduotoneMinusCircle = 'solar-bold-duotone-MinusCircle';
    case BoldduotoneExplicit = 'solar-bold-duotone-Explicit';
    case BoldduotoneTraffic = 'solar-bold-duotone-Traffic';
    case BoldduotoneFilter = 'solar-bold-duotone-Filter';
    case BoldduotoneCloseSquare = 'solar-bold-duotone-CloseSquare';
    case BoldduotoneAddCircle = 'solar-bold-duotone-AddCircle';
    case BoldduotoneFerrisWheel = 'solar-bold-duotone-FerrisWheel';
    case BoldduotoneCup = 'solar-bold-duotone-Cup';
    case BoldduotoneBalloon = 'solar-bold-duotone-Balloon';
    case BoldduotoneHelp = 'solar-bold-duotone-Help';
    case BoldduotoneBatteryFull = 'solar-bold-duotone-BatteryFull';
    case BoldduotoneCat = 'solar-bold-duotone-Cat';
    case BoldduotoneMaskSad = 'solar-bold-duotone-MaskSad';
    case BoldduotoneHighQuality = 'solar-bold-duotone-HighQuality';
    case BoldduotoneMagicStick = 'solar-bold-duotone-MagicStick';
    case BoldduotoneCosmetic = 'solar-bold-duotone-Cosmetic';
    case BoldduotoneBatteryLow = 'solar-bold-duotone-BatteryLow';
    case BoldduotoneShareCircle = 'solar-bold-duotone-ShareCircle';
    case BoldduotoneMaskHapply = 'solar-bold-duotone-MaskHapply';
    case BoldduotoneAccessibility = 'solar-bold-duotone-Accessibility';
    case BoldduotoneTrashBinMinimalistic2 = 'solar-bold-duotone-TrashBinMinimalistic2';
    case BoldduotoneIncomingCallRounded = 'solar-bold-duotone-IncomingCallRounded';
    case BoldduotoneCallDropped = 'solar-bold-duotone-CallDropped';
    case BoldduotoneCallChat = 'solar-bold-duotone-CallChat';
    case BoldduotoneCallCancelRounded = 'solar-bold-duotone-CallCancelRounded';
    case BoldduotoneCallMedicineRounded = 'solar-bold-duotone-CallMedicineRounded';
    case BoldduotoneCallDroppedRounded = 'solar-bold-duotone-CallDroppedRounded';
    case BoldduotoneRecordSquare = 'solar-bold-duotone-RecordSquare';
    case BoldduotonePhoneCalling = 'solar-bold-duotone-PhoneCalling';
    case BoldduotonePhoneRounded = 'solar-bold-duotone-PhoneRounded';
    case BoldduotoneCallMedicine = 'solar-bold-duotone-CallMedicine';
    case BoldduotoneRecordMinimalistic = 'solar-bold-duotone-RecordMinimalistic';
    case BoldduotoneEndCall = 'solar-bold-duotone-EndCall';
    case BoldduotoneOutgoingCall = 'solar-bold-duotone-OutgoingCall';
    case BoldduotoneRecordCircle = 'solar-bold-duotone-RecordCircle';
    case BoldduotoneIncomingCall = 'solar-bold-duotone-IncomingCall';
    case BoldduotoneCallChatRounded = 'solar-bold-duotone-CallChatRounded';
    case BoldduotoneEndCallRounded = 'solar-bold-duotone-EndCallRounded';
    case BoldduotonePhone = 'solar-bold-duotone-Phone';
    case BoldduotoneOutgoingCallRounded = 'solar-bold-duotone-OutgoingCallRounded';
    case BoldduotoneCallCancel = 'solar-bold-duotone-CallCancel';
    case BoldduotonePhoneCallingRounded = 'solar-bold-duotone-PhoneCallingRounded';
    case BoldduotoneStationMinimalistic = 'solar-bold-duotone-StationMinimalistic';
    case BoldduotoneSidebarCode = 'solar-bold-duotone-SidebarCode';
    case BoldduotoneWiFiRouterMinimalistic = 'solar-bold-duotone-WiFiRouterMinimalistic';
    case BoldduotoneUSB = 'solar-bold-duotone-USB';
    case BoldduotoneSiderbar = 'solar-bold-duotone-Siderbar';
    case BoldduotoneCode2 = 'solar-bold-duotone-Code2';
    case BoldduotoneSlashCircle = 'solar-bold-duotone-SlashCircle';
    case BoldduotoneScreencast = 'solar-bold-duotone-Screencast';
    case BoldduotoneHashtagSquare = 'solar-bold-duotone-HashtagSquare';
    case BoldduotoneSidebarMinimalistic = 'solar-bold-duotone-SidebarMinimalistic';
    case BoldduotoneCode = 'solar-bold-duotone-Code';
    case BoldduotoneUsbSquare = 'solar-bold-duotone-UsbSquare';
    case BoldduotoneWiFiRouter = 'solar-bold-duotone-WiFiRouter';
    case BoldduotoneCodeCircle = 'solar-bold-duotone-CodeCircle';
    case BoldduotoneTranslation = 'solar-bold-duotone-Translation';
    case BoldduotoneBugMinimalistic = 'solar-bold-duotone-BugMinimalistic';
    case BoldduotoneStation = 'solar-bold-duotone-Station';
    case BoldduotoneProgramming = 'solar-bold-duotone-Programming';
    case BoldduotoneWiFiRouterRound = 'solar-bold-duotone-WiFiRouterRound';
    case BoldduotoneHashtag = 'solar-bold-duotone-Hashtag';
    case BoldduotoneBug = 'solar-bold-duotone-Bug';
    case BoldduotoneHashtagChat = 'solar-bold-duotone-HashtagChat';
    case BoldduotoneCommand = 'solar-bold-duotone-Command';
    case BoldduotoneTranslation2 = 'solar-bold-duotone-Translation2';
    case BoldduotoneHashtagCircle = 'solar-bold-duotone-HashtagCircle';
    case BoldduotoneScreencast2 = 'solar-bold-duotone-Screencast2';
    case BoldduotoneSlashSquare = 'solar-bold-duotone-SlashSquare';
    case BoldduotoneWindowFrame = 'solar-bold-duotone-WindowFrame';
    case BoldduotoneStructure = 'solar-bold-duotone-Structure';
    case BoldduotoneUsbCircle = 'solar-bold-duotone-UsbCircle';
    case BoldduotoneCodeSquare = 'solar-bold-duotone-CodeSquare';
    case BoldduotoneNotes = 'solar-bold-duotone-Notes';
    case BoldduotoneDocumentText = 'solar-bold-duotone-DocumentText';
    case BoldduotoneDocumentAdd = 'solar-bold-duotone-DocumentAdd';
    case BoldduotoneDocumentMedicine = 'solar-bold-duotone-DocumentMedicine';
    case BoldduotoneArchiveMinimalistic = 'solar-bold-duotone-ArchiveMinimalistic';
    case BoldduotoneClipboard = 'solar-bold-duotone-Clipboard';
    case BoldduotoneClipboardAdd = 'solar-bold-duotone-ClipboardAdd';
    case BoldduotoneArchive = 'solar-bold-duotone-Archive';
    case BoldduotoneClipboardHeart = 'solar-bold-duotone-ClipboardHeart';
    case BoldduotoneClipboardRemove = 'solar-bold-duotone-ClipboardRemove';
    case BoldduotoneClipboardText = 'solar-bold-duotone-ClipboardText';
    case BoldduotoneDocument = 'solar-bold-duotone-Document';
    case BoldduotoneNotesMinimalistic = 'solar-bold-duotone-NotesMinimalistic';
    case BoldduotoneArchiveUp = 'solar-bold-duotone-ArchiveUp';
    case BoldduotoneArchiveUpMinimlistic = 'solar-bold-duotone-ArchiveUpMinimlistic';
    case BoldduotoneArchiveCheck = 'solar-bold-duotone-ArchiveCheck';
    case BoldduotoneArchiveDown = 'solar-bold-duotone-ArchiveDown';
    case BoldduotoneArchiveDownMinimlistic = 'solar-bold-duotone-ArchiveDownMinimlistic';
    case BoldduotoneDocumentsMinimalistic = 'solar-bold-duotone-DocumentsMinimalistic';
    case BoldduotoneClipboardCheck = 'solar-bold-duotone-ClipboardCheck';
    case BoldduotoneClipboardList = 'solar-bold-duotone-ClipboardList';
    case BoldduotoneDocuments = 'solar-bold-duotone-Documents';
    case BoldduotoneNotebook = 'solar-bold-duotone-Notebook';
    case BoldduotoneGalleryRound = 'solar-bold-duotone-GalleryRound';
    case BoldduotonePlayCircle = 'solar-bold-duotone-PlayCircle';
    case BoldduotoneStream = 'solar-bold-duotone-Stream';
    case BoldduotoneGalleryRemove = 'solar-bold-duotone-GalleryRemove';
    case BoldduotoneClapperboard = 'solar-bold-duotone-Clapperboard';
    case BoldduotonePauseCircle = 'solar-bold-duotone-PauseCircle';
    case BoldduotoneRewind5SecondsBack = 'solar-bold-duotone-Rewind5SecondsBack';
    case BoldduotoneRepeat = 'solar-bold-duotone-Repeat';
    case BoldduotoneClapperboardEdit = 'solar-bold-duotone-ClapperboardEdit';
    case BoldduotoneVideoFrameCut = 'solar-bold-duotone-VideoFrameCut';
    case BoldduotonePanorama = 'solar-bold-duotone-Panorama';
    case BoldduotonePlayStream = 'solar-bold-duotone-PlayStream';
    case BoldduotoneClapperboardOpen = 'solar-bold-duotone-ClapperboardOpen';
    case BoldduotoneClapperboardText = 'solar-bold-duotone-ClapperboardText';
    case BoldduotoneLibrary = 'solar-bold-duotone-Library';
    case BoldduotoneReel2 = 'solar-bold-duotone-Reel2';
    case BoldduotoneVolumeSmall = 'solar-bold-duotone-VolumeSmall';
    case BoldduotoneVideoFrame = 'solar-bold-duotone-VideoFrame';
    case BoldduotoneMicrophoneLarge = 'solar-bold-duotone-MicrophoneLarge';
    case BoldduotoneRewindForward = 'solar-bold-duotone-RewindForward';
    case BoldduotoneRewindBackCircle = 'solar-bold-duotone-RewindBackCircle';
    case BoldduotoneMicrophone = 'solar-bold-duotone-Microphone';
    case BoldduotoneVideoFrameReplace = 'solar-bold-duotone-VideoFrameReplace';
    case BoldduotoneClapperboardPlay = 'solar-bold-duotone-ClapperboardPlay';
    case BoldduotoneGalleryDownload = 'solar-bold-duotone-GalleryDownload';
    case BoldduotoneMusicNote4 = 'solar-bold-duotone-MusicNote4';
    case BoldduotoneVideocameraRecord = 'solar-bold-duotone-VideocameraRecord';
    case BoldduotonePlaybackSpeed = 'solar-bold-duotone-PlaybackSpeed';
    case BoldduotoneSoundwave = 'solar-bold-duotone-Soundwave';
    case BoldduotoneStopCircle = 'solar-bold-duotone-StopCircle';
    case BoldduotoneQuitFullScreenCircle = 'solar-bold-duotone-QuitFullScreenCircle';
    case BoldduotoneRewindBack = 'solar-bold-duotone-RewindBack';
    case BoldduotoneRepeatOne = 'solar-bold-duotone-RepeatOne';
    case BoldduotoneGalleryCheck = 'solar-bold-duotone-GalleryCheck';
    case BoldduotoneWallpaper = 'solar-bold-duotone-Wallpaper';
    case BoldduotoneRewindForwardCircle = 'solar-bold-duotone-RewindForwardCircle';
    case BoldduotoneGalleryEdit = 'solar-bold-duotone-GalleryEdit';
    case BoldduotoneGallery = 'solar-bold-duotone-Gallery';
    case BoldduotoneGalleryMinimalistic = 'solar-bold-duotone-GalleryMinimalistic';
    case BoldduotoneUploadTrack = 'solar-bold-duotone-UploadTrack';
    case BoldduotoneVolume = 'solar-bold-duotone-Volume';
    case BoldduotoneUploadTrack2 = 'solar-bold-duotone-UploadTrack2';
    case BoldduotoneMusicNotes = 'solar-bold-duotone-MusicNotes';
    case BoldduotoneMusicNote2 = 'solar-bold-duotone-MusicNote2';
    case BoldduotoneCameraAdd = 'solar-bold-duotone-CameraAdd';
    case BoldduotonePodcast = 'solar-bold-duotone-Podcast';
    case BoldduotoneCameraRotate = 'solar-bold-duotone-CameraRotate';
    case BoldduotoneMusicNote3 = 'solar-bold-duotone-MusicNote3';
    case BoldduotoneStop = 'solar-bold-duotone-Stop';
    case BoldduotoneMuted = 'solar-bold-duotone-Muted';
    case BoldduotoneSkipNext = 'solar-bold-duotone-SkipNext';
    case BoldduotoneGallerySend = 'solar-bold-duotone-GallerySend';
    case BoldduotoneRecord = 'solar-bold-duotone-Record';
    case BoldduotoneFullScreenCircle = 'solar-bold-duotone-FullScreenCircle';
    case BoldduotoneVolumeCross = 'solar-bold-duotone-VolumeCross';
    case BoldduotoneSoundwaveCircle = 'solar-bold-duotone-SoundwaveCircle';
    case BoldduotoneSkipPrevious = 'solar-bold-duotone-SkipPrevious';
    case BoldduotoneRewind5SecondsForward = 'solar-bold-duotone-Rewind5SecondsForward';
    case BoldduotonePlay = 'solar-bold-duotone-Play';
    case BoldduotonePIP = 'solar-bold-duotone-PIP';
    case BoldduotoneMusicLibrary = 'solar-bold-duotone-MusicLibrary';
    case BoldduotoneVideoFrame2 = 'solar-bold-duotone-VideoFrame2';
    case BoldduotoneCamera = 'solar-bold-duotone-Camera';
    case BoldduotoneQuitPip = 'solar-bold-duotone-QuitPip';
    case BoldduotoneClapperboardOpenPlay = 'solar-bold-duotone-ClapperboardOpenPlay';
    case BoldduotoneRewind10SecondsBack = 'solar-bold-duotone-Rewind10SecondsBack';
    case BoldduotoneRepeatOneMinimalistic = 'solar-bold-duotone-RepeatOneMinimalistic';
    case BoldduotoneVinyl = 'solar-bold-duotone-Vinyl';
    case BoldduotoneVideoLibrary = 'solar-bold-duotone-VideoLibrary';
    case BoldduotoneGalleryWide = 'solar-bold-duotone-GalleryWide';
    case BoldduotoneReel = 'solar-bold-duotone-Reel';
    case BoldduotoneToPip = 'solar-bold-duotone-ToPip';
    case BoldduotonePip2 = 'solar-bold-duotone-Pip2';
    case BoldduotoneFullScreen = 'solar-bold-duotone-FullScreen';
    case BoldduotoneCameraMinimalistic = 'solar-bold-duotone-CameraMinimalistic';
    case BoldduotoneVideoFrameCut2 = 'solar-bold-duotone-VideoFrameCut2';
    case BoldduotoneGalleryCircle = 'solar-bold-duotone-GalleryCircle';
    case BoldduotoneVideoFramePlayHorizontal = 'solar-bold-duotone-VideoFramePlayHorizontal';
    case BoldduotoneMusicNoteSlider2 = 'solar-bold-duotone-MusicNoteSlider2';
    case BoldduotoneMusicNoteSlider = 'solar-bold-duotone-MusicNoteSlider';
    case BoldduotoneVideocameraAdd = 'solar-bold-duotone-VideocameraAdd';
    case BoldduotoneQuitFullScreenSquare = 'solar-bold-duotone-QuitFullScreenSquare';
    case BoldduotoneAlbum = 'solar-bold-duotone-Album';
    case BoldduotoneGalleryAdd = 'solar-bold-duotone-GalleryAdd';
    case BoldduotoneCameraSquare = 'solar-bold-duotone-CameraSquare';
    case BoldduotoneRewind15SecondsBack = 'solar-bold-duotone-Rewind15SecondsBack';
    case BoldduotoneRewind15SecondsForward = 'solar-bold-duotone-Rewind15SecondsForward';
    case BoldduotoneVinylRecord = 'solar-bold-duotone-VinylRecord';
    case BoldduotoneShuffle = 'solar-bold-duotone-Shuffle';
    case BoldduotonePause = 'solar-bold-duotone-Pause';
    case BoldduotoneMusicNote = 'solar-bold-duotone-MusicNote';
    case BoldduotoneQuitFullScreen = 'solar-bold-duotone-QuitFullScreen';
    case BoldduotoneMicrophone2 = 'solar-bold-duotone-Microphone2';
    case BoldduotoneVideocamera = 'solar-bold-duotone-Videocamera';
    case BoldduotoneGalleryFavourite = 'solar-bold-duotone-GalleryFavourite';
    case BoldduotoneMusicLibrary2 = 'solar-bold-duotone-MusicLibrary2';
    case BoldduotoneVideoFramePlayVertical = 'solar-bold-duotone-VideoFramePlayVertical';
    case BoldduotoneFullScreenSquare = 'solar-bold-duotone-FullScreenSquare';
    case BoldduotoneRewind10SecondsForward = 'solar-bold-duotone-Rewind10SecondsForward';
    case BoldduotoneVolumeLoud = 'solar-bold-duotone-VolumeLoud';
    case BoldduotoneMicrophone3 = 'solar-bold-duotone-Microphone3';
    case BoldduotoneSoundwaveSquare = 'solar-bold-duotone-SoundwaveSquare';
    case BoldduotoneCardholder = 'solar-bold-duotone-Cardholder';
    case BoldduotoneBillList = 'solar-bold-duotone-BillList';
    case BoldduotoneSaleSquare = 'solar-bold-duotone-SaleSquare';
    case BoldduotoneDollar = 'solar-bold-duotone-Dollar';
    case BoldduotoneTicket = 'solar-bold-duotone-Ticket';
    case BoldduotoneTag = 'solar-bold-duotone-Tag';
    case BoldduotoneCashOut = 'solar-bold-duotone-CashOut';
    case BoldduotoneWallet2 = 'solar-bold-duotone-Wallet2';
    case BoldduotoneRuble = 'solar-bold-duotone-Ruble';
    case BoldduotoneCardTransfer = 'solar-bold-duotone-CardTransfer';
    case BoldduotoneEuro = 'solar-bold-duotone-Euro';
    case BoldduotoneSale = 'solar-bold-duotone-Sale';
    case BoldduotoneCardSearch = 'solar-bold-duotone-CardSearch';
    case BoldduotoneWallet = 'solar-bold-duotone-Wallet';
    case BoldduotoneBillCross = 'solar-bold-duotone-BillCross';
    case BoldduotoneTicketSale = 'solar-bold-duotone-TicketSale';
    case BoldduotoneSafeSquare = 'solar-bold-duotone-SafeSquare';
    case BoldduotoneCard = 'solar-bold-duotone-Card';
    case BoldduotoneSafe2 = 'solar-bold-duotone-Safe2';
    case BoldduotoneDollarMinimalistic = 'solar-bold-duotone-DollarMinimalistic';
    case BoldduotoneTagPrice = 'solar-bold-duotone-TagPrice';
    case BoldduotoneMoneyBag = 'solar-bold-duotone-MoneyBag';
    case BoldduotoneBill = 'solar-bold-duotone-Bill';
    case BoldduotoneCardSend = 'solar-bold-duotone-CardSend';
    case BoldduotoneCardRecive = 'solar-bold-duotone-CardRecive';
    case BoldduotoneBanknote2 = 'solar-bold-duotone-Banknote2';
    case BoldduotoneTagHorizontal = 'solar-bold-duotone-TagHorizontal';
    case BoldduotoneBillCheck = 'solar-bold-duotone-BillCheck';
    case BoldduotoneTickerStar = 'solar-bold-duotone-TickerStar';
    case BoldduotoneBanknote = 'solar-bold-duotone-Banknote';
    case BoldduotoneVerifiedCheck = 'solar-bold-duotone-VerifiedCheck';
    case BoldduotoneWadOfMoney = 'solar-bold-duotone-WadOfMoney';
    case BoldduotoneCard2 = 'solar-bold-duotone-Card2';
    case BoldduotoneSafeCircle = 'solar-bold-duotone-SafeCircle';
    case BoldduotoneWalletMoney = 'solar-bold-duotone-WalletMoney';
    case BoldduotoneList = 'solar-bold-duotone-List';
    case BoldduotoneListDownMinimalistic = 'solar-bold-duotone-ListDownMinimalistic';
    case BoldduotonePlaylist2 = 'solar-bold-duotone-Playlist2';
    case BoldduotoneChecklistMinimalistic = 'solar-bold-duotone-ChecklistMinimalistic';
    case BoldduotonePlaaylistMinimalistic = 'solar-bold-duotone-PlaaylistMinimalistic';
    case BoldduotoneListHeart = 'solar-bold-duotone-ListHeart';
    case BoldduotoneListArrowDown = 'solar-bold-duotone-ListArrowDown';
    case BoldduotoneListArrowUp = 'solar-bold-duotone-ListArrowUp';
    case BoldduotoneListUpMinimalistic = 'solar-bold-duotone-ListUpMinimalistic';
    case BoldduotonePlaylist = 'solar-bold-duotone-Playlist';
    case BoldduotoneListUp = 'solar-bold-duotone-ListUp';
    case BoldduotoneListCrossMinimalistic = 'solar-bold-duotone-ListCrossMinimalistic';
    case BoldduotoneListCross = 'solar-bold-duotone-ListCross';
    case BoldduotoneListArrowDownMinimalistic = 'solar-bold-duotone-ListArrowDownMinimalistic';
    case BoldduotoneSortByAlphabet = 'solar-bold-duotone-SortByAlphabet';
    case BoldduotoneChecklist = 'solar-bold-duotone-Checklist';
    case BoldduotoneSortFromBottomToTop = 'solar-bold-duotone-SortFromBottomToTop';
    case BoldduotoneListCheck = 'solar-bold-duotone-ListCheck';
    case BoldduotonePlaylistMinimalistic2 = 'solar-bold-duotone-PlaylistMinimalistic2';
    case BoldduotonePlaylistMinimalistic3 = 'solar-bold-duotone-PlaylistMinimalistic3';
    case BoldduotoneList1 = 'solar-bold-duotone-List1';
    case BoldduotoneSortFromTopToBottom = 'solar-bold-duotone-SortFromTopToBottom';
    case BoldduotoneSortByTime = 'solar-bold-duotone-SortByTime';
    case BoldduotoneListDown = 'solar-bold-duotone-ListDown';
    case BoldduotoneListHeartMinimalistic = 'solar-bold-duotone-ListHeartMinimalistic';
    case BoldduotoneListCheckMinimalistic = 'solar-bold-duotone-ListCheckMinimalistic';
    case BoldduotoneListArrowUpMinimalistic = 'solar-bold-duotone-ListArrowUpMinimalistic';
    case BoldduotoneUserCrossRounded = 'solar-bold-duotone-UserCrossRounded';
    case BoldduotoneUser = 'solar-bold-duotone-User';
    case BoldduotoneUsersGroupRounded = 'solar-bold-duotone-UsersGroupRounded';
    case BoldduotoneUserPlusRounded = 'solar-bold-duotone-UserPlusRounded';
    case BoldduotoneUserBlock = 'solar-bold-duotone-UserBlock';
    case BoldduotoneUserMinus = 'solar-bold-duotone-UserMinus';
    case BoldduotoneUserHands = 'solar-bold-duotone-UserHands';
    case BoldduotoneUserHeart = 'solar-bold-duotone-UserHeart';
    case BoldduotoneUserMinusRounded = 'solar-bold-duotone-UserMinusRounded';
    case BoldduotoneUserCross = 'solar-bold-duotone-UserCross';
    case BoldduotoneUserSpeakRounded = 'solar-bold-duotone-UserSpeakRounded';
    case BoldduotoneUserId = 'solar-bold-duotone-UserId';
    case BoldduotoneUserBlockRounded = 'solar-bold-duotone-UserBlockRounded';
    case BoldduotoneUserHeartRounded = 'solar-bold-duotone-UserHeartRounded';
    case BoldduotoneUsersGroupTwoRounded = 'solar-bold-duotone-UsersGroupTwoRounded';
    case BoldduotoneUserHandUp = 'solar-bold-duotone-UserHandUp';
    case BoldduotoneUserCircle = 'solar-bold-duotone-UserCircle';
    case BoldduotoneUserRounded = 'solar-bold-duotone-UserRounded';
    case BoldduotoneUserCheck = 'solar-bold-duotone-UserCheck';
    case BoldduotoneUserPlus = 'solar-bold-duotone-UserPlus';
    case BoldduotoneUserCheckRounded = 'solar-bold-duotone-UserCheckRounded';
    case BoldduotoneUserSpeak = 'solar-bold-duotone-UserSpeak';
    case BoldduotoneVirus = 'solar-bold-duotone-Virus';
    case BoldduotoneAdhesivePlaster2 = 'solar-bold-duotone-AdhesivePlaster2';
    case BoldduotoneDropper = 'solar-bold-duotone-Dropper';
    case BoldduotonePulse2 = 'solar-bold-duotone-Pulse2';
    case BoldduotoneBoneBroken = 'solar-bold-duotone-BoneBroken';
    case BoldduotoneHeartPulse2 = 'solar-bold-duotone-HeartPulse2';
    case BoldduotoneMedicalKit = 'solar-bold-duotone-MedicalKit';
    case BoldduotoneTestTube = 'solar-bold-duotone-TestTube';
    case BoldduotoneHealth = 'solar-bold-duotone-Health';
    case BoldduotoneDropperMinimalistic2 = 'solar-bold-duotone-DropperMinimalistic2';
    case BoldduotoneDNA = 'solar-bold-duotone-DNA';
    case BoldduotoneDropper3 = 'solar-bold-duotone-Dropper3';
    case BoldduotoneThermometer = 'solar-bold-duotone-Thermometer';
    case BoldduotoneDropper2 = 'solar-bold-duotone-Dropper2';
    case BoldduotoneJarOfPills2 = 'solar-bold-duotone-JarOfPills2';
    case BoldduotoneBoneCrack = 'solar-bold-duotone-BoneCrack';
    case BoldduotoneJarOfPills = 'solar-bold-duotone-JarOfPills';
    case BoldduotoneSyringe = 'solar-bold-duotone-Syringe';
    case BoldduotoneStethoscope = 'solar-bold-duotone-Stethoscope';
    case BoldduotoneBenzeneRing = 'solar-bold-duotone-BenzeneRing';
    case BoldduotoneBacteria = 'solar-bold-duotone-Bacteria';
    case BoldduotoneAdhesivePlaster = 'solar-bold-duotone-AdhesivePlaster';
    case BoldduotoneBone = 'solar-bold-duotone-Bone';
    case BoldduotoneBones = 'solar-bold-duotone-Bones';
    case BoldduotonePill = 'solar-bold-duotone-Pill';
    case BoldduotonePills = 'solar-bold-duotone-Pills';
    case BoldduotoneHeartPulse = 'solar-bold-duotone-HeartPulse';
    case BoldduotoneTestTubeMinimalistic = 'solar-bold-duotone-TestTubeMinimalistic';
    case BoldduotonePills2 = 'solar-bold-duotone-Pills2';
    case BoldduotonePulse = 'solar-bold-duotone-Pulse';
    case BoldduotoneDropperMinimalistic = 'solar-bold-duotone-DropperMinimalistic';
    case BoldduotonePills3 = 'solar-bold-duotone-Pills3';
    case BoldduotoneWhisk = 'solar-bold-duotone-Whisk';
    case BoldduotoneBottle = 'solar-bold-duotone-Bottle';
    case BoldduotoneOvenMittsMinimalistic = 'solar-bold-duotone-OvenMittsMinimalistic';
    case BoldduotoneChefHatMinimalistic = 'solar-bold-duotone-ChefHatMinimalistic';
    case BoldduotoneTeaCup = 'solar-bold-duotone-TeaCup';
    case BoldduotoneWineglassTriangle = 'solar-bold-duotone-WineglassTriangle';
    case BoldduotoneOvenMitts = 'solar-bold-duotone-OvenMitts';
    case BoldduotoneCupPaper = 'solar-bold-duotone-CupPaper';
    case BoldduotoneLadle = 'solar-bold-duotone-Ladle';
    case BoldduotoneCorkscrew = 'solar-bold-duotone-Corkscrew';
    case BoldduotoneDonutBitten = 'solar-bold-duotone-DonutBitten';
    case BoldduotoneWineglass = 'solar-bold-duotone-Wineglass';
    case BoldduotoneDonut = 'solar-bold-duotone-Donut';
    case BoldduotoneCupHot = 'solar-bold-duotone-CupHot';
    case BoldduotoneChefHatHeart = 'solar-bold-duotone-ChefHatHeart';
    case BoldduotoneChefHat = 'solar-bold-duotone-ChefHat';
    case BoldduotoneRollingPin = 'solar-bold-duotone-RollingPin';
    case BoldduotoneCodeFile = 'solar-bold-duotone-CodeFile';
    case BoldduotoneFileCorrupted = 'solar-bold-duotone-FileCorrupted';
    case BoldduotoneFile = 'solar-bold-duotone-File';
    case BoldduotoneFileRight = 'solar-bold-duotone-FileRight';
    case BoldduotoneFileFavourite = 'solar-bold-duotone-FileFavourite';
    case BoldduotoneFileDownload = 'solar-bold-duotone-FileDownload';
    case BoldduotoneZipFile = 'solar-bold-duotone-ZipFile';
    case BoldduotoneFileText = 'solar-bold-duotone-FileText';
    case BoldduotoneFileSmile = 'solar-bold-duotone-FileSmile)';
    case BoldduotoneFileCheck = 'solar-bold-duotone-FileCheck';
    case BoldduotoneFileSend = 'solar-bold-duotone-FileSend';
    case BoldduotoneFileLeft = 'solar-bold-duotone-FileLeft';
    case BoldduotoneFigmaFile = 'solar-bold-duotone-FigmaFile';
    case BoldduotoneFileRemove = 'solar-bold-duotone-FileRemove';
    case BoldduotoneCloudFile = 'solar-bold-duotone-CloudFile';
    case BoldduotoneRemoveFolder = 'solar-bold-duotone-RemoveFolder';
    case BoldduotoneFolderFavouritestar = 'solar-bold-duotone-FolderFavourite(star)';
    case BoldduotoneAddFolder = 'solar-bold-duotone-AddFolder';
    case BoldduotoneFolderCheck = 'solar-bold-duotone-FolderCheck';
    case BoldduotoneFolderFavouritebookmark = 'solar-bold-duotone-FolderFavourite(bookmark)';
    case BoldduotoneFolder2 = 'solar-bold-duotone-Folder2';
    case BoldduotoneFolderSecurity = 'solar-bold-duotone-FolderSecurity';
    case BoldduotoneFolderCloud = 'solar-bold-duotone-FolderCloud';
    case BoldduotoneMoveToFolder = 'solar-bold-duotone-MoveToFolder';
    case BoldduotoneFolderError = 'solar-bold-duotone-FolderError';
    case BoldduotoneFolderPathConnect = 'solar-bold-duotone-FolderPathConnect';
    case BoldduotoneFolderOpen = 'solar-bold-duotone-FolderOpen';
    case BoldduotoneFolder = 'solar-bold-duotone-Folder';
    case BoldduotoneFolderWithFiles = 'solar-bold-duotone-FolderWithFiles';
    case BoldduotoneCloudCheck = 'solar-bold-duotone-CloudCheck';
    case BoldduotoneTemperature = 'solar-bold-duotone-Temperature';
    case BoldduotoneWind = 'solar-bold-duotone-Wind';
    case BoldduotoneCloudSnowfall = 'solar-bold-duotone-CloudSnowfall';
    case BoldduotoneSunrise = 'solar-bold-duotone-Sunrise';
    case BoldduotoneSun2 = 'solar-bold-duotone-Sun2';
    case BoldduotoneCloudSun = 'solar-bold-duotone-CloudSun';
    case BoldduotoneCloudBoltMinimalistic = 'solar-bold-duotone-CloudBoltMinimalistic';
    case BoldduotoneCloudDownload = 'solar-bold-duotone-CloudDownload';
    case BoldduotoneClouds = 'solar-bold-duotone-Clouds';
    case BoldduotoneTornado = 'solar-bold-duotone-Tornado';
    case BoldduotoneMoonSleep = 'solar-bold-duotone-MoonSleep';
    case BoldduotoneCloudUpload = 'solar-bold-duotone-CloudUpload';
    case BoldduotoneCloudRain = 'solar-bold-duotone-CloudRain';
    case BoldduotoneFog = 'solar-bold-duotone-Fog';
    case BoldduotoneSnowflake = 'solar-bold-duotone-Snowflake';
    case BoldduotoneMoonFog = 'solar-bold-duotone-MoonFog';
    case BoldduotoneCloudMinus = 'solar-bold-duotone-CloudMinus';
    case BoldduotoneCloudBolt = 'solar-bold-duotone-CloudBolt';
    case BoldduotoneCloudWaterdrop = 'solar-bold-duotone-CloudWaterdrop';
    case BoldduotoneSunset = 'solar-bold-duotone-Sunset';
    case BoldduotoneWaterdrops = 'solar-bold-duotone-Waterdrops';
    case BoldduotoneMoonStars = 'solar-bold-duotone-MoonStars';
    case BoldduotoneCloudPlus = 'solar-bold-duotone-CloudPlus';
    case BoldduotoneSun = 'solar-bold-duotone-Sun';
    case BoldduotoneCloudWaterdrops = 'solar-bold-duotone-CloudWaterdrops';
    case BoldduotoneCloudSun2 = 'solar-bold-duotone-CloudSun2';
    case BoldduotoneCloudyMoon = 'solar-bold-duotone-CloudyMoon';
    case BoldduotoneTornadoSmall = 'solar-bold-duotone-TornadoSmall';
    case BoldduotoneCloud = 'solar-bold-duotone-Cloud';
    case BoldduotoneSunFog = 'solar-bold-duotone-SunFog';
    case BoldduotoneCloundCross = 'solar-bold-duotone-CloundCross';
    case BoldduotoneCloudSnowfallMinimalistic = 'solar-bold-duotone-CloudSnowfallMinimalistic';
    case BoldduotoneCloudStorm = 'solar-bold-duotone-CloudStorm';
    case BoldduotoneMoon = 'solar-bold-duotone-Moon';
    case BoldduotoneRefreshCircle = 'solar-bold-duotone-RefreshCircle';
    case BoldduotoneSquareArrowRightDown = 'solar-bold-duotone-SquareArrowRightDown';
    case BoldduotoneRoundArrowLeftDown = 'solar-bold-duotone-RoundArrowLeftDown';
    case BoldduotoneRestart = 'solar-bold-duotone-Restart';
    case BoldduotoneRoundAltArrowDown = 'solar-bold-duotone-RoundAltArrowDown';
    case BoldduotoneRoundSortVertical = 'solar-bold-duotone-RoundSortVertical';
    case BoldduotoneSquareAltArrowUp = 'solar-bold-duotone-SquareAltArrowUp';
    case BoldduotoneArrowLeftUp = 'solar-bold-duotone-ArrowLeftUp';
    case BoldduotoneSortHorizontal = 'solar-bold-duotone-SortHorizontal';
    case BoldduotoneTransferHorizontal = 'solar-bold-duotone-TransferHorizontal';
    case BoldduotoneSquareDoubleAltArrowUp = 'solar-bold-duotone-SquareDoubleAltArrowUp';
    case BoldduotoneRoundArrowLeftUp = 'solar-bold-duotone-RoundArrowLeftUp';
    case BoldduotoneAltArrowRight = 'solar-bold-duotone-AltArrowRight';
    case BoldduotoneRoundDoubleAltArrowUp = 'solar-bold-duotone-RoundDoubleAltArrowUp';
    case BoldduotoneRestartCircle = 'solar-bold-duotone-RestartCircle';
    case BoldduotoneSquareArrowDown = 'solar-bold-duotone-SquareArrowDown';
    case BoldduotoneSortVertical = 'solar-bold-duotone-SortVertical';
    case BoldduotoneSquareSortHorizontal = 'solar-bold-duotone-SquareSortHorizontal';
    case BoldduotoneDoubleAltArrowLeft = 'solar-bold-duotone-DoubleAltArrowLeft';
    case BoldduotoneSquareAltArrowDown = 'solar-bold-duotone-SquareAltArrowDown';
    case BoldduotoneSquareAltArrowRight = 'solar-bold-duotone-SquareAltArrowRight';
    case BoldduotoneSquareArrowUp = 'solar-bold-duotone-SquareArrowUp';
    case BoldduotoneDoubleAltArrowRight = 'solar-bold-duotone-DoubleAltArrowRight';
    case BoldduotoneRoundTransferVertical = 'solar-bold-duotone-RoundTransferVertical';
    case BoldduotoneArrowLeft = 'solar-bold-duotone-ArrowLeft';
    case BoldduotoneRoundDoubleAltArrowRight = 'solar-bold-duotone-RoundDoubleAltArrowRight';
    case BoldduotoneSquareDoubleAltArrowLeft = 'solar-bold-duotone-SquareDoubleAltArrowLeft';
    case BoldduotoneAltArrowDown = 'solar-bold-duotone-AltArrowDown';
    case BoldduotoneRoundTransferHorizontal = 'solar-bold-duotone-RoundTransferHorizontal';
    case BoldduotoneRoundArrowRightDown = 'solar-bold-duotone-RoundArrowRightDown';
    case BoldduotoneArrowUp = 'solar-bold-duotone-ArrowUp';
    case BoldduotoneRoundArrowLeft = 'solar-bold-duotone-RoundArrowLeft';
    case BoldduotoneDoubleAltArrowUp = 'solar-bold-duotone-DoubleAltArrowUp';
    case BoldduotoneRoundArrowRight = 'solar-bold-duotone-RoundArrowRight';
    case BoldduotoneSquareTransferHorizontal = 'solar-bold-duotone-SquareTransferHorizontal';
    case BoldduotoneArrowRight = 'solar-bold-duotone-ArrowRight';
    case BoldduotoneRoundDoubleAltArrowLeft = 'solar-bold-duotone-RoundDoubleAltArrowLeft';
    case BoldduotoneRoundArrowUp = 'solar-bold-duotone-RoundArrowUp';
    case BoldduotoneSquareSortVertical = 'solar-bold-duotone-SquareSortVertical';
    case BoldduotoneAltArrowLeft = 'solar-bold-duotone-AltArrowLeft';
    case BoldduotoneSquareDoubleAltArrowRight = 'solar-bold-duotone-SquareDoubleAltArrowRight';
    case BoldduotoneRefresh = 'solar-bold-duotone-Refresh';
    case BoldduotoneTransferVertical = 'solar-bold-duotone-TransferVertical';
    case BoldduotoneRefreshSquare = 'solar-bold-duotone-RefreshSquare';
    case BoldduotoneSquareTransferVertical = 'solar-bold-duotone-SquareTransferVertical';
    case BoldduotoneSquareDoubleAltArrowDown = 'solar-bold-duotone-SquareDoubleAltArrowDown';
    case BoldduotoneRoundArrowRightUp = 'solar-bold-duotone-RoundArrowRightUp';
    case BoldduotoneArrowDown = 'solar-bold-duotone-ArrowDown';
    case BoldduotoneRestartSquare = 'solar-bold-duotone-RestartSquare';
    case BoldduotoneSquareArrowRight = 'solar-bold-duotone-SquareArrowRight';
    case BoldduotoneRoundDoubleAltArrowDown = 'solar-bold-duotone-RoundDoubleAltArrowDown';
    case BoldduotoneSquareArrowLeftUp = 'solar-bold-duotone-SquareArrowLeftUp';
    case BoldduotoneRoundArrowDown = 'solar-bold-duotone-RoundArrowDown';
    case BoldduotoneSquareArrowRightUp = 'solar-bold-duotone-SquareArrowRightUp';
    case BoldduotoneRoundTransferDiagonal = 'solar-bold-duotone-RoundTransferDiagonal';
    case BoldduotoneArrowRightDown = 'solar-bold-duotone-ArrowRightDown';
    case BoldduotoneArrowLeftDown = 'solar-bold-duotone-ArrowLeftDown';
    case BoldduotoneRoundAltArrowLeft = 'solar-bold-duotone-RoundAltArrowLeft';
    case BoldduotoneArrowRightUp = 'solar-bold-duotone-ArrowRightUp';
    case BoldduotoneSquareArrowLeftDown = 'solar-bold-duotone-SquareArrowLeftDown';
    case BoldduotoneRoundAltArrowUp = 'solar-bold-duotone-RoundAltArrowUp';
    case BoldduotoneAltArrowUp = 'solar-bold-duotone-AltArrowUp';
    case BoldduotoneSquareAltArrowLeft = 'solar-bold-duotone-SquareAltArrowLeft';
    case BoldduotoneRoundSortHorizontal = 'solar-bold-duotone-RoundSortHorizontal';
    case BoldduotoneDoubleAltArrowDown = 'solar-bold-duotone-DoubleAltArrowDown';
    case BoldduotoneRoundAltArrowRight = 'solar-bold-duotone-RoundAltArrowRight';
    case BoldduotoneSquareArrowLeft = 'solar-bold-duotone-SquareArrowLeft';
    case BoldduotoneTuningSquare2 = 'solar-bold-duotone-TuningSquare2';
    case BoldduotoneWidgetAdd = 'solar-bold-duotone-WidgetAdd';
    case BoldduotoneTuningSquare = 'solar-bold-duotone-TuningSquare';
    case BoldduotoneSettingsMinimalistic = 'solar-bold-duotone-SettingsMinimalistic';
    case BoldduotoneWidget6 = 'solar-bold-duotone-Widget6';
    case BoldduotoneWidget4 = 'solar-bold-duotone-Widget4';
    case BoldduotoneSettings = 'solar-bold-duotone-Settings';
    case BoldduotoneWidget5 = 'solar-bold-duotone-Widget5';
    case BoldduotoneWidget2 = 'solar-bold-duotone-Widget2';
    case BoldduotoneWidget3 = 'solar-bold-duotone-Widget3';
    case BoldduotoneTuning2 = 'solar-bold-duotone-Tuning2';
    case BoldduotoneTuning3 = 'solar-bold-duotone-Tuning3';
    case BoldduotoneWidget = 'solar-bold-duotone-Widget';
    case BoldduotoneTuning4 = 'solar-bold-duotone-Tuning4';
    case BoldduotoneTuning = 'solar-bold-duotone-Tuning';
    case BoldduotoneDiagramDown = 'solar-bold-duotone-DiagramDown';
    case BoldduotoneChart2 = 'solar-bold-duotone-Chart2';
    case BoldduotoneChart = 'solar-bold-duotone-Chart';
    case BoldduotoneDiagramUp = 'solar-bold-duotone-DiagramUp';
    case BoldduotoneGraphNew = 'solar-bold-duotone-GraphNew';
    case BoldduotoneCourseUp = 'solar-bold-duotone-CourseUp';
    case BoldduotoneGraphDownNew = 'solar-bold-duotone-GraphDownNew';
    case BoldduotonePieChart3 = 'solar-bold-duotone-PieChart3';
    case BoldduotonePieChart2 = 'solar-bold-duotone-PieChart2';
    case BoldduotoneGraphNewUp = 'solar-bold-duotone-GraphNewUp';
    case BoldduotonePieChart = 'solar-bold-duotone-PieChart';
    case BoldduotoneRoundGraph = 'solar-bold-duotone-RoundGraph';
    case BoldduotoneGraphUp = 'solar-bold-duotone-GraphUp';
    case BoldduotoneChartSquare = 'solar-bold-duotone-ChartSquare';
    case BoldduotoneCourseDown = 'solar-bold-duotone-CourseDown';
    case BoldduotoneChatSquare2 = 'solar-bold-duotone-ChatSquare2';
    case BoldduotoneGraphDown = 'solar-bold-duotone-GraphDown';
    case BoldduotoneGraph = 'solar-bold-duotone-Graph';
    case BoldduotonePresentationGraph = 'solar-bold-duotone-PresentationGraph';
    case BoldduotoneMaximizeSquare3 = 'solar-bold-duotone-MaximizeSquare3';
    case BoldduotoneMaximizeSquareMinimalistic = 'solar-bold-duotone-MaximizeSquareMinimalistic';
    case BoldduotoneMaximizeSquare2 = 'solar-bold-duotone-MaximizeSquare2';
    case BoldduotoneMinimizeSquare = 'solar-bold-duotone-MinimizeSquare';
    case BoldduotoneDownloadSquare = 'solar-bold-duotone-DownloadSquare';
    case BoldduotoneUndoLeftRoundSquare = 'solar-bold-duotone-UndoLeftRoundSquare';
    case BoldduotoneReply = 'solar-bold-duotone-Reply';
    case BoldduotoneLogout = 'solar-bold-duotone-Logout';
    case BoldduotoneReciveSquare = 'solar-bold-duotone-ReciveSquare';
    case BoldduotoneExport = 'solar-bold-duotone-Export';
    case BoldduotoneSendTwiceSquare = 'solar-bold-duotone-SendTwiceSquare';
    case BoldduotoneUndoLeftRound = 'solar-bold-duotone-UndoLeftRound';
    case BoldduotoneForward2 = 'solar-bold-duotone-Forward2';
    case BoldduotoneMaximize = 'solar-bold-duotone-Maximize';
    case BoldduotoneUndoRightRound = 'solar-bold-duotone-UndoRightRound';
    case BoldduotoneMinimizeSquare2 = 'solar-bold-duotone-MinimizeSquare2';
    case BoldduotoneMinimizeSquare3 = 'solar-bold-duotone-MinimizeSquare3';
    case BoldduotoneUploadTwiceSquare = 'solar-bold-duotone-UploadTwiceSquare';
    case BoldduotoneMinimize = 'solar-bold-duotone-Minimize';
    case BoldduotoneCircleTopUp = 'solar-bold-duotone-CircleTopUp';
    case BoldduotoneUploadMinimalistic = 'solar-bold-duotone-UploadMinimalistic';
    case BoldduotoneDownload = 'solar-bold-duotone-Download';
    case BoldduotoneImport = 'solar-bold-duotone-Import';
    case BoldduotoneLogin = 'solar-bold-duotone-Login';
    case BoldduotoneUndoLeft = 'solar-bold-duotone-UndoLeft';
    case BoldduotoneSquareTopUp = 'solar-bold-duotone-SquareTopUp';
    case BoldduotoneDownloadTwiceSquare = 'solar-bold-duotone-DownloadTwiceSquare';
    case BoldduotoneCircleBottomDown = 'solar-bold-duotone-CircleBottomDown';
    case BoldduotoneMaximizeSquare = 'solar-bold-duotone-MaximizeSquare';
    case BoldduotoneUploadSquare = 'solar-bold-duotone-UploadSquare';
    case BoldduotoneUndoRightSquare = 'solar-bold-duotone-UndoRightSquare';
    case BoldduotoneReciveTwiceSquare = 'solar-bold-duotone-ReciveTwiceSquare';
    case BoldduotoneCircleTopDown = 'solar-bold-duotone-CircleTopDown';
    case BoldduotoneArrowToDownLeft = 'solar-bold-duotone-ArrowToDownLeft';
    case BoldduotoneLogout2 = 'solar-bold-duotone-Logout2';
    case BoldduotoneLogout3 = 'solar-bold-duotone-Logout3';
    case BoldduotoneScale = 'solar-bold-duotone-Scale';
    case BoldduotoneArrowToDownRight = 'solar-bold-duotone-ArrowToDownRight';
    case BoldduotoneDownloadMinimalistic = 'solar-bold-duotone-DownloadMinimalistic';
    case BoldduotoneMinimizeSquareMinimalistic = 'solar-bold-duotone-MinimizeSquareMinimalistic';
    case BoldduotoneReply2 = 'solar-bold-duotone-Reply2';
    case BoldduotoneSquareBottomUp = 'solar-bold-duotone-SquareBottomUp';
    case BoldduotoneUndoRight = 'solar-bold-duotone-UndoRight';
    case BoldduotoneUndoLeftSquare = 'solar-bold-duotone-UndoLeftSquare';
    case BoldduotoneSendSquare = 'solar-bold-duotone-SendSquare';
    case BoldduotoneExit = 'solar-bold-duotone-Exit';
    case BoldduotoneSquareBottomDown = 'solar-bold-duotone-SquareBottomDown';
    case BoldduotoneUndoRightRoundSquare = 'solar-bold-duotone-UndoRightRoundSquare';
    case BoldduotoneArrowToTopLeft = 'solar-bold-duotone-ArrowToTopLeft';
    case BoldduotoneCircleBottomUp = 'solar-bold-duotone-CircleBottomUp';
    case BoldduotoneScreenShare = 'solar-bold-duotone-ScreenShare';
    case BoldduotoneUpload = 'solar-bold-duotone-Upload';
    case BoldduotoneSquareTopDown = 'solar-bold-duotone-SquareTopDown';
    case BoldduotoneArrowToTopRight = 'solar-bold-duotone-ArrowToTopRight';
    case BoldduotoneLogin3 = 'solar-bold-duotone-Login3';
    case BoldduotoneLogin2 = 'solar-bold-duotone-Login2';
    case BoldduotonePassport = 'solar-bold-duotone-Passport';
    case BoldduotoneDiplomaVerified = 'solar-bold-duotone-DiplomaVerified';
    case BoldduotoneCaseRound = 'solar-bold-duotone-CaseRound';
    case BoldduotoneBackpack = 'solar-bold-duotone-Backpack';
    case BoldduotoneBook2 = 'solar-bold-duotone-Book2';
    case BoldduotoneSquareAcademicCap2 = 'solar-bold-duotone-SquareAcademicCap2';
    case BoldduotoneCaseRoundMinimalistic = 'solar-bold-duotone-CaseRoundMinimalistic';
    case BoldduotoneCase = 'solar-bold-duotone-Case';
    case BoldduotoneBookBookmarkMinimalistic = 'solar-bold-duotone-BookBookmarkMinimalistic';
    case BoldduotoneBookmarkOpened = 'solar-bold-duotone-BookmarkOpened';
    case BoldduotoneDiploma = 'solar-bold-duotone-Diploma';
    case BoldduotoneBook = 'solar-bold-duotone-Book';
    case BoldduotoneSquareAcademicCap = 'solar-bold-duotone-SquareAcademicCap';
    case BoldduotoneBookmarkCircle = 'solar-bold-duotone-BookmarkCircle';
    case BoldduotoneCalculatorMinimalistic = 'solar-bold-duotone-CalculatorMinimalistic';
    case BoldduotoneNotebookSquare = 'solar-bold-duotone-NotebookSquare';
    case BoldduotoneBookMinimalistic = 'solar-bold-duotone-BookMinimalistic';
    case BoldduotoneCaseMinimalistic = 'solar-bold-duotone-CaseMinimalistic';
    case BoldduotoneNotebookBookmark = 'solar-bold-duotone-NotebookBookmark';
    case BoldduotonePassportMinimalistic = 'solar-bold-duotone-PassportMinimalistic';
    case BoldduotoneBookBookmark = 'solar-bold-duotone-BookBookmark';
    case BoldduotoneBookmarkSquareMinimalistic = 'solar-bold-duotone-BookmarkSquareMinimalistic';
    case BoldduotoneBookmark = 'solar-bold-duotone-Bookmark';
    case BoldduotonePlusMinus = 'solar-bold-duotone-PlusMinus';
    case BoldduotoneCalculator = 'solar-bold-duotone-Calculator';
    case BoldduotoneBookmarkSquare = 'solar-bold-duotone-BookmarkSquare';
    case BoldduotoneNotebookMinimalistic = 'solar-bold-duotone-NotebookMinimalistic';
    case BoldduotoneFireSquare = 'solar-bold-duotone-FireSquare';
    case BoldduotoneSuitcaseLines = 'solar-bold-duotone-SuitcaseLines';
    case BoldduotoneFire = 'solar-bold-duotone-Fire';
    case BoldduotoneBonfire = 'solar-bold-duotone-Bonfire';
    case BoldduotoneSuitcaseTag = 'solar-bold-duotone-SuitcaseTag';
    case BoldduotoneLeaf = 'solar-bold-duotone-Leaf';
    case BoldduotoneSuitcase = 'solar-bold-duotone-Suitcase';
    case BoldduotoneFlame = 'solar-bold-duotone-Flame';
    case BoldduotoneFireMinimalistic = 'solar-bold-duotone-FireMinimalistic';
    case BoldduotoneBellBing = 'solar-bold-duotone-BellBing';
    case BoldduotoneNotificationLinesRemove = 'solar-bold-duotone-NotificationLinesRemove';
    case BoldduotoneNotificationUnread = 'solar-bold-duotone-NotificationUnread';
    case BoldduotoneBell = 'solar-bold-duotone-Bell';
    case BoldduotoneNotificationRemove = 'solar-bold-duotone-NotificationRemove';
    case BoldduotoneNotificationUnreadLines = 'solar-bold-duotone-NotificationUnreadLines';
    case BoldduotoneBellOff = 'solar-bold-duotone-BellOff';
    case BoldduotoneLightning = 'solar-bold-duotone-Lightning';
    case BoldduotoneLightbulbMinimalistic = 'solar-bold-duotone-LightbulbMinimalistic';
    case BoldduotoneServerSquareCloud = 'solar-bold-duotone-ServerSquareCloud';
    case BoldduotoneLightbulbBolt = 'solar-bold-duotone-LightbulbBolt';
    case BoldduotoneAirbudsCharge = 'solar-bold-duotone-AirbudsCharge';
    case BoldduotoneServerPath = 'solar-bold-duotone-ServerPath';
    case BoldduotoneSimCardMinimalistic = 'solar-bold-duotone-SimCardMinimalistic';
    case BoldduotoneSmartphone = 'solar-bold-duotone-Smartphone';
    case BoldduotoneTurntable = 'solar-bold-duotone-Turntable';
    case BoldduotoneAirbudsCheck = 'solar-bold-duotone-AirbudsCheck';
    case BoldduotoneMouseMinimalistic = 'solar-bold-duotone-MouseMinimalistic';
    case BoldduotoneSmartphoneRotateAngle = 'solar-bold-duotone-SmartphoneRotateAngle';
    case BoldduotoneRadioMinimalistic = 'solar-bold-duotone-RadioMinimalistic';
    case BoldduotoneAirbuds = 'solar-bold-duotone-Airbuds';
    case BoldduotoneSmartphoneRotateOrientation = 'solar-bold-duotone-SmartphoneRotateOrientation';
    case BoldduotoneIPhone = 'solar-bold-duotone-IPhone';
    case BoldduotoneSimCard = 'solar-bold-duotone-SimCard';
    case BoldduotoneFlashDrive = 'solar-bold-duotone-FlashDrive';
    case BoldduotoneDevices = 'solar-bold-duotone-Devices';
    case BoldduotoneSimCards = 'solar-bold-duotone-SimCards';
    case BoldduotoneAirbudsCaseOpen = 'solar-bold-duotone-AirbudsCaseOpen';
    case BoldduotoneTurntableMusicNote = 'solar-bold-duotone-TurntableMusicNote';
    case BoldduotoneKeyboard = 'solar-bold-duotone-Keyboard';
    case BoldduotoneGamepadCharge = 'solar-bold-duotone-GamepadCharge';
    case BoldduotoneBoombox = 'solar-bold-duotone-Boombox';
    case BoldduotoneSmartSpeakerMinimalistic = 'solar-bold-duotone-SmartSpeakerMinimalistic';
    case BoldduotoneTelescope = 'solar-bold-duotone-Telescope';
    case BoldduotoneMonitorCamera = 'solar-bold-duotone-MonitorCamera';
    case BoldduotoneLaptopMinimalistic = 'solar-bold-duotone-LaptopMinimalistic';
    case BoldduotoneServer2 = 'solar-bold-duotone-Server2';
    case BoldduotoneSmartSpeaker = 'solar-bold-duotone-SmartSpeaker';
    case BoldduotoneProjector = 'solar-bold-duotone-Projector';
    case BoldduotoneServer = 'solar-bold-duotone-Server';
    case BoldduotoneTV = 'solar-bold-duotone-TV';
    case BoldduotoneCassette2 = 'solar-bold-duotone-Cassette2';
    case BoldduotoneRadio = 'solar-bold-duotone-Radio';
    case BoldduotoneSmartphoneVibration = 'solar-bold-duotone-SmartphoneVibration';
    case BoldduotoneAirbudsLeft = 'solar-bold-duotone-AirbudsLeft';
    case BoldduotoneHeadphonesRound = 'solar-bold-duotone-HeadphonesRound';
    case BoldduotoneGameboy = 'solar-bold-duotone-Gameboy';
    case BoldduotoneHeadphonesRoundSound = 'solar-bold-duotone-HeadphonesRoundSound';
    case BoldduotoneCPU = 'solar-bold-duotone-CPU';
    case BoldduotonePrinter2 = 'solar-bold-duotone-Printer2';
    case BoldduotoneHeadphonesSquare = 'solar-bold-duotone-HeadphonesSquare';
    case BoldduotoneServerSquareUpdate = 'solar-bold-duotone-ServerSquareUpdate';
    case BoldduotonePrinterMinimalistic = 'solar-bold-duotone-PrinterMinimalistic';
    case BoldduotoneBluetooth = 'solar-bold-duotone-Bluetooth';
    case BoldduotoneWirelessCharge = 'solar-bold-duotone-WirelessCharge';
    case BoldduotoneBluetoothCircle = 'solar-bold-duotone-BluetoothCircle';
    case BoldduotoneAirbudsCaseMinimalistic = 'solar-bold-duotone-AirbudsCaseMinimalistic';
    case BoldduotoneLightbulb = 'solar-bold-duotone-Lightbulb';
    case BoldduotoneAirbudsRemove = 'solar-bold-duotone-AirbudsRemove';
    case BoldduotoneSmartphoneRotate2 = 'solar-bold-duotone-SmartphoneRotate2';
    case BoldduotoneSsdSquare = 'solar-bold-duotone-SsdSquare';
    case BoldduotonePrinter = 'solar-bold-duotone-Printer';
    case BoldduotoneSmartphone2 = 'solar-bold-duotone-Smartphone2';
    case BoldduotoneServerMinimalistic = 'solar-bold-duotone-ServerMinimalistic';
    case BoldduotoneHeadphonesSquareSound = 'solar-bold-duotone-HeadphonesSquareSound';
    case BoldduotoneDiskette = 'solar-bold-duotone-Diskette';
    case BoldduotoneBluetoothWave = 'solar-bold-duotone-BluetoothWave';
    case BoldduotoneSmartSpeaker2 = 'solar-bold-duotone-SmartSpeaker2';
    case BoldduotoneLaptop3 = 'solar-bold-duotone-Laptop3';
    case BoldduotoneLaptop2 = 'solar-bold-duotone-Laptop2';
    case BoldduotoneMouseCircle = 'solar-bold-duotone-MouseCircle';
    case BoldduotoneTurntableMinimalistic = 'solar-bold-duotone-TurntableMinimalistic';
    case BoldduotoneSmartphoneUpdate = 'solar-bold-duotone-SmartphoneUpdate';
    case BoldduotoneGamepadMinimalistic = 'solar-bold-duotone-GamepadMinimalistic';
    case BoldduotoneSdCard = 'solar-bold-duotone-SdCard';
    case BoldduotonePlugCircle = 'solar-bold-duotone-PlugCircle';
    case BoldduotoneAirbudsCase = 'solar-bold-duotone-AirbudsCase';
    case BoldduotoneSsdRound = 'solar-bold-duotone-SsdRound';
    case BoldduotoneLaptop = 'solar-bold-duotone-Laptop';
    case BoldduotoneAirbudsRight = 'solar-bold-duotone-AirbudsRight';
    case BoldduotoneDisplay = 'solar-bold-duotone-Display';
    case BoldduotoneMonitorSmartphone = 'solar-bold-duotone-MonitorSmartphone';
    case BoldduotoneSocket = 'solar-bold-duotone-Socket';
    case BoldduotoneGamepadOld = 'solar-bold-duotone-GamepadOld';
    case BoldduotoneCpuBolt = 'solar-bold-duotone-CpuBolt';
    case BoldduotoneAirbudsCaseCharge = 'solar-bold-duotone-AirbudsCaseCharge';
    case BoldduotoneTablet = 'solar-bold-duotone-Tablet';
    case BoldduotoneWeigher = 'solar-bold-duotone-Weigher';
    case BoldduotoneServerSquare = 'solar-bold-duotone-ServerSquare';
    case BoldduotoneMouse = 'solar-bold-duotone-Mouse';
    case BoldduotoneGamepadNoCharge = 'solar-bold-duotone-GamepadNoCharge';
    case BoldduotoneBluetoothSquare = 'solar-bold-duotone-BluetoothSquare';
    case BoldduotoneCloudStorage = 'solar-bold-duotone-CloudStorage';
    case BoldduotoneGamepad = 'solar-bold-duotone-Gamepad';
    case BoldduotoneMonitor = 'solar-bold-duotone-Monitor';
    case BoldduotoneCassette = 'solar-bold-duotone-Cassette';
    // Line Duotone Style (1205 icons)
    case LineduotoneFacemaskCircle = 'solar-line-duotone-FacemaskCircle';
    case LineduotoneConfoundedCircle = 'solar-line-duotone-ConfoundedCircle';
    case LineduotoneSadSquare = 'solar-line-duotone-SadSquare';
    case LineduotoneSleepingCircle = 'solar-line-duotone-SleepingCircle';
    case LineduotoneFaceScanCircle = 'solar-line-duotone-FaceScanCircle';
    case LineduotoneSmileCircle = 'solar-line-duotone-SmileCircle';
    case LineduotoneStickerSmileCircle = 'solar-line-duotone-StickerSmileCircle';
    case LineduotoneStickerSquare = 'solar-line-duotone-StickerSquare';
    case LineduotoneEmojiFunnyCircle = 'solar-line-duotone-EmojiFunnyCircle';
    case LineduotoneExpressionlessSquare = 'solar-line-duotone-ExpressionlessSquare';
    case LineduotoneSleepingSquare = 'solar-line-duotone-SleepingSquare';
    case LineduotoneSadCircle = 'solar-line-duotone-SadCircle';
    case LineduotoneFacemaskSquare = 'solar-line-duotone-FacemaskSquare';
    case LineduotoneConfoundedSquare = 'solar-line-duotone-ConfoundedSquare';
    case LineduotoneFaceScanSquare = 'solar-line-duotone-FaceScanSquare';
    case LineduotoneSmileSquare = 'solar-line-duotone-SmileSquare';
    case LineduotoneStickerSmileCircle2 = 'solar-line-duotone-StickerSmileCircle2';
    case LineduotoneStickerSmileSquare = 'solar-line-duotone-StickerSmileSquare';
    case LineduotoneEmojiFunnySquare = 'solar-line-duotone-EmojiFunnySquare';
    case LineduotoneStickerCircle = 'solar-line-duotone-StickerCircle';
    case LineduotoneExpressionlessCircle = 'solar-line-duotone-ExpressionlessCircle';
    case LineduotoneLike = 'solar-line-duotone-Like';
    case LineduotoneMedalStarSquare = 'solar-line-duotone-MedalStarSquare';
    case LineduotoneDislike = 'solar-line-duotone-Dislike';
    case LineduotoneStarShine = 'solar-line-duotone-StarShine';
    case LineduotoneHeartAngle = 'solar-line-duotone-HeartAngle';
    case LineduotoneMedalRibbon = 'solar-line-duotone-MedalRibbon';
    case LineduotoneHeartShine = 'solar-line-duotone-HeartShine';
    case LineduotoneMedalStarCircle = 'solar-line-duotone-MedalStarCircle';
    case LineduotoneMedalRibbonsStar = 'solar-line-duotone-MedalRibbonsStar';
    case LineduotoneStar = 'solar-line-duotone-Star';
    case LineduotoneHeartUnlock = 'solar-line-duotone-HeartUnlock';
    case LineduotoneMedalRibbonStar = 'solar-line-duotone-MedalRibbonStar';
    case LineduotoneHeartLock = 'solar-line-duotone-HeartLock';
    case LineduotoneHeartBroken = 'solar-line-duotone-HeartBroken';
    case LineduotoneHearts = 'solar-line-duotone-Hearts';
    case LineduotoneMedalStar = 'solar-line-duotone-MedalStar';
    case LineduotoneHeart = 'solar-line-duotone-Heart';
    case LineduotoneCloset = 'solar-line-duotone-Closet';
    case LineduotoneBed = 'solar-line-duotone-Bed';
    case LineduotoneWashingMachine = 'solar-line-duotone-WashingMachine';
    case LineduotoneBedsideTable = 'solar-line-duotone-BedsideTable';
    case LineduotoneSofa3 = 'solar-line-duotone-Sofa3';
    case LineduotoneSofa2 = 'solar-line-duotone-Sofa2';
    case LineduotoneChair2 = 'solar-line-duotone-Chair2';
    case LineduotoneBath = 'solar-line-duotone-Bath';
    case LineduotoneSmartVacuumCleaner2 = 'solar-line-duotone-SmartVacuumCleaner2';
    case LineduotoneCondicioner = 'solar-line-duotone-Condicioner';
    case LineduotoneSmartVacuumCleaner = 'solar-line-duotone-SmartVacuumCleaner';
    case LineduotoneRemoteController2 = 'solar-line-duotone-RemoteController2';
    case LineduotoneFloorLampMinimalistic = 'solar-line-duotone-FloorLampMinimalistic';
    case LineduotoneLamp = 'solar-line-duotone-Lamp';
    case LineduotoneBarChair = 'solar-line-duotone-BarChair';
    case LineduotoneBedsideTable2 = 'solar-line-duotone-BedsideTable2';
    case LineduotoneCloset2 = 'solar-line-duotone-Closet2';
    case LineduotoneBedsideTable3 = 'solar-line-duotone-BedsideTable3';
    case LineduotoneSpeaker = 'solar-line-duotone-Speaker';
    case LineduotoneVolumeKnob = 'solar-line-duotone-VolumeKnob';
    case LineduotoneArmchair = 'solar-line-duotone-Armchair';
    case LineduotoneSpeakerMinimalistic = 'solar-line-duotone-SpeakerMinimalistic';
    case LineduotoneRemoteController = 'solar-line-duotone-RemoteController';
    case LineduotoneTrellis = 'solar-line-duotone-Trellis';
    case LineduotoneFloorLamp = 'solar-line-duotone-FloorLamp';
    case LineduotoneCondicioner2 = 'solar-line-duotone-Condicioner2';
    case LineduotoneBedsideTable4 = 'solar-line-duotone-BedsideTable4';
    case LineduotoneArmchair2 = 'solar-line-duotone-Armchair2';
    case LineduotoneWashingMachineMinimalistic = 'solar-line-duotone-WashingMachineMinimalistic';
    case LineduotoneChair = 'solar-line-duotone-Chair';
    case LineduotoneRemoteControllerMinimalistic = 'solar-line-duotone-RemoteControllerMinimalistic';
    case LineduotoneChandelier = 'solar-line-duotone-Chandelier';
    case LineduotoneFridge = 'solar-line-duotone-Fridge';
    case LineduotoneMirror = 'solar-line-duotone-Mirror';
    case LineduotoneSofa = 'solar-line-duotone-Sofa';
    case LineduotoneEarth = 'solar-line-duotone-Earth';
    case LineduotoneStarsLine = 'solar-line-duotone-StarsLine';
    case LineduotoneStarFall2 = 'solar-line-duotone-StarFall2';
    case LineduotoneStarFall = 'solar-line-duotone-StarFall';
    case LineduotoneBlackHole3 = 'solar-line-duotone-BlackHole3';
    case LineduotoneWomen = 'solar-line-duotone-Women';
    case LineduotoneBlackHole = 'solar-line-duotone-BlackHole';
    case LineduotoneStarRings = 'solar-line-duotone-StarRings';
    case LineduotoneBlackHole2 = 'solar-line-duotone-BlackHole2';
    case LineduotoneStarFallMinimalistic2 = 'solar-line-duotone-StarFallMinimalistic2';
    case LineduotonePlanet = 'solar-line-duotone-Planet';
    case LineduotoneSatellite = 'solar-line-duotone-Satellite';
    case LineduotoneMen = 'solar-line-duotone-Men';
    case LineduotoneRocket2 = 'solar-line-duotone-Rocket2';
    case LineduotoneStars = 'solar-line-duotone-Stars';
    case LineduotoneStarAngle = 'solar-line-duotone-StarAngle';
    case LineduotoneInfinity = 'solar-line-duotone-Infinity';
    case LineduotoneUfo2 = 'solar-line-duotone-Ufo2';
    case LineduotoneUfo3 = 'solar-line-duotone-Ufo3';
    case LineduotoneStarRing = 'solar-line-duotone-StarRing';
    case LineduotonePlanet2 = 'solar-line-duotone-Planet2';
    case LineduotonePlanet3 = 'solar-line-duotone-Planet3';
    case LineduotoneAsteroid = 'solar-line-duotone-Asteroid';
    case LineduotoneStarsMinimalistic = 'solar-line-duotone-StarsMinimalistic';
    case LineduotoneUFO = 'solar-line-duotone-UFO';
    case LineduotonePlanet4 = 'solar-line-duotone-Planet4';
    case LineduotoneRocket = 'solar-line-duotone-Rocket';
    case LineduotoneStarFallMinimalistic = 'solar-line-duotone-StarFallMinimalistic';
    case LineduotoneStarRainbow = 'solar-line-duotone-StarRainbow';
    case LineduotoneAtom = 'solar-line-duotone-Atom';
    case LineduotoneStarCircle = 'solar-line-duotone-StarCircle';
    case LineduotoneCompassBig = 'solar-line-duotone-CompassBig';
    case LineduotoneMapPointSchool = 'solar-line-duotone-MapPointSchool';
    case LineduotoneSignpost = 'solar-line-duotone-Signpost';
    case LineduotoneMapArrowDown = 'solar-line-duotone-MapArrowDown';
    case LineduotoneMap = 'solar-line-duotone-Map';
    case LineduotoneMapArrowUp = 'solar-line-duotone-MapArrowUp';
    case LineduotonePointOnMapPerspective = 'solar-line-duotone-PointOnMapPerspective';
    case LineduotoneRadar = 'solar-line-duotone-Radar';
    case LineduotoneStreets = 'solar-line-duotone-Streets';
    case LineduotoneMapPointWave = 'solar-line-duotone-MapPointWave';
    case LineduotonePeopleNearby = 'solar-line-duotone-PeopleNearby';
    case LineduotoneStreetsMapPoint = 'solar-line-duotone-StreetsMapPoint';
    case LineduotoneMapPointSearch = 'solar-line-duotone-MapPointSearch';
    case LineduotoneGPS = 'solar-line-duotone-GPS';
    case LineduotoneMapArrowSquare = 'solar-line-duotone-MapArrowSquare';
    case LineduotoneBranchingPathsDown = 'solar-line-duotone-BranchingPathsDown';
    case LineduotoneMapPointRotate = 'solar-line-duotone-MapPointRotate';
    case LineduotoneGlobal = 'solar-line-duotone-Global';
    case LineduotoneCompassSquare = 'solar-line-duotone-CompassSquare';
    case LineduotoneRouting3 = 'solar-line-duotone-Routing3';
    case LineduotoneRouting2 = 'solar-line-duotone-Routing2';
    case LineduotoneMapPointRemove = 'solar-line-duotone-MapPointRemove';
    case LineduotoneGlobus = 'solar-line-duotone-Globus';
    case LineduotoneSignpost2 = 'solar-line-duotone-Signpost2';
    case LineduotoneRadar2 = 'solar-line-duotone-Radar2';
    case LineduotoneStreetsNavigation = 'solar-line-duotone-StreetsNavigation';
    case LineduotoneMapPoint = 'solar-line-duotone-MapPoint';
    case LineduotoneMapPointHospital = 'solar-line-duotone-MapPointHospital';
    case LineduotoneCompass = 'solar-line-duotone-Compass';
    case LineduotoneMapPointAdd = 'solar-line-duotone-MapPointAdd';
    case LineduotoneBranchingPathsUp = 'solar-line-duotone-BranchingPathsUp';
    case LineduotoneMapPointFavourite = 'solar-line-duotone-MapPointFavourite';
    case LineduotoneRoute = 'solar-line-duotone-Route';
    case LineduotonePointOnMap = 'solar-line-duotone-PointOnMap';
    case LineduotoneMapArrowRight = 'solar-line-duotone-MapArrowRight';
    case LineduotoneRouting = 'solar-line-duotone-Routing';
    case LineduotoneMapArrowLeft = 'solar-line-duotone-MapArrowLeft';
    case LineduotoneIncognito = 'solar-line-duotone-Incognito';
    case LineduotoneLockPassword = 'solar-line-duotone-LockPassword';
    case LineduotoneShieldNetwork = 'solar-line-duotone-ShieldNetwork';
    case LineduotoneKeyMinimalisticSquare = 'solar-line-duotone-KeyMinimalisticSquare';
    case LineduotoneLockKeyholeUnlocked = 'solar-line-duotone-LockKeyholeUnlocked';
    case LineduotoneLock = 'solar-line-duotone-Lock';
    case LineduotoneShieldKeyhole = 'solar-line-duotone-ShieldKeyhole';
    case LineduotoneEyeClosed = 'solar-line-duotone-EyeClosed';
    case LineduotoneKey = 'solar-line-duotone-Key';
    case LineduotoneShieldMinus = 'solar-line-duotone-ShieldMinus';
    case LineduotoneShield = 'solar-line-duotone-Shield';
    case LineduotoneLockUnlocked = 'solar-line-duotone-LockUnlocked';
    case LineduotoneBombMinimalistic = 'solar-line-duotone-BombMinimalistic';
    case LineduotoneShieldStar = 'solar-line-duotone-ShieldStar';
    case LineduotoneBomb = 'solar-line-duotone-Bomb';
    case LineduotoneKeySquare = 'solar-line-duotone-KeySquare';
    case LineduotoneLockKeyholeMinimalisticUnlocked = 'solar-line-duotone-LockKeyholeMinimalisticUnlocked';
    case LineduotoneShieldCross = 'solar-line-duotone-ShieldCross';
    case LineduotoneObjectScan = 'solar-line-duotone-ObjectScan';
    case LineduotonePasswordMinimalisticInput = 'solar-line-duotone-PasswordMinimalisticInput';
    case LineduotoneLockPasswordUnlocked = 'solar-line-duotone-LockPasswordUnlocked';
    case LineduotoneSiren = 'solar-line-duotone-Siren';
    case LineduotoneShieldMinimalistic = 'solar-line-duotone-ShieldMinimalistic';
    case LineduotoneEyeScan = 'solar-line-duotone-EyeScan';
    case LineduotoneKeyMinimalisticSquare2 = 'solar-line-duotone-KeyMinimalisticSquare2';
    case LineduotoneScanner2 = 'solar-line-duotone-Scanner2';
    case LineduotoneKeyMinimalisticSquare3 = 'solar-line-duotone-KeyMinimalisticSquare3';
    case LineduotoneKeyMinimalistic2 = 'solar-line-duotone-KeyMinimalistic2';
    case LineduotoneCodeScan = 'solar-line-duotone-CodeScan';
    case LineduotoneShieldPlus = 'solar-line-duotone-ShieldPlus';
    case LineduotonePasswordMinimalistic = 'solar-line-duotone-PasswordMinimalistic';
    case LineduotoneEye = 'solar-line-duotone-Eye';
    case LineduotoneQrCode = 'solar-line-duotone-QrCode';
    case LineduotoneShieldCheck = 'solar-line-duotone-ShieldCheck';
    case LineduotoneKeyMinimalistic = 'solar-line-duotone-KeyMinimalistic';
    case LineduotoneLockKeyhole = 'solar-line-duotone-LockKeyhole';
    case LineduotoneShieldUser = 'solar-line-duotone-ShieldUser';
    case LineduotoneKeySquare2 = 'solar-line-duotone-KeySquare2';
    case LineduotoneBombEmoji = 'solar-line-duotone-BombEmoji';
    case LineduotoneScanner = 'solar-line-duotone-Scanner';
    case LineduotoneShieldUp = 'solar-line-duotone-ShieldUp';
    case LineduotoneSirenRounded = 'solar-line-duotone-SirenRounded';
    case LineduotoneLockKeyholeMinimalistic = 'solar-line-duotone-LockKeyholeMinimalistic';
    case LineduotonePassword = 'solar-line-duotone-Password';
    case LineduotoneShieldKeyholeMinimalistic = 'solar-line-duotone-ShieldKeyholeMinimalistic';
    case LineduotoneShieldWarning = 'solar-line-duotone-ShieldWarning';
    case LineduotonePallete2 = 'solar-line-duotone-Pallete2';
    case LineduotoneAlignVerticalSpacing = 'solar-line-duotone-AlignVerticalSpacing';
    case LineduotoneAlignVerticalCenter = 'solar-line-duotone-AlignVerticalCenter';
    case LineduotoneCropMinimalistic = 'solar-line-duotone-CropMinimalistic';
    case LineduotoneMirrorRight = 'solar-line-duotone-MirrorRight';
    case LineduotoneAlignBottom = 'solar-line-duotone-AlignBottom';
    case LineduotoneRadialBlur = 'solar-line-duotone-RadialBlur';
    case LineduotoneCrop = 'solar-line-duotone-Crop';
    case LineduotoneAlignHorizontaSpacing = 'solar-line-duotone-AlignHorizontaSpacing';
    case LineduotoneRulerPen = 'solar-line-duotone-RulerPen';
    case LineduotoneThreeSquares = 'solar-line-duotone-ThreeSquares';
    case LineduotonePaintRoller = 'solar-line-duotone-PaintRoller';
    case LineduotoneLayers = 'solar-line-duotone-Layers';
    case LineduotoneFilters = 'solar-line-duotone-Filters';
    case LineduotoneRulerCrossPen = 'solar-line-duotone-RulerCrossPen';
    case LineduotoneFlipHorizontal = 'solar-line-duotone-FlipHorizontal';
    case LineduotoneAlignLeft = 'solar-line-duotone-AlignLeft';
    case LineduotoneRuler = 'solar-line-duotone-Ruler';
    case LineduotonePalette = 'solar-line-duotone-Palette';
    case LineduotoneAlignTop = 'solar-line-duotone-AlignTop';
    case LineduotoneAlignHorizontalCenter = 'solar-line-duotone-AlignHorizontalCenter';
    case LineduotoneAlignRight = 'solar-line-duotone-AlignRight';
    case LineduotoneRulerAngular = 'solar-line-duotone-RulerAngular';
    case LineduotonePipette = 'solar-line-duotone-Pipette';
    case LineduotoneFlipVertical = 'solar-line-duotone-FlipVertical';
    case LineduotoneMirrorLeft = 'solar-line-duotone-MirrorLeft';
    case LineduotoneLayersMinimalistic = 'solar-line-duotone-LayersMinimalistic';
    case LineduotoneColourTuneing = 'solar-line-duotone-ColourTuneing';
    case LineduotonePaletteRound = 'solar-line-duotone-PaletteRound';
    case LineduotoneEraser = 'solar-line-duotone-Eraser';
    case LineduotoneTextItalicCircle = 'solar-line-duotone-TextItalicCircle';
    case LineduotoneLinkRound = 'solar-line-duotone-LinkRound';
    case LineduotoneTextItalic = 'solar-line-duotone-TextItalic';
    case LineduotoneLinkBrokenMinimalistic = 'solar-line-duotone-LinkBrokenMinimalistic';
    case LineduotoneTextUnderlineCross = 'solar-line-duotone-TextUnderlineCross';
    case LineduotoneLink = 'solar-line-duotone-Link';
    case LineduotoneEraserCircle = 'solar-line-duotone-EraserCircle';
    case LineduotoneLinkCircle = 'solar-line-duotone-LinkCircle';
    case LineduotoneTextBoldCircle = 'solar-line-duotone-TextBoldCircle';
    case LineduotoneTextField = 'solar-line-duotone-TextField';
    case LineduotoneTextSquare = 'solar-line-duotone-TextSquare';
    case LineduotoneTextSquare2 = 'solar-line-duotone-TextSquare2';
    case LineduotoneLinkRoundAngle = 'solar-line-duotone-LinkRoundAngle';
    case LineduotoneTextUnderlineCircle = 'solar-line-duotone-TextUnderlineCircle';
    case LineduotoneTextCrossCircle = 'solar-line-duotone-TextCrossCircle';
    case LineduotoneTextItalicSquare = 'solar-line-duotone-TextItalicSquare';
    case LineduotoneParagraphSpacing = 'solar-line-duotone-ParagraphSpacing';
    case LineduotoneText = 'solar-line-duotone-Text';
    case LineduotoneLinkBroken = 'solar-line-duotone-LinkBroken';
    case LineduotoneTextCross = 'solar-line-duotone-TextCross';
    case LineduotoneTextUnderline = 'solar-line-duotone-TextUnderline';
    case LineduotoneLinkMinimalistic = 'solar-line-duotone-LinkMinimalistic';
    case LineduotoneLinkMinimalistic2 = 'solar-line-duotone-LinkMinimalistic2';
    case LineduotoneTextBold = 'solar-line-duotone-TextBold';
    case LineduotoneTextSelection = 'solar-line-duotone-TextSelection';
    case LineduotoneTextFieldFocus = 'solar-line-duotone-TextFieldFocus';
    case LineduotoneTextBoldSquare = 'solar-line-duotone-TextBoldSquare';
    case LineduotoneEraserSquare = 'solar-line-duotone-EraserSquare';
    case LineduotoneLinkSquare = 'solar-line-duotone-LinkSquare';
    case LineduotoneTextCircle = 'solar-line-duotone-TextCircle';
    case LineduotoneBackspace = 'solar-line-duotone-Backspace';
    case LineduotoneTextCrossSquare = 'solar-line-duotone-TextCrossSquare';
    case LineduotoneInboxUnread = 'solar-line-duotone-InboxUnread';
    case LineduotoneChatUnread = 'solar-line-duotone-ChatUnread';
    case LineduotoneChatRound = 'solar-line-duotone-ChatRound';
    case LineduotoneUnread = 'solar-line-duotone-Unread';
    case LineduotoneMailbox = 'solar-line-duotone-Mailbox';
    case LineduotoneLetter = 'solar-line-duotone-Letter';
    case LineduotonePenNewRound = 'solar-line-duotone-PenNewRound';
    case LineduotoneMultipleForwardRight = 'solar-line-duotone-MultipleForwardRight';
    case LineduotoneMultipleForwardLeft = 'solar-line-duotone-MultipleForwardLeft';
    case LineduotoneInboxArchive = 'solar-line-duotone-InboxArchive';
    case LineduotoneInbox = 'solar-line-duotone-Inbox';
    case LineduotonePen2 = 'solar-line-duotone-Pen2';
    case LineduotonePenNewSquare = 'solar-line-duotone-PenNewSquare';
    case LineduotonePen = 'solar-line-duotone-Pen';
    case LineduotoneChatDots = 'solar-line-duotone-ChatDots';
    case LineduotoneChatSquareCall = 'solar-line-duotone-ChatSquareCall';
    case LineduotoneSquareShareLine = 'solar-line-duotone-SquareShareLine';
    case LineduotoneChatRoundCheck = 'solar-line-duotone-ChatRoundCheck';
    case LineduotoneInboxOut = 'solar-line-duotone-InboxOut';
    case LineduotonePlain3 = 'solar-line-duotone-Plain3';
    case LineduotoneChatRoundDots = 'solar-line-duotone-ChatRoundDots';
    case LineduotoneChatRoundLike = 'solar-line-duotone-ChatRoundLike';
    case LineduotonePlain2 = 'solar-line-duotone-Plain2';
    case LineduotoneChatRoundUnread = 'solar-line-duotone-ChatRoundUnread';
    case LineduotoneChatSquareLike = 'solar-line-duotone-ChatSquareLike';
    case LineduotonePaperclip = 'solar-line-duotone-Paperclip';
    case LineduotoneChatSquareCheck = 'solar-line-duotone-ChatSquareCheck';
    case LineduotoneChatSquare = 'solar-line-duotone-ChatSquare';
    case LineduotoneLetterOpened = 'solar-line-duotone-LetterOpened';
    case LineduotoneSquareForward = 'solar-line-duotone-SquareForward';
    case LineduotoneLetterUnread = 'solar-line-duotone-LetterUnread';
    case LineduotonePaperclipRounded2 = 'solar-line-duotone-PaperclipRounded2';
    case LineduotoneChatRoundCall = 'solar-line-duotone-ChatRoundCall';
    case LineduotoneInboxLine = 'solar-line-duotone-InboxLine';
    case LineduotoneChatRoundVideo = 'solar-line-duotone-ChatRoundVideo';
    case LineduotoneChatRoundMoney = 'solar-line-duotone-ChatRoundMoney';
    case LineduotoneInboxIn = 'solar-line-duotone-InboxIn';
    case LineduotoneCheckRead = 'solar-line-duotone-CheckRead';
    case LineduotoneChatRoundLine = 'solar-line-duotone-ChatRoundLine';
    case LineduotoneForward = 'solar-line-duotone-Forward';
    case LineduotonePaperclip2 = 'solar-line-duotone-Paperclip2';
    case LineduotoneDialog2 = 'solar-line-duotone-Dialog2';
    case LineduotoneDialog = 'solar-line-duotone-Dialog';
    case LineduotonePaperclipRounded = 'solar-line-duotone-PaperclipRounded';
    case LineduotonePlain = 'solar-line-duotone-Plain';
    case LineduotoneChatSquareArrow = 'solar-line-duotone-ChatSquareArrow';
    case LineduotoneChatSquareCode = 'solar-line-duotone-ChatSquareCode';
    case LineduotoneChatLine = 'solar-line-duotone-ChatLine';
    case LineduotoneTennis = 'solar-line-duotone-Tennis';
    case LineduotoneBicyclingRound = 'solar-line-duotone-BicyclingRound';
    case LineduotoneBalls = 'solar-line-duotone-Balls';
    case LineduotoneMeditationRound = 'solar-line-duotone-MeditationRound';
    case LineduotoneStretchingRound = 'solar-line-duotone-StretchingRound';
    case LineduotoneDumbbells2 = 'solar-line-duotone-Dumbbells2';
    case LineduotoneMeditation = 'solar-line-duotone-Meditation';
    case LineduotoneRunning2 = 'solar-line-duotone-Running2';
    case LineduotoneRugby = 'solar-line-duotone-Rugby';
    case LineduotoneBodyShapeMinimalistic = 'solar-line-duotone-BodyShapeMinimalistic';
    case LineduotoneStretching = 'solar-line-duotone-Stretching';
    case LineduotoneBowling = 'solar-line-duotone-Bowling';
    case LineduotoneRanking = 'solar-line-duotone-Ranking';
    case LineduotoneTreadmillRound = 'solar-line-duotone-TreadmillRound';
    case LineduotoneVolleyball = 'solar-line-duotone-Volleyball';
    case LineduotoneDumbbellLargeMinimalistic = 'solar-line-duotone-DumbbellLargeMinimalistic';
    case LineduotoneRunningRound = 'solar-line-duotone-RunningRound';
    case LineduotoneHiking = 'solar-line-duotone-Hiking';
    case LineduotoneHikingMinimalistic = 'solar-line-duotone-HikingMinimalistic';
    case LineduotoneWaterSun = 'solar-line-duotone-WaterSun';
    case LineduotoneGolf = 'solar-line-duotone-Golf';
    case LineduotoneSkateboarding = 'solar-line-duotone-Skateboarding';
    case LineduotoneDumbbells = 'solar-line-duotone-Dumbbells';
    case LineduotoneWalkingRound = 'solar-line-duotone-WalkingRound';
    case LineduotoneRunning = 'solar-line-duotone-Running';
    case LineduotoneTreadmill = 'solar-line-duotone-Treadmill';
    case LineduotoneSkateboard = 'solar-line-duotone-Skateboard';
    case LineduotoneDumbbellSmall = 'solar-line-duotone-DumbbellSmall';
    case LineduotoneBasketball = 'solar-line-duotone-Basketball';
    case LineduotoneFootball = 'solar-line-duotone-Football';
    case LineduotoneDumbbell = 'solar-line-duotone-Dumbbell';
    case LineduotoneBodyShape = 'solar-line-duotone-BodyShape';
    case LineduotoneWater = 'solar-line-duotone-Water';
    case LineduotoneSkateboardingRound = 'solar-line-duotone-SkateboardingRound';
    case LineduotoneHikingRound = 'solar-line-duotone-HikingRound';
    case LineduotoneVolleyball2 = 'solar-line-duotone-Volleyball2';
    case LineduotoneTennis2 = 'solar-line-duotone-Tennis2';
    case LineduotoneSwimming = 'solar-line-duotone-Swimming';
    case LineduotoneBicycling = 'solar-line-duotone-Bicycling';
    case LineduotoneWalking = 'solar-line-duotone-Walking';
    case LineduotoneDumbbellLarge = 'solar-line-duotone-DumbbellLarge';
    case LineduotoneCalendarMark = 'solar-line-duotone-CalendarMark';
    case LineduotoneHistory2 = 'solar-line-duotone-History2';
    case LineduotoneWatchSquareMinimalisticCharge = 'solar-line-duotone-WatchSquareMinimalisticCharge';
    case LineduotoneHistory3 = 'solar-line-duotone-History3';
    case LineduotoneHourglass = 'solar-line-duotone-Hourglass';
    case LineduotoneCalendarSearch = 'solar-line-duotone-CalendarSearch';
    case LineduotoneStopwatchPlay = 'solar-line-duotone-StopwatchPlay';
    case LineduotoneWatchRound = 'solar-line-duotone-WatchRound';
    case LineduotoneCalendarAdd = 'solar-line-duotone-CalendarAdd';
    case LineduotoneCalendarDate = 'solar-line-duotone-CalendarDate';
    case LineduotoneStopwatch = 'solar-line-duotone-Stopwatch';
    case LineduotoneAlarmPause = 'solar-line-duotone-AlarmPause';
    case LineduotoneAlarmTurnOff = 'solar-line-duotone-AlarmTurnOff';
    case LineduotoneClockSquare = 'solar-line-duotone-ClockSquare';
    case LineduotoneStopwatchPause = 'solar-line-duotone-StopwatchPause';
    case LineduotoneCalendarMinimalistic = 'solar-line-duotone-CalendarMinimalistic';
    case LineduotoneAlarmAdd = 'solar-line-duotone-AlarmAdd';
    case LineduotoneAlarmPlay = 'solar-line-duotone-AlarmPlay';
    case LineduotoneHourglassLine = 'solar-line-duotone-HourglassLine';
    case LineduotoneAlarmSleep = 'solar-line-duotone-AlarmSleep';
    case LineduotoneAlarmRemove = 'solar-line-duotone-AlarmRemove';
    case LineduotoneCalendar = 'solar-line-duotone-Calendar';
    case LineduotoneClockCircle = 'solar-line-duotone-ClockCircle';
    case LineduotoneHistory = 'solar-line-duotone-History';
    case LineduotoneAlarm = 'solar-line-duotone-Alarm';
    case LineduotoneWatchSquare = 'solar-line-duotone-WatchSquare';
    case LineduotoneWatchSquareMinimalistic = 'solar-line-duotone-WatchSquareMinimalistic';
    case LineduotoneMagniferBug = 'solar-line-duotone-MagniferBug';
    case LineduotoneMagnifer = 'solar-line-duotone-Magnifer';
    case LineduotoneMagniferZoomIn = 'solar-line-duotone-MagniferZoomIn';
    case LineduotoneRoundedMagnifer = 'solar-line-duotone-RoundedMagnifer';
    case LineduotoneRoundedMagniferZoomIn = 'solar-line-duotone-RoundedMagniferZoomIn';
    case LineduotoneMinimalisticMagniferBug = 'solar-line-duotone-MinimalisticMagniferBug';
    case LineduotoneRoundedMagniferBug = 'solar-line-duotone-RoundedMagniferBug';
    case LineduotoneMinimalisticMagniferZoomOut = 'solar-line-duotone-MinimalisticMagniferZoomOut';
    case LineduotoneMinimalisticMagnifer = 'solar-line-duotone-MinimalisticMagnifer';
    case LineduotoneRoundedMagniferZoomOut = 'solar-line-duotone-RoundedMagniferZoomOut';
    case LineduotoneMinimalisticMagniferZoomIn = 'solar-line-duotone-MinimalisticMagniferZoomIn';
    case LineduotoneMagniferZoomOut = 'solar-line-duotone-MagniferZoomOut';
    case LineduotoneBagCheck = 'solar-line-duotone-BagCheck';
    case LineduotoneShopMinimalistic = 'solar-line-duotone-ShopMinimalistic';
    case LineduotoneShop = 'solar-line-duotone-Shop';
    case LineduotoneCartCheck = 'solar-line-duotone-CartCheck';
    case LineduotoneCart = 'solar-line-duotone-Cart';
    case LineduotoneCart3 = 'solar-line-duotone-Cart3';
    case LineduotoneCart2 = 'solar-line-duotone-Cart2';
    case LineduotoneBagMusic = 'solar-line-duotone-BagMusic';
    case LineduotoneCartLargeMinimalistic = 'solar-line-duotone-CartLargeMinimalistic';
    case LineduotoneCart5 = 'solar-line-duotone-Cart5';
    case LineduotoneCart4 = 'solar-line-duotone-Cart4';
    case LineduotoneBag = 'solar-line-duotone-Bag';
    case LineduotoneBagHeart = 'solar-line-duotone-BagHeart';
    case LineduotoneCartPlus = 'solar-line-duotone-CartPlus';
    case LineduotoneCartLarge = 'solar-line-duotone-CartLarge';
    case LineduotoneBagCross = 'solar-line-duotone-BagCross';
    case LineduotoneBagMusic2 = 'solar-line-duotone-BagMusic2';
    case LineduotoneBag5 = 'solar-line-duotone-Bag5';
    case LineduotoneBag4 = 'solar-line-duotone-Bag4';
    case LineduotoneCartLarge4 = 'solar-line-duotone-CartLarge4';
    case LineduotoneCartLarge3 = 'solar-line-duotone-CartLarge3';
    case LineduotoneBag3 = 'solar-line-duotone-Bag3';
    case LineduotoneBag2 = 'solar-line-duotone-Bag2';
    case LineduotoneShop2 = 'solar-line-duotone-Shop2';
    case LineduotoneCartLarge2 = 'solar-line-duotone-CartLarge2';
    case LineduotoneBagSmile = 'solar-line-duotone-BagSmile';
    case LineduotoneCartCross = 'solar-line-duotone-CartCross';
    case LineduotoneInfoSquare = 'solar-line-duotone-InfoSquare';
    case LineduotoneFlashlightOn = 'solar-line-duotone-FlashlightOn';
    case LineduotoneXXX = 'solar-line-duotone-XXX';
    case LineduotoneFigma = 'solar-line-duotone-Figma';
    case LineduotoneFlashlight = 'solar-line-duotone-Flashlight';
    case LineduotoneGhost = 'solar-line-duotone-Ghost';
    case LineduotoneCupMusic = 'solar-line-duotone-CupMusic';
    case LineduotoneBatteryFullMinimalistic = 'solar-line-duotone-BatteryFullMinimalistic';
    case LineduotoneDangerCircle = 'solar-line-duotone-DangerCircle';
    case LineduotoneCheckSquare = 'solar-line-duotone-CheckSquare';
    case LineduotoneGhostSmile = 'solar-line-duotone-GhostSmile';
    case LineduotoneTarget = 'solar-line-duotone-Target';
    case LineduotoneBatteryHalfMinimalistic = 'solar-line-duotone-BatteryHalfMinimalistic';
    case LineduotoneScissors = 'solar-line-duotone-Scissors';
    case LineduotonePinList = 'solar-line-duotone-PinList';
    case LineduotoneBatteryCharge = 'solar-line-duotone-BatteryCharge';
    case LineduotoneUmbrella = 'solar-line-duotone-Umbrella';
    case LineduotoneHomeSmile = 'solar-line-duotone-HomeSmile';
    case LineduotoneHome = 'solar-line-duotone-Home';
    case LineduotoneCopyright = 'solar-line-duotone-Copyright';
    case LineduotoneHomeWifi = 'solar-line-duotone-HomeWifi';
    case LineduotoneTShirt = 'solar-line-duotone-TShirt';
    case LineduotoneBatteryChargeMinimalistic = 'solar-line-duotone-BatteryChargeMinimalistic';
    case LineduotoneCupStar = 'solar-line-duotone-CupStar';
    case LineduotoneSpecialEffects = 'solar-line-duotone-SpecialEffects';
    case LineduotoneBody = 'solar-line-duotone-Body';
    case LineduotoneHamburgerMenu = 'solar-line-duotone-HamburgerMenu';
    case LineduotonePower = 'solar-line-duotone-Power';
    case LineduotoneDatabase = 'solar-line-duotone-Database';
    case LineduotoneCursorSquare = 'solar-line-duotone-CursorSquare';
    case LineduotoneFuel = 'solar-line-duotone-Fuel';
    case LineduotoneMentionCircle = 'solar-line-duotone-MentionCircle';
    case LineduotoneConfettiMinimalistic = 'solar-line-duotone-ConfettiMinimalistic';
    case LineduotoneMenuDotsCircle = 'solar-line-duotone-MenuDotsCircle';
    case LineduotonePaw = 'solar-line-duotone-Paw';
    case LineduotoneSubtitles = 'solar-line-duotone-Subtitles';
    case LineduotoneSliderVerticalMinimalistic = 'solar-line-duotone-SliderVerticalMinimalistic';
    case LineduotoneCrownMinimalistic = 'solar-line-duotone-CrownMinimalistic';
    case LineduotoneMenuDots = 'solar-line-duotone-MenuDots';
    case LineduotoneDelivery = 'solar-line-duotone-Delivery';
    case LineduotoneWaterdrop = 'solar-line-duotone-Waterdrop';
    case LineduotonePerfume = 'solar-line-duotone-Perfume';
    case LineduotoneHomeAngle2 = 'solar-line-duotone-HomeAngle2';
    case LineduotoneHomeWifiAngle = 'solar-line-duotone-HomeWifiAngle';
    case LineduotoneQuestionCircle = 'solar-line-duotone-QuestionCircle';
    case LineduotoneTrashBinMinimalistic = 'solar-line-duotone-TrashBinMinimalistic';
    case LineduotoneMagicStick3 = 'solar-line-duotone-MagicStick3';
    case LineduotoneAddSquare = 'solar-line-duotone-AddSquare';
    case LineduotoneCrownStar = 'solar-line-duotone-CrownStar';
    case LineduotoneMagnet = 'solar-line-duotone-Magnet';
    case LineduotoneConfetti = 'solar-line-duotone-Confetti';
    case LineduotonePin = 'solar-line-duotone-Pin';
    case LineduotoneMinusSquare = 'solar-line-duotone-MinusSquare';
    case LineduotoneBolt = 'solar-line-duotone-Bolt';
    case LineduotoneCloseCircle = 'solar-line-duotone-CloseCircle';
    case LineduotoneForbiddenCircle = 'solar-line-duotone-ForbiddenCircle';
    case LineduotoneMagicStick2 = 'solar-line-duotone-MagicStick2';
    case LineduotoneCrownLine = 'solar-line-duotone-CrownLine';
    case LineduotoneBoltCircle = 'solar-line-duotone-BoltCircle';
    case LineduotoneFlag = 'solar-line-duotone-Flag';
    case LineduotoneSliderHorizontal = 'solar-line-duotone-SliderHorizontal';
    case LineduotoneHighDefinition = 'solar-line-duotone-HighDefinition';
    case LineduotoneCursor = 'solar-line-duotone-Cursor';
    case LineduotoneFeed = 'solar-line-duotone-Feed';
    case LineduotoneTrafficEconomy = 'solar-line-duotone-TrafficEconomy';
    case LineduotoneAugmentedReality = 'solar-line-duotone-AugmentedReality';
    case LineduotoneIcon4K = 'solar-line-duotone-Icon4K';
    case LineduotoneMagnetWave = 'solar-line-duotone-MagnetWave';
    case LineduotoneHomeSmileAngle = 'solar-line-duotone-HomeSmileAngle';
    case LineduotoneSliderVertical = 'solar-line-duotone-SliderVertical';
    case LineduotoneCheckCircle = 'solar-line-duotone-CheckCircle';
    case LineduotoneCopy = 'solar-line-duotone-Copy';
    case LineduotoneDangerSquare = 'solar-line-duotone-DangerSquare';
    case LineduotoneSkirt = 'solar-line-duotone-Skirt';
    case LineduotoneGlasses = 'solar-line-duotone-Glasses';
    case LineduotoneHomeAdd = 'solar-line-duotone-HomeAdd';
    case LineduotoneSledgehammer = 'solar-line-duotone-Sledgehammer';
    case LineduotoneInfoCircle = 'solar-line-duotone-InfoCircle';
    case LineduotoneDangerTriangle = 'solar-line-duotone-DangerTriangle';
    case LineduotonePinCircle = 'solar-line-duotone-PinCircle';
    case LineduotoneSmartHome = 'solar-line-duotone-SmartHome';
    case LineduotoneScissorsSquare = 'solar-line-duotone-ScissorsSquare';
    case LineduotoneSleeping = 'solar-line-duotone-Sleeping';
    case LineduotoneBox = 'solar-line-duotone-Box';
    case LineduotoneCrown = 'solar-line-duotone-Crown';
    case LineduotoneBroom = 'solar-line-duotone-Broom';
    case LineduotonePostsCarouselHorizontal = 'solar-line-duotone-PostsCarouselHorizontal';
    case LineduotoneFlag2 = 'solar-line-duotone-Flag2';
    case LineduotonePlate = 'solar-line-duotone-Plate';
    case LineduotoneTrashBinTrash = 'solar-line-duotone-TrashBinTrash';
    case LineduotoneCupFirst = 'solar-line-duotone-CupFirst';
    case LineduotoneSmartHomeAngle = 'solar-line-duotone-SmartHomeAngle';
    case LineduotonePaperBin = 'solar-line-duotone-PaperBin';
    case LineduotoneBoxMinimalistic = 'solar-line-duotone-BoxMinimalistic';
    case LineduotoneDanger = 'solar-line-duotone-Danger';
    case LineduotoneMenuDotsSquare = 'solar-line-duotone-MenuDotsSquare';
    case LineduotoneHanger2 = 'solar-line-duotone-Hanger2';
    case LineduotoneBatteryHalf = 'solar-line-duotone-BatteryHalf';
    case LineduotoneHome2 = 'solar-line-duotone-Home2';
    case LineduotonePostsCarouselVertical = 'solar-line-duotone-PostsCarouselVertical';
    case LineduotoneRevote = 'solar-line-duotone-Revote';
    case LineduotoneMentionSquare = 'solar-line-duotone-MentionSquare';
    case LineduotoneWinRar = 'solar-line-duotone-WinRar';
    case LineduotoneForbidden = 'solar-line-duotone-Forbidden';
    case LineduotoneQuestionSquare = 'solar-line-duotone-QuestionSquare';
    case LineduotoneHanger = 'solar-line-duotone-Hanger';
    case LineduotoneReorder = 'solar-line-duotone-Reorder';
    case LineduotoneHomeAddAngle = 'solar-line-duotone-HomeAddAngle';
    case LineduotoneMasks = 'solar-line-duotone-Masks';
    case LineduotoneGift = 'solar-line-duotone-Gift';
    case LineduotoneCreativeCommons = 'solar-line-duotone-CreativeCommons';
    case LineduotoneSliderMinimalisticHorizontal = 'solar-line-duotone-SliderMinimalisticHorizontal';
    case LineduotoneHomeAngle = 'solar-line-duotone-HomeAngle';
    case LineduotoneBatteryLowMinimalistic = 'solar-line-duotone-BatteryLowMinimalistic';
    case LineduotoneShare = 'solar-line-duotone-Share';
    case LineduotoneTrashBin2 = 'solar-line-duotone-TrashBin2';
    case LineduotoneSort = 'solar-line-duotone-Sort';
    case LineduotoneMinusCircle = 'solar-line-duotone-MinusCircle';
    case LineduotoneExplicit = 'solar-line-duotone-Explicit';
    case LineduotoneTraffic = 'solar-line-duotone-Traffic';
    case LineduotoneFilter = 'solar-line-duotone-Filter';
    case LineduotoneCloseSquare = 'solar-line-duotone-CloseSquare';
    case LineduotoneAddCircle = 'solar-line-duotone-AddCircle';
    case LineduotoneFerrisWheel = 'solar-line-duotone-FerrisWheel';
    case LineduotoneCup = 'solar-line-duotone-Cup';
    case LineduotoneBalloon = 'solar-line-duotone-Balloon';
    case LineduotoneHelp = 'solar-line-duotone-Help';
    case LineduotoneBatteryFull = 'solar-line-duotone-BatteryFull';
    case LineduotoneCat = 'solar-line-duotone-Cat';
    case LineduotoneMaskSad = 'solar-line-duotone-MaskSad';
    case LineduotoneHighQuality = 'solar-line-duotone-HighQuality';
    case LineduotoneMagicStick = 'solar-line-duotone-MagicStick';
    case LineduotoneCosmetic = 'solar-line-duotone-Cosmetic';
    case LineduotoneBatteryLow = 'solar-line-duotone-BatteryLow';
    case LineduotoneShareCircle = 'solar-line-duotone-ShareCircle';
    case LineduotoneMaskHapply = 'solar-line-duotone-MaskHapply';
    case LineduotoneAccessibility = 'solar-line-duotone-Accessibility';
    case LineduotoneTrashBinMinimalistic2 = 'solar-line-duotone-TrashBinMinimalistic2';
    case LineduotoneIncomingCallRounded = 'solar-line-duotone-IncomingCallRounded';
    case LineduotoneCallDropped = 'solar-line-duotone-CallDropped';
    case LineduotoneCallChat = 'solar-line-duotone-CallChat';
    case LineduotoneCallCancelRounded = 'solar-line-duotone-CallCancelRounded';
    case LineduotoneCallMedicineRounded = 'solar-line-duotone-CallMedicineRounded';
    case LineduotoneCallDroppedRounded = 'solar-line-duotone-CallDroppedRounded';
    case LineduotoneRecordSquare = 'solar-line-duotone-RecordSquare';
    case LineduotonePhoneCalling = 'solar-line-duotone-PhoneCalling';
    case LineduotonePhoneRounded = 'solar-line-duotone-PhoneRounded';
    case LineduotoneCallMedicine = 'solar-line-duotone-CallMedicine';
    case LineduotoneRecordMinimalistic = 'solar-line-duotone-RecordMinimalistic';
    case LineduotoneEndCall = 'solar-line-duotone-EndCall';
    case LineduotoneOutgoingCall = 'solar-line-duotone-OutgoingCall';
    case LineduotoneRecordCircle = 'solar-line-duotone-RecordCircle';
    case LineduotoneIncomingCall = 'solar-line-duotone-IncomingCall';
    case LineduotoneCallChatRounded = 'solar-line-duotone-CallChatRounded';
    case LineduotoneEndCallRounded = 'solar-line-duotone-EndCallRounded';
    case LineduotonePhone = 'solar-line-duotone-Phone';
    case LineduotoneOutgoingCallRounded = 'solar-line-duotone-OutgoingCallRounded';
    case LineduotoneCallCancel = 'solar-line-duotone-CallCancel';
    case LineduotonePhoneCallingRounded = 'solar-line-duotone-PhoneCallingRounded';
    case LineduotoneStationMinimalistic = 'solar-line-duotone-StationMinimalistic';
    case LineduotoneSidebarCode = 'solar-line-duotone-SidebarCode';
    case LineduotoneWiFiRouterMinimalistic = 'solar-line-duotone-WiFiRouterMinimalistic';
    case LineduotoneUSB = 'solar-line-duotone-USB';
    case LineduotoneSiderbar = 'solar-line-duotone-Siderbar';
    case LineduotoneCode2 = 'solar-line-duotone-Code2';
    case LineduotoneSlashCircle = 'solar-line-duotone-SlashCircle';
    case LineduotoneScreencast = 'solar-line-duotone-Screencast';
    case LineduotoneHashtagSquare = 'solar-line-duotone-HashtagSquare';
    case LineduotoneSidebarMinimalistic = 'solar-line-duotone-SidebarMinimalistic';
    case LineduotoneCode = 'solar-line-duotone-Code';
    case LineduotoneUsbSquare = 'solar-line-duotone-UsbSquare';
    case LineduotoneWiFiRouter = 'solar-line-duotone-WiFiRouter';
    case LineduotoneCodeCircle = 'solar-line-duotone-CodeCircle';
    case LineduotoneTranslation = 'solar-line-duotone-Translation';
    case LineduotoneBugMinimalistic = 'solar-line-duotone-BugMinimalistic';
    case LineduotoneStation = 'solar-line-duotone-Station';
    case LineduotoneProgramming = 'solar-line-duotone-Programming';
    case LineduotoneWiFiRouterRound = 'solar-line-duotone-WiFiRouterRound';
    case LineduotoneHashtag = 'solar-line-duotone-Hashtag';
    case LineduotoneBug = 'solar-line-duotone-Bug';
    case LineduotoneHashtagChat = 'solar-line-duotone-HashtagChat';
    case LineduotoneCommand = 'solar-line-duotone-Command';
    case LineduotoneTranslation2 = 'solar-line-duotone-Translation2';
    case LineduotoneHashtagCircle = 'solar-line-duotone-HashtagCircle';
    case LineduotoneScreencast2 = 'solar-line-duotone-Screencast2';
    case LineduotoneSlashSquare = 'solar-line-duotone-SlashSquare';
    case LineduotoneWindowFrame = 'solar-line-duotone-WindowFrame';
    case LineduotoneStructure = 'solar-line-duotone-Structure';
    case LineduotoneUsbCircle = 'solar-line-duotone-UsbCircle';
    case LineduotoneCodeSquare = 'solar-line-duotone-CodeSquare';
    case LineduotoneNotes = 'solar-line-duotone-Notes';
    case LineduotoneDocumentText = 'solar-line-duotone-DocumentText';
    case LineduotoneDocumentAdd = 'solar-line-duotone-DocumentAdd';
    case LineduotoneDocumentMedicine = 'solar-line-duotone-DocumentMedicine';
    case LineduotoneArchiveMinimalistic = 'solar-line-duotone-ArchiveMinimalistic';
    case LineduotoneClipboard = 'solar-line-duotone-Clipboard';
    case LineduotoneClipboardAdd = 'solar-line-duotone-ClipboardAdd';
    case LineduotoneArchive = 'solar-line-duotone-Archive';
    case LineduotoneClipboardHeart = 'solar-line-duotone-ClipboardHeart';
    case LineduotoneClipboardRemove = 'solar-line-duotone-ClipboardRemove';
    case LineduotoneClipboardText = 'solar-line-duotone-ClipboardText';
    case LineduotoneDocument = 'solar-line-duotone-Document';
    case LineduotoneNotesMinimalistic = 'solar-line-duotone-NotesMinimalistic';
    case LineduotoneArchiveUp = 'solar-line-duotone-ArchiveUp';
    case LineduotoneArchiveUpMinimlistic = 'solar-line-duotone-ArchiveUpMinimlistic';
    case LineduotoneArchiveCheck = 'solar-line-duotone-ArchiveCheck';
    case LineduotoneArchiveDown = 'solar-line-duotone-ArchiveDown';
    case LineduotoneArchiveDownMinimlistic = 'solar-line-duotone-ArchiveDownMinimlistic';
    case LineduotoneDocumentsMinimalistic = 'solar-line-duotone-DocumentsMinimalistic';
    case LineduotoneClipboardCheck = 'solar-line-duotone-ClipboardCheck';
    case LineduotoneClipboardList = 'solar-line-duotone-ClipboardList';
    case LineduotoneDocuments = 'solar-line-duotone-Documents';
    case LineduotoneNotebook = 'solar-line-duotone-Notebook';
    case LineduotoneGalleryRound = 'solar-line-duotone-GalleryRound';
    case LineduotonePlayCircle = 'solar-line-duotone-PlayCircle';
    case LineduotoneStream = 'solar-line-duotone-Stream';
    case LineduotoneGalleryRemove = 'solar-line-duotone-GalleryRemove';
    case LineduotoneClapperboard = 'solar-line-duotone-Clapperboard';
    case LineduotonePauseCircle = 'solar-line-duotone-PauseCircle';
    case LineduotoneRewind5SecondsBack = 'solar-line-duotone-Rewind5SecondsBack';
    case LineduotoneRepeat = 'solar-line-duotone-Repeat';
    case LineduotoneClapperboardEdit = 'solar-line-duotone-ClapperboardEdit';
    case LineduotoneVideoFrameCut = 'solar-line-duotone-VideoFrameCut';
    case LineduotonePanorama = 'solar-line-duotone-Panorama';
    case LineduotonePlayStream = 'solar-line-duotone-PlayStream';
    case LineduotoneClapperboardOpen = 'solar-line-duotone-ClapperboardOpen';
    case LineduotoneClapperboardText = 'solar-line-duotone-ClapperboardText';
    case LineduotoneLibrary = 'solar-line-duotone-Library';
    case LineduotoneReel2 = 'solar-line-duotone-Reel2';
    case LineduotoneVolumeSmall = 'solar-line-duotone-VolumeSmall';
    case LineduotoneVideoFrame = 'solar-line-duotone-VideoFrame';
    case LineduotoneMicrophoneLarge = 'solar-line-duotone-MicrophoneLarge';
    case LineduotoneRewindForward = 'solar-line-duotone-RewindForward';
    case LineduotoneRewindBackCircle = 'solar-line-duotone-RewindBackCircle';
    case LineduotoneMicrophone = 'solar-line-duotone-Microphone';
    case LineduotoneVideoFrameReplace = 'solar-line-duotone-VideoFrameReplace';
    case LineduotoneClapperboardPlay = 'solar-line-duotone-ClapperboardPlay';
    case LineduotoneGalleryDownload = 'solar-line-duotone-GalleryDownload';
    case LineduotoneMusicNote4 = 'solar-line-duotone-MusicNote4';
    case LineduotoneVideocameraRecord = 'solar-line-duotone-VideocameraRecord';
    case LineduotonePlaybackSpeed = 'solar-line-duotone-PlaybackSpeed';
    case LineduotoneSoundwave = 'solar-line-duotone-Soundwave';
    case LineduotoneStopCircle = 'solar-line-duotone-StopCircle';
    case LineduotoneQuitFullScreenCircle = 'solar-line-duotone-QuitFullScreenCircle';
    case LineduotoneRewindBack = 'solar-line-duotone-RewindBack';
    case LineduotoneRepeatOne = 'solar-line-duotone-RepeatOne';
    case LineduotoneGalleryCheck = 'solar-line-duotone-GalleryCheck';
    case LineduotoneWallpaper = 'solar-line-duotone-Wallpaper';
    case LineduotoneRewindForwardCircle = 'solar-line-duotone-RewindForwardCircle';
    case LineduotoneGalleryEdit = 'solar-line-duotone-GalleryEdit';
    case LineduotoneGallery = 'solar-line-duotone-Gallery';
    case LineduotoneGalleryMinimalistic = 'solar-line-duotone-GalleryMinimalistic';
    case LineduotoneUploadTrack = 'solar-line-duotone-UploadTrack';
    case LineduotoneVolume = 'solar-line-duotone-Volume';
    case LineduotoneUploadTrack2 = 'solar-line-duotone-UploadTrack2';
    case LineduotoneMusicNotes = 'solar-line-duotone-MusicNotes';
    case LineduotoneMusicNote2 = 'solar-line-duotone-MusicNote2';
    case LineduotoneCameraAdd = 'solar-line-duotone-CameraAdd';
    case LineduotonePodcast = 'solar-line-duotone-Podcast';
    case LineduotoneCameraRotate = 'solar-line-duotone-CameraRotate';
    case LineduotoneMusicNote3 = 'solar-line-duotone-MusicNote3';
    case LineduotoneStop = 'solar-line-duotone-Stop';
    case LineduotoneMuted = 'solar-line-duotone-Muted';
    case LineduotoneSkipNext = 'solar-line-duotone-SkipNext';
    case LineduotoneGallerySend = 'solar-line-duotone-GallerySend';
    case LineduotoneRecord = 'solar-line-duotone-Record';
    case LineduotoneFullScreenCircle = 'solar-line-duotone-FullScreenCircle';
    case LineduotoneVolumeCross = 'solar-line-duotone-VolumeCross';
    case LineduotoneSoundwaveCircle = 'solar-line-duotone-SoundwaveCircle';
    case LineduotoneSkipPrevious = 'solar-line-duotone-SkipPrevious';
    case LineduotoneRewind5SecondsForward = 'solar-line-duotone-Rewind5SecondsForward';
    case LineduotonePlay = 'solar-line-duotone-Play';
    case LineduotonePIP = 'solar-line-duotone-PIP';
    case LineduotoneMusicLibrary = 'solar-line-duotone-MusicLibrary';
    case LineduotoneVideoFrame2 = 'solar-line-duotone-VideoFrame2';
    case LineduotoneCamera = 'solar-line-duotone-Camera';
    case LineduotoneQuitPip = 'solar-line-duotone-QuitPip';
    case LineduotoneClapperboardOpenPlay = 'solar-line-duotone-ClapperboardOpenPlay';
    case LineduotoneRewind10SecondsBack = 'solar-line-duotone-Rewind10SecondsBack';
    case LineduotoneRepeatOneMinimalistic = 'solar-line-duotone-RepeatOneMinimalistic';
    case LineduotoneVinyl = 'solar-line-duotone-Vinyl';
    case LineduotoneVideoLibrary = 'solar-line-duotone-VideoLibrary';
    case LineduotoneGalleryWide = 'solar-line-duotone-GalleryWide';
    case LineduotoneReel = 'solar-line-duotone-Reel';
    case LineduotoneToPip = 'solar-line-duotone-ToPip';
    case LineduotonePip2 = 'solar-line-duotone-Pip2';
    case LineduotoneFullScreen = 'solar-line-duotone-FullScreen';
    case LineduotoneCameraMinimalistic = 'solar-line-duotone-CameraMinimalistic';
    case LineduotoneVideoFrameCut2 = 'solar-line-duotone-VideoFrameCut2';
    case LineduotoneGalleryCircle = 'solar-line-duotone-GalleryCircle';
    case LineduotoneVideoFramePlayHorizontal = 'solar-line-duotone-VideoFramePlayHorizontal';
    case LineduotoneMusicNoteSlider2 = 'solar-line-duotone-MusicNoteSlider2';
    case LineduotoneMusicNoteSlider = 'solar-line-duotone-MusicNoteSlider';
    case LineduotoneVideocameraAdd = 'solar-line-duotone-VideocameraAdd';
    case LineduotoneQuitFullScreenSquare = 'solar-line-duotone-QuitFullScreenSquare';
    case LineduotoneAlbum = 'solar-line-duotone-Album';
    case LineduotoneGalleryAdd = 'solar-line-duotone-GalleryAdd';
    case LineduotoneCameraSquare = 'solar-line-duotone-CameraSquare';
    case LineduotoneRewind15SecondsBack = 'solar-line-duotone-Rewind15SecondsBack';
    case LineduotoneRewind15SecondsForward = 'solar-line-duotone-Rewind15SecondsForward';
    case LineduotoneVinylRecord = 'solar-line-duotone-VinylRecord';
    case LineduotoneShuffle = 'solar-line-duotone-Shuffle';
    case LineduotonePause = 'solar-line-duotone-Pause';
    case LineduotoneMusicNote = 'solar-line-duotone-MusicNote';
    case LineduotoneQuitFullScreen = 'solar-line-duotone-QuitFullScreen';
    case LineduotoneMicrophone2 = 'solar-line-duotone-Microphone2';
    case LineduotoneVideocamera = 'solar-line-duotone-Videocamera';
    case LineduotoneGalleryFavourite = 'solar-line-duotone-GalleryFavourite';
    case LineduotoneMusicLibrary2 = 'solar-line-duotone-MusicLibrary2';
    case LineduotoneVideoFramePlayVertical = 'solar-line-duotone-VideoFramePlayVertical';
    case LineduotoneFullScreenSquare = 'solar-line-duotone-FullScreenSquare';
    case LineduotoneRewind10SecondsForward = 'solar-line-duotone-Rewind10SecondsForward';
    case LineduotoneVolumeLoud = 'solar-line-duotone-VolumeLoud';
    case LineduotoneMicrophone3 = 'solar-line-duotone-Microphone3';
    case LineduotoneSoundwaveSquare = 'solar-line-duotone-SoundwaveSquare';
    case LineduotoneCardholder = 'solar-line-duotone-Cardholder';
    case LineduotoneBillList = 'solar-line-duotone-BillList';
    case LineduotoneSaleSquare = 'solar-line-duotone-SaleSquare';
    case LineduotoneDollar = 'solar-line-duotone-Dollar';
    case LineduotoneTicket = 'solar-line-duotone-Ticket';
    case LineduotoneTag = 'solar-line-duotone-Tag';
    case LineduotoneCashOut = 'solar-line-duotone-CashOut';
    case LineduotoneWallet2 = 'solar-line-duotone-Wallet2';
    case LineduotoneRuble = 'solar-line-duotone-Ruble';
    case LineduotoneCardTransfer = 'solar-line-duotone-CardTransfer';
    case LineduotoneEuro = 'solar-line-duotone-Euro';
    case LineduotoneSale = 'solar-line-duotone-Sale';
    case LineduotoneCardSearch = 'solar-line-duotone-CardSearch';
    case LineduotoneWallet = 'solar-line-duotone-Wallet';
    case LineduotoneBillCross = 'solar-line-duotone-BillCross';
    case LineduotoneTicketSale = 'solar-line-duotone-TicketSale';
    case LineduotoneSafeSquare = 'solar-line-duotone-SafeSquare';
    case LineduotoneCard = 'solar-line-duotone-Card';
    case LineduotoneSafe2 = 'solar-line-duotone-Safe2';
    case LineduotoneDollarMinimalistic = 'solar-line-duotone-DollarMinimalistic';
    case LineduotoneTagPrice = 'solar-line-duotone-TagPrice';
    case LineduotoneMoneyBag = 'solar-line-duotone-MoneyBag';
    case LineduotoneBill = 'solar-line-duotone-Bill';
    case LineduotoneCardSend = 'solar-line-duotone-CardSend';
    case LineduotoneCardRecive = 'solar-line-duotone-CardRecive';
    case LineduotoneBanknote2 = 'solar-line-duotone-Banknote2';
    case LineduotoneTagHorizontal = 'solar-line-duotone-TagHorizontal';
    case LineduotoneBillCheck = 'solar-line-duotone-BillCheck';
    case LineduotoneTickerStar = 'solar-line-duotone-TickerStar';
    case LineduotoneBanknote = 'solar-line-duotone-Banknote';
    case LineduotoneVerifiedCheck = 'solar-line-duotone-VerifiedCheck';
    case LineduotoneWadOfMoney = 'solar-line-duotone-WadOfMoney';
    case LineduotoneCard2 = 'solar-line-duotone-Card2';
    case LineduotoneSafeCircle = 'solar-line-duotone-SafeCircle';
    case LineduotoneWalletMoney = 'solar-line-duotone-WalletMoney';
    case LineduotoneList = 'solar-line-duotone-List';
    case LineduotoneListDownMinimalistic = 'solar-line-duotone-ListDownMinimalistic';
    case LineduotonePlaylist2 = 'solar-line-duotone-Playlist2';
    case LineduotoneChecklistMinimalistic = 'solar-line-duotone-ChecklistMinimalistic';
    case LineduotonePlaaylistMinimalistic = 'solar-line-duotone-PlaaylistMinimalistic';
    case LineduotoneListHeart = 'solar-line-duotone-ListHeart';
    case LineduotoneListArrowDown = 'solar-line-duotone-ListArrowDown';
    case LineduotoneListArrowUp = 'solar-line-duotone-ListArrowUp';
    case LineduotoneListUpMinimalistic = 'solar-line-duotone-ListUpMinimalistic';
    case LineduotonePlaylist = 'solar-line-duotone-Playlist';
    case LineduotoneListUp = 'solar-line-duotone-ListUp';
    case LineduotoneListCrossMinimalistic = 'solar-line-duotone-ListCrossMinimalistic';
    case LineduotoneListCross = 'solar-line-duotone-ListCross';
    case LineduotoneListArrowDownMinimalistic = 'solar-line-duotone-ListArrowDownMinimalistic';
    case LineduotoneSortByAlphabet = 'solar-line-duotone-SortByAlphabet';
    case LineduotoneChecklist = 'solar-line-duotone-Checklist';
    case LineduotoneSortFromBottomToTop = 'solar-line-duotone-SortFromBottomToTop';
    case LineduotoneListCheck = 'solar-line-duotone-ListCheck';
    case LineduotonePlaylistMinimalistic2 = 'solar-line-duotone-PlaylistMinimalistic2';
    case LineduotonePlaylistMinimalistic3 = 'solar-line-duotone-PlaylistMinimalistic3';
    case LineduotoneList1 = 'solar-line-duotone-List1';
    case LineduotoneSortFromTopToBottom = 'solar-line-duotone-SortFromTopToBottom';
    case LineduotoneSortByTime = 'solar-line-duotone-SortByTime';
    case LineduotoneListDown = 'solar-line-duotone-ListDown';
    case LineduotoneListHeartMinimalistic = 'solar-line-duotone-ListHeartMinimalistic';
    case LineduotoneListCheckMinimalistic = 'solar-line-duotone-ListCheckMinimalistic';
    case LineduotoneListArrowUpMinimalistic = 'solar-line-duotone-ListArrowUpMinimalistic';
    case LineduotoneUserCrossRounded = 'solar-line-duotone-UserCrossRounded';
    case LineduotoneUser = 'solar-line-duotone-User';
    case LineduotoneUsersGroupRounded = 'solar-line-duotone-UsersGroupRounded';
    case LineduotoneUserPlusRounded = 'solar-line-duotone-UserPlusRounded';
    case LineduotoneUserBlock = 'solar-line-duotone-UserBlock';
    case LineduotoneUserMinus = 'solar-line-duotone-UserMinus';
    case LineduotoneUserHands = 'solar-line-duotone-UserHands';
    case LineduotoneUserHeart = 'solar-line-duotone-UserHeart';
    case LineduotoneUserMinusRounded = 'solar-line-duotone-UserMinusRounded';
    case LineduotoneUserCross = 'solar-line-duotone-UserCross';
    case LineduotoneUserSpeakRounded = 'solar-line-duotone-UserSpeakRounded';
    case LineduotoneUserId = 'solar-line-duotone-UserId';
    case LineduotoneUserBlockRounded = 'solar-line-duotone-UserBlockRounded';
    case LineduotoneUserHeartRounded = 'solar-line-duotone-UserHeartRounded';
    case LineduotoneUsersGroupTwoRounded = 'solar-line-duotone-UsersGroupTwoRounded';
    case LineduotoneUserHandUp = 'solar-line-duotone-UserHandUp';
    case LineduotoneUserCircle = 'solar-line-duotone-UserCircle';
    case LineduotoneUserRounded = 'solar-line-duotone-UserRounded';
    case LineduotoneUserCheck = 'solar-line-duotone-UserCheck';
    case LineduotoneUserPlus = 'solar-line-duotone-UserPlus';
    case LineduotoneUserCheckRounded = 'solar-line-duotone-UserCheckRounded';
    case LineduotoneUserSpeak = 'solar-line-duotone-UserSpeak';
    case LineduotoneVirus = 'solar-line-duotone-Virus';
    case LineduotoneAdhesivePlaster2 = 'solar-line-duotone-AdhesivePlaster2';
    case LineduotoneDropper = 'solar-line-duotone-Dropper';
    case LineduotonePulse2 = 'solar-line-duotone-Pulse2';
    case LineduotoneBoneBroken = 'solar-line-duotone-BoneBroken';
    case LineduotoneHeartPulse2 = 'solar-line-duotone-HeartPulse2';
    case LineduotoneMedicalKit = 'solar-line-duotone-MedicalKit';
    case LineduotoneTestTube = 'solar-line-duotone-TestTube';
    case LineduotoneHealth = 'solar-line-duotone-Health';
    case LineduotoneDropperMinimalistic2 = 'solar-line-duotone-DropperMinimalistic2';
    case LineduotoneDNA = 'solar-line-duotone-DNA';
    case LineduotoneDropper3 = 'solar-line-duotone-Dropper3';
    case LineduotoneThermometer = 'solar-line-duotone-Thermometer';
    case LineduotoneDropper2 = 'solar-line-duotone-Dropper2';
    case LineduotoneJarOfPills2 = 'solar-line-duotone-JarOfPills2';
    case LineduotoneBoneCrack = 'solar-line-duotone-BoneCrack';
    case LineduotoneJarOfPills = 'solar-line-duotone-JarOfPills';
    case LineduotoneSyringe = 'solar-line-duotone-Syringe';
    case LineduotoneStethoscope = 'solar-line-duotone-Stethoscope';
    case LineduotoneBenzeneRing = 'solar-line-duotone-BenzeneRing';
    case LineduotoneBacteria = 'solar-line-duotone-Bacteria';
    case LineduotoneAdhesivePlaster = 'solar-line-duotone-AdhesivePlaster';
    case LineduotoneBone = 'solar-line-duotone-Bone';
    case LineduotoneBones = 'solar-line-duotone-Bones';
    case LineduotonePill = 'solar-line-duotone-Pill';
    case LineduotonePills = 'solar-line-duotone-Pills';
    case LineduotoneHeartPulse = 'solar-line-duotone-HeartPulse';
    case LineduotoneTestTubeMinimalistic = 'solar-line-duotone-TestTubeMinimalistic';
    case LineduotonePills2 = 'solar-line-duotone-Pills2';
    case LineduotonePulse = 'solar-line-duotone-Pulse';
    case LineduotoneDropperMinimalistic = 'solar-line-duotone-DropperMinimalistic';
    case LineduotonePills3 = 'solar-line-duotone-Pills3';
    case LineduotoneWhisk = 'solar-line-duotone-Whisk';
    case LineduotoneBottle = 'solar-line-duotone-Bottle';
    case LineduotoneOvenMittsMinimalistic = 'solar-line-duotone-OvenMittsMinimalistic';
    case LineduotoneChefHatMinimalistic = 'solar-line-duotone-ChefHatMinimalistic';
    case LineduotoneTeaCup = 'solar-line-duotone-TeaCup';
    case LineduotoneWineglassTriangle = 'solar-line-duotone-WineglassTriangle';
    case LineduotoneOvenMitts = 'solar-line-duotone-OvenMitts';
    case LineduotoneCupPaper = 'solar-line-duotone-CupPaper';
    case LineduotoneLadle = 'solar-line-duotone-Ladle';
    case LineduotoneCorkscrew = 'solar-line-duotone-Corkscrew';
    case LineduotoneDonutBitten = 'solar-line-duotone-DonutBitten';
    case LineduotoneWineglass = 'solar-line-duotone-Wineglass';
    case LineduotoneDonut = 'solar-line-duotone-Donut';
    case LineduotoneCupHot = 'solar-line-duotone-CupHot';
    case LineduotoneChefHatHeart = 'solar-line-duotone-ChefHatHeart';
    case LineduotoneChefHat = 'solar-line-duotone-ChefHat';
    case LineduotoneRollingPin = 'solar-line-duotone-RollingPin';
    case LineduotoneCodeFile = 'solar-line-duotone-CodeFile';
    case LineduotoneFileCorrupted = 'solar-line-duotone-FileCorrupted';
    case LineduotoneFile = 'solar-line-duotone-File';
    case LineduotoneFileRight = 'solar-line-duotone-FileRight';
    case LineduotoneFileFavourite = 'solar-line-duotone-FileFavourite';
    case LineduotoneFileDownload = 'solar-line-duotone-FileDownload';
    case LineduotoneZipFile = 'solar-line-duotone-ZipFile';
    case LineduotoneFileText = 'solar-line-duotone-FileText';
    case LineduotoneFileSmile = 'solar-line-duotone-FileSmile)';
    case LineduotoneFileCheck = 'solar-line-duotone-FileCheck';
    case LineduotoneFileSend = 'solar-line-duotone-FileSend';
    case LineduotoneFileLeft = 'solar-line-duotone-FileLeft';
    case LineduotoneFigmaFile = 'solar-line-duotone-FigmaFile';
    case LineduotoneFileRemove = 'solar-line-duotone-FileRemove';
    case LineduotoneCloudFile = 'solar-line-duotone-CloudFile';
    case LineduotoneRemoveFolder = 'solar-line-duotone-RemoveFolder';
    case LineduotoneFolderFavouritestar = 'solar-line-duotone-FolderFavourite(star)';
    case LineduotoneAddFolder = 'solar-line-duotone-AddFolder';
    case LineduotoneFolderCheck = 'solar-line-duotone-FolderCheck';
    case LineduotoneFolderFavouritebookmark = 'solar-line-duotone-FolderFavourite(bookmark)';
    case LineduotoneFolder2 = 'solar-line-duotone-Folder2';
    case LineduotoneFolderSecurity = 'solar-line-duotone-FolderSecurity';
    case LineduotoneFolderCloud = 'solar-line-duotone-FolderCloud';
    case LineduotoneMoveToFolder = 'solar-line-duotone-MoveToFolder';
    case LineduotoneFolderError = 'solar-line-duotone-FolderError';
    case LineduotoneFolderPathConnect = 'solar-line-duotone-FolderPathConnect';
    case LineduotoneFolderOpen = 'solar-line-duotone-FolderOpen';
    case LineduotoneFolder = 'solar-line-duotone-Folder';
    case LineduotoneFolderWithFiles = 'solar-line-duotone-FolderWithFiles';
    case LineduotoneCloudCheck = 'solar-line-duotone-CloudCheck';
    case LineduotoneTemperature = 'solar-line-duotone-Temperature';
    case LineduotoneWind = 'solar-line-duotone-Wind';
    case LineduotoneCloudSnowfall = 'solar-line-duotone-CloudSnowfall';
    case LineduotoneSunrise = 'solar-line-duotone-Sunrise';
    case LineduotoneSun2 = 'solar-line-duotone-Sun2';
    case LineduotoneCloudSun = 'solar-line-duotone-CloudSun';
    case LineduotoneCloudBoltMinimalistic = 'solar-line-duotone-CloudBoltMinimalistic';
    case LineduotoneCloudDownload = 'solar-line-duotone-CloudDownload';
    case LineduotoneClouds = 'solar-line-duotone-Clouds';
    case LineduotoneTornado = 'solar-line-duotone-Tornado';
    case LineduotoneMoonSleep = 'solar-line-duotone-MoonSleep';
    case LineduotoneCloudUpload = 'solar-line-duotone-CloudUpload';
    case LineduotoneCloudRain = 'solar-line-duotone-CloudRain';
    case LineduotoneFog = 'solar-line-duotone-Fog';
    case LineduotoneSnowflake = 'solar-line-duotone-Snowflake';
    case LineduotoneMoonFog = 'solar-line-duotone-MoonFog';
    case LineduotoneCloudMinus = 'solar-line-duotone-CloudMinus';
    case LineduotoneCloudBolt = 'solar-line-duotone-CloudBolt';
    case LineduotoneCloudWaterdrop = 'solar-line-duotone-CloudWaterdrop';
    case LineduotoneSunset = 'solar-line-duotone-Sunset';
    case LineduotoneWaterdrops = 'solar-line-duotone-Waterdrops';
    case LineduotoneMoonStars = 'solar-line-duotone-MoonStars';
    case LineduotoneCloudPlus = 'solar-line-duotone-CloudPlus';
    case LineduotoneSun = 'solar-line-duotone-Sun';
    case LineduotoneCloudWaterdrops = 'solar-line-duotone-CloudWaterdrops';
    case LineduotoneCloudSun2 = 'solar-line-duotone-CloudSun2';
    case LineduotoneCloudyMoon = 'solar-line-duotone-CloudyMoon';
    case LineduotoneTornadoSmall = 'solar-line-duotone-TornadoSmall';
    case LineduotoneCloud = 'solar-line-duotone-Cloud';
    case LineduotoneSunFog = 'solar-line-duotone-SunFog';
    case LineduotoneCloundCross = 'solar-line-duotone-CloundCross';
    case LineduotoneCloudSnowfallMinimalistic = 'solar-line-duotone-CloudSnowfallMinimalistic';
    case LineduotoneCloudStorm = 'solar-line-duotone-CloudStorm';
    case LineduotoneMoon = 'solar-line-duotone-Moon';
    case LineduotoneRefreshCircle = 'solar-line-duotone-RefreshCircle';
    case LineduotoneSquareArrowRightDown = 'solar-line-duotone-SquareArrowRightDown';
    case LineduotoneRoundArrowLeftDown = 'solar-line-duotone-RoundArrowLeftDown';
    case LineduotoneRestart = 'solar-line-duotone-Restart';
    case LineduotoneRoundAltArrowDown = 'solar-line-duotone-RoundAltArrowDown';
    case LineduotoneRoundSortVertical = 'solar-line-duotone-RoundSortVertical';
    case LineduotoneSquareAltArrowUp = 'solar-line-duotone-SquareAltArrowUp';
    case LineduotoneArrowLeftUp = 'solar-line-duotone-ArrowLeftUp';
    case LineduotoneSortHorizontal = 'solar-line-duotone-SortHorizontal';
    case LineduotoneTransferHorizontal = 'solar-line-duotone-TransferHorizontal';
    case LineduotoneSquareDoubleAltArrowUp = 'solar-line-duotone-SquareDoubleAltArrowUp';
    case LineduotoneRoundArrowLeftUp = 'solar-line-duotone-RoundArrowLeftUp';
    case LineduotoneAltArrowRight = 'solar-line-duotone-AltArrowRight';
    case LineduotoneRoundDoubleAltArrowUp = 'solar-line-duotone-RoundDoubleAltArrowUp';
    case LineduotoneRestartCircle = 'solar-line-duotone-RestartCircle';
    case LineduotoneSquareArrowDown = 'solar-line-duotone-SquareArrowDown';
    case LineduotoneSortVertical = 'solar-line-duotone-SortVertical';
    case LineduotoneSquareSortHorizontal = 'solar-line-duotone-SquareSortHorizontal';
    case LineduotoneDoubleAltArrowLeft = 'solar-line-duotone-DoubleAltArrowLeft';
    case LineduotoneSquareAltArrowDown = 'solar-line-duotone-SquareAltArrowDown';
    case LineduotoneSquareAltArrowRight = 'solar-line-duotone-SquareAltArrowRight';
    case LineduotoneSquareArrowUp = 'solar-line-duotone-SquareArrowUp';
    case LineduotoneDoubleAltArrowRight = 'solar-line-duotone-DoubleAltArrowRight';
    case LineduotoneRoundTransferVertical = 'solar-line-duotone-RoundTransferVertical';
    case LineduotoneArrowLeft = 'solar-line-duotone-ArrowLeft';
    case LineduotoneRoundDoubleAltArrowRight = 'solar-line-duotone-RoundDoubleAltArrowRight';
    case LineduotoneSquareDoubleAltArrowLeft = 'solar-line-duotone-SquareDoubleAltArrowLeft';
    case LineduotoneAltArrowDown = 'solar-line-duotone-AltArrowDown';
    case LineduotoneRoundTransferHorizontal = 'solar-line-duotone-RoundTransferHorizontal';
    case LineduotoneRoundArrowRightDown = 'solar-line-duotone-RoundArrowRightDown';
    case LineduotoneArrowUp = 'solar-line-duotone-ArrowUp';
    case LineduotoneRoundArrowLeft = 'solar-line-duotone-RoundArrowLeft';
    case LineduotoneDoubleAltArrowUp = 'solar-line-duotone-DoubleAltArrowUp';
    case LineduotoneRoundArrowRight = 'solar-line-duotone-RoundArrowRight';
    case LineduotoneSquareTransferHorizontal = 'solar-line-duotone-SquareTransferHorizontal';
    case LineduotoneArrowRight = 'solar-line-duotone-ArrowRight';
    case LineduotoneRoundDoubleAltArrowLeft = 'solar-line-duotone-RoundDoubleAltArrowLeft';
    case LineduotoneRoundArrowUp = 'solar-line-duotone-RoundArrowUp';
    case LineduotoneSquareSortVertical = 'solar-line-duotone-SquareSortVertical';
    case LineduotoneAltArrowLeft = 'solar-line-duotone-AltArrowLeft';
    case LineduotoneSquareDoubleAltArrowRight = 'solar-line-duotone-SquareDoubleAltArrowRight';
    case LineduotoneRefresh = 'solar-line-duotone-Refresh';
    case LineduotoneTransferVertical = 'solar-line-duotone-TransferVertical';
    case LineduotoneRefreshSquare = 'solar-line-duotone-RefreshSquare';
    case LineduotoneSquareTransferVertical = 'solar-line-duotone-SquareTransferVertical';
    case LineduotoneSquareDoubleAltArrowDown = 'solar-line-duotone-SquareDoubleAltArrowDown';
    case LineduotoneRoundArrowRightUp = 'solar-line-duotone-RoundArrowRightUp';
    case LineduotoneArrowDown = 'solar-line-duotone-ArrowDown';
    case LineduotoneRestartSquare = 'solar-line-duotone-RestartSquare';
    case LineduotoneSquareArrowRight = 'solar-line-duotone-SquareArrowRight';
    case LineduotoneRoundDoubleAltArrowDown = 'solar-line-duotone-RoundDoubleAltArrowDown';
    case LineduotoneSquareArrowLeftUp = 'solar-line-duotone-SquareArrowLeftUp';
    case LineduotoneRoundArrowDown = 'solar-line-duotone-RoundArrowDown';
    case LineduotoneSquareArrowRightUp = 'solar-line-duotone-SquareArrowRightUp';
    case LineduotoneRoundTransferDiagonal = 'solar-line-duotone-RoundTransferDiagonal';
    case LineduotoneArrowRightDown = 'solar-line-duotone-ArrowRightDown';
    case LineduotoneArrowLeftDown = 'solar-line-duotone-ArrowLeftDown';
    case LineduotoneRoundAltArrowLeft = 'solar-line-duotone-RoundAltArrowLeft';
    case LineduotoneArrowRightUp = 'solar-line-duotone-ArrowRightUp';
    case LineduotoneSquareArrowLeftDown = 'solar-line-duotone-SquareArrowLeftDown';
    case LineduotoneRoundAltArrowUp = 'solar-line-duotone-RoundAltArrowUp';
    case LineduotoneAltArrowUp = 'solar-line-duotone-AltArrowUp';
    case LineduotoneSquareAltArrowLeft = 'solar-line-duotone-SquareAltArrowLeft';
    case LineduotoneRoundSortHorizontal = 'solar-line-duotone-RoundSortHorizontal';
    case LineduotoneDoubleAltArrowDown = 'solar-line-duotone-DoubleAltArrowDown';
    case LineduotoneRoundAltArrowRight = 'solar-line-duotone-RoundAltArrowRight';
    case LineduotoneSquareArrowLeft = 'solar-line-duotone-SquareArrowLeft';
    case LineduotoneTuningSquare2 = 'solar-line-duotone-TuningSquare2';
    case LineduotoneWidgetAdd = 'solar-line-duotone-WidgetAdd';
    case LineduotoneTuningSquare = 'solar-line-duotone-TuningSquare';
    case LineduotoneSettingsMinimalistic = 'solar-line-duotone-SettingsMinimalistic';
    case LineduotoneWidget6 = 'solar-line-duotone-Widget6';
    case LineduotoneWidget4 = 'solar-line-duotone-Widget4';
    case LineduotoneSettings = 'solar-line-duotone-Settings';
    case LineduotoneWidget5 = 'solar-line-duotone-Widget5';
    case LineduotoneWidget2 = 'solar-line-duotone-Widget2';
    case LineduotoneWidget3 = 'solar-line-duotone-Widget3';
    case LineduotoneTuning2 = 'solar-line-duotone-Tuning2';
    case LineduotoneTuning3 = 'solar-line-duotone-Tuning3';
    case LineduotoneWidget = 'solar-line-duotone-Widget';
    case LineduotoneTuning4 = 'solar-line-duotone-Tuning4';
    case LineduotoneTuning = 'solar-line-duotone-Tuning';
    case LineduotoneDiagramDown = 'solar-line-duotone-DiagramDown';
    case LineduotoneChart2 = 'solar-line-duotone-Chart2';
    case LineduotoneChart = 'solar-line-duotone-Chart';
    case LineduotoneDiagramUp = 'solar-line-duotone-DiagramUp';
    case LineduotoneGraphNew = 'solar-line-duotone-GraphNew';
    case LineduotoneCourseUp = 'solar-line-duotone-CourseUp';
    case LineduotoneGraphDownNew = 'solar-line-duotone-GraphDownNew';
    case LineduotonePieChart3 = 'solar-line-duotone-PieChart3';
    case LineduotonePieChart2 = 'solar-line-duotone-PieChart2';
    case LineduotoneGraphNewUp = 'solar-line-duotone-GraphNewUp';
    case LineduotonePieChart = 'solar-line-duotone-PieChart';
    case LineduotoneRoundGraph = 'solar-line-duotone-RoundGraph';
    case LineduotoneGraphUp = 'solar-line-duotone-GraphUp';
    case LineduotoneChartSquare = 'solar-line-duotone-ChartSquare';
    case LineduotoneCourseDown = 'solar-line-duotone-CourseDown';
    case LineduotoneChatSquare2 = 'solar-line-duotone-ChatSquare2';
    case LineduotoneGraphDown = 'solar-line-duotone-GraphDown';
    case LineduotoneGraph = 'solar-line-duotone-Graph';
    case LineduotonePresentationGraph = 'solar-line-duotone-PresentationGraph';
    case LineduotoneMaximizeSquare3 = 'solar-line-duotone-MaximizeSquare3';
    case LineduotoneMaximizeSquareMinimalistic = 'solar-line-duotone-MaximizeSquareMinimalistic';
    case LineduotoneMaximizeSquare2 = 'solar-line-duotone-MaximizeSquare2';
    case LineduotoneMinimizeSquare = 'solar-line-duotone-MinimizeSquare';
    case LineduotoneDownloadSquare = 'solar-line-duotone-DownloadSquare';
    case LineduotoneUndoLeftRoundSquare = 'solar-line-duotone-UndoLeftRoundSquare';
    case LineduotoneReply = 'solar-line-duotone-Reply';
    case LineduotoneLogout = 'solar-line-duotone-Logout';
    case LineduotoneReciveSquare = 'solar-line-duotone-ReciveSquare';
    case LineduotoneExport = 'solar-line-duotone-Export';
    case LineduotoneSendTwiceSquare = 'solar-line-duotone-SendTwiceSquare';
    case LineduotoneUndoLeftRound = 'solar-line-duotone-UndoLeftRound';
    case LineduotoneForward2 = 'solar-line-duotone-Forward2';
    case LineduotoneMaximize = 'solar-line-duotone-Maximize';
    case LineduotoneUndoRightRound = 'solar-line-duotone-UndoRightRound';
    case LineduotoneMinimizeSquare2 = 'solar-line-duotone-MinimizeSquare2';
    case LineduotoneMinimizeSquare3 = 'solar-line-duotone-MinimizeSquare3';
    case LineduotoneUploadTwiceSquare = 'solar-line-duotone-UploadTwiceSquare';
    case LineduotoneMinimize = 'solar-line-duotone-Minimize';
    case LineduotoneCircleTopUp = 'solar-line-duotone-CircleTopUp';
    case LineduotoneUploadMinimalistic = 'solar-line-duotone-UploadMinimalistic';
    case LineduotoneDownload = 'solar-line-duotone-Download';
    case LineduotoneImport = 'solar-line-duotone-Import';
    case LineduotoneLogin = 'solar-line-duotone-Login';
    case LineduotoneUndoLeft = 'solar-line-duotone-UndoLeft';
    case LineduotoneSquareTopUp = 'solar-line-duotone-SquareTopUp';
    case LineduotoneDownloadTwiceSquare = 'solar-line-duotone-DownloadTwiceSquare';
    case LineduotoneCircleBottomDown = 'solar-line-duotone-CircleBottomDown';
    case LineduotoneMaximizeSquare = 'solar-line-duotone-MaximizeSquare';
    case LineduotoneUploadSquare = 'solar-line-duotone-UploadSquare';
    case LineduotoneUndoRightSquare = 'solar-line-duotone-UndoRightSquare';
    case LineduotoneReciveTwiceSquare = 'solar-line-duotone-ReciveTwiceSquare';
    case LineduotoneCircleTopDown = 'solar-line-duotone-CircleTopDown';
    case LineduotoneArrowToDownLeft = 'solar-line-duotone-ArrowToDownLeft';
    case LineduotoneLogout2 = 'solar-line-duotone-Logout2';
    case LineduotoneLogout3 = 'solar-line-duotone-Logout3';
    case LineduotoneScale = 'solar-line-duotone-Scale';
    case LineduotoneArrowToDownRight = 'solar-line-duotone-ArrowToDownRight';
    case LineduotoneDownloadMinimalistic = 'solar-line-duotone-DownloadMinimalistic';
    case LineduotoneMinimizeSquareMinimalistic = 'solar-line-duotone-MinimizeSquareMinimalistic';
    case LineduotoneReply2 = 'solar-line-duotone-Reply2';
    case LineduotoneSquareBottomUp = 'solar-line-duotone-SquareBottomUp';
    case LineduotoneUndoRight = 'solar-line-duotone-UndoRight';
    case LineduotoneUndoLeftSquare = 'solar-line-duotone-UndoLeftSquare';
    case LineduotoneSendSquare = 'solar-line-duotone-SendSquare';
    case LineduotoneExit = 'solar-line-duotone-Exit';
    case LineduotoneSquareBottomDown = 'solar-line-duotone-SquareBottomDown';
    case LineduotoneUndoRightRoundSquare = 'solar-line-duotone-UndoRightRoundSquare';
    case LineduotoneArrowToTopLeft = 'solar-line-duotone-ArrowToTopLeft';
    case LineduotoneCircleBottomUp = 'solar-line-duotone-CircleBottomUp';
    case LineduotoneScreenShare = 'solar-line-duotone-ScreenShare';
    case LineduotoneUpload = 'solar-line-duotone-Upload';
    case LineduotoneSquareTopDown = 'solar-line-duotone-SquareTopDown';
    case LineduotoneArrowToTopRight = 'solar-line-duotone-ArrowToTopRight';
    case LineduotoneLogin3 = 'solar-line-duotone-Login3';
    case LineduotoneLogin2 = 'solar-line-duotone-Login2';
    case LineduotonePassport = 'solar-line-duotone-Passport';
    case LineduotoneDiplomaVerified = 'solar-line-duotone-DiplomaVerified';
    case LineduotoneCaseRound = 'solar-line-duotone-CaseRound';
    case LineduotoneBackpack = 'solar-line-duotone-Backpack';
    case LineduotoneBook2 = 'solar-line-duotone-Book2';
    case LineduotoneSquareAcademicCap2 = 'solar-line-duotone-SquareAcademicCap2';
    case LineduotoneCaseRoundMinimalistic = 'solar-line-duotone-CaseRoundMinimalistic';
    case LineduotoneCase = 'solar-line-duotone-Case';
    case LineduotoneBookBookmarkMinimalistic = 'solar-line-duotone-BookBookmarkMinimalistic';
    case LineduotoneBookmarkOpened = 'solar-line-duotone-BookmarkOpened';
    case LineduotoneDiploma = 'solar-line-duotone-Diploma';
    case LineduotoneBook = 'solar-line-duotone-Book';
    case LineduotoneSquareAcademicCap = 'solar-line-duotone-SquareAcademicCap';
    case LineduotoneBookmarkCircle = 'solar-line-duotone-BookmarkCircle';
    case LineduotoneCalculatorMinimalistic = 'solar-line-duotone-CalculatorMinimalistic';
    case LineduotoneNotebookSquare = 'solar-line-duotone-NotebookSquare';
    case LineduotoneBookMinimalistic = 'solar-line-duotone-BookMinimalistic';
    case LineduotoneCaseMinimalistic = 'solar-line-duotone-CaseMinimalistic';
    case LineduotoneNotebookBookmark = 'solar-line-duotone-NotebookBookmark';
    case LineduotonePassportMinimalistic = 'solar-line-duotone-PassportMinimalistic';
    case LineduotoneBookBookmark = 'solar-line-duotone-BookBookmark';
    case LineduotoneBookmarkSquareMinimalistic = 'solar-line-duotone-BookmarkSquareMinimalistic';
    case LineduotoneBookmark = 'solar-line-duotone-Bookmark';
    case LineduotonePlusMinus = 'solar-line-duotone-PlusMinus';
    case LineduotoneCalculator = 'solar-line-duotone-Calculator';
    case LineduotoneBookmarkSquare = 'solar-line-duotone-BookmarkSquare';
    case LineduotoneNotebookMinimalistic = 'solar-line-duotone-NotebookMinimalistic';
    case LineduotoneFireSquare = 'solar-line-duotone-FireSquare';
    case LineduotoneSuitcaseLines = 'solar-line-duotone-SuitcaseLines';
    case LineduotoneFire = 'solar-line-duotone-Fire';
    case LineduotoneBonfire = 'solar-line-duotone-Bonfire';
    case LineduotoneSuitcaseTag = 'solar-line-duotone-SuitcaseTag';
    case LineduotoneLeaf = 'solar-line-duotone-Leaf';
    case LineduotoneSuitcase = 'solar-line-duotone-Suitcase';
    case LineduotoneFlame = 'solar-line-duotone-Flame';
    case LineduotoneFireMinimalistic = 'solar-line-duotone-FireMinimalistic';
    case LineduotoneBellBing = 'solar-line-duotone-BellBing';
    case LineduotoneNotificationLinesRemove = 'solar-line-duotone-NotificationLinesRemove';
    case LineduotoneNotificationUnread = 'solar-line-duotone-NotificationUnread';
    case LineduotoneBell = 'solar-line-duotone-Bell';
    case LineduotoneNotificationRemove = 'solar-line-duotone-NotificationRemove';
    case LineduotoneNotificationUnreadLines = 'solar-line-duotone-NotificationUnreadLines';
    case LineduotoneBellOff = 'solar-line-duotone-BellOff';
    case LineduotoneLightning = 'solar-line-duotone-Lightning';
    case LineduotoneLightbulbMinimalistic = 'solar-line-duotone-LightbulbMinimalistic';
    case LineduotoneServerSquareCloud = 'solar-line-duotone-ServerSquareCloud';
    case LineduotoneLightbulbBolt = 'solar-line-duotone-LightbulbBolt';
    case LineduotoneAirbudsCharge = 'solar-line-duotone-AirbudsCharge';
    case LineduotoneServerPath = 'solar-line-duotone-ServerPath';
    case LineduotoneSimCardMinimalistic = 'solar-line-duotone-SimCardMinimalistic';
    case LineduotoneSmartphone = 'solar-line-duotone-Smartphone';
    case LineduotoneTurntable = 'solar-line-duotone-Turntable';
    case LineduotoneAirbudsCheck = 'solar-line-duotone-AirbudsCheck';
    case LineduotoneMouseMinimalistic = 'solar-line-duotone-MouseMinimalistic';
    case LineduotoneSmartphoneRotateAngle = 'solar-line-duotone-SmartphoneRotateAngle';
    case LineduotoneRadioMinimalistic = 'solar-line-duotone-RadioMinimalistic';
    case LineduotoneAirbuds = 'solar-line-duotone-Airbuds';
    case LineduotoneSmartphoneRotateOrientation = 'solar-line-duotone-SmartphoneRotateOrientation';
    case LineduotoneIPhone = 'solar-line-duotone-IPhone';
    case LineduotoneSimCard = 'solar-line-duotone-SimCard';
    case LineduotoneFlashDrive = 'solar-line-duotone-FlashDrive';
    case LineduotoneDevices = 'solar-line-duotone-Devices';
    case LineduotoneSimCards = 'solar-line-duotone-SimCards';
    case LineduotoneAirbudsCaseOpen = 'solar-line-duotone-AirbudsCaseOpen';
    case LineduotoneTurntableMusicNote = 'solar-line-duotone-TurntableMusicNote';
    case LineduotoneKeyboard = 'solar-line-duotone-Keyboard';
    case LineduotoneGamepadCharge = 'solar-line-duotone-GamepadCharge';
    case LineduotoneBoombox = 'solar-line-duotone-Boombox';
    case LineduotoneSmartSpeakerMinimalistic = 'solar-line-duotone-SmartSpeakerMinimalistic';
    case LineduotoneTelescope = 'solar-line-duotone-Telescope';
    case LineduotoneMonitorCamera = 'solar-line-duotone-MonitorCamera';
    case LineduotoneLaptopMinimalistic = 'solar-line-duotone-LaptopMinimalistic';
    case LineduotoneServer2 = 'solar-line-duotone-Server2';
    case LineduotoneSmartSpeaker = 'solar-line-duotone-SmartSpeaker';
    case LineduotoneProjector = 'solar-line-duotone-Projector';
    case LineduotoneServer = 'solar-line-duotone-Server';
    case LineduotoneTV = 'solar-line-duotone-TV';
    case LineduotoneCassette2 = 'solar-line-duotone-Cassette2';
    case LineduotoneRadio = 'solar-line-duotone-Radio';
    case LineduotoneSmartphoneVibration = 'solar-line-duotone-SmartphoneVibration';
    case LineduotoneAirbudsLeft = 'solar-line-duotone-AirbudsLeft';
    case LineduotoneHeadphonesRound = 'solar-line-duotone-HeadphonesRound';
    case LineduotoneGameboy = 'solar-line-duotone-Gameboy';
    case LineduotoneHeadphonesRoundSound = 'solar-line-duotone-HeadphonesRoundSound';
    case LineduotoneCPU = 'solar-line-duotone-CPU';
    case LineduotonePrinter2 = 'solar-line-duotone-Printer2';
    case LineduotoneHeadphonesSquare = 'solar-line-duotone-HeadphonesSquare';
    case LineduotoneServerSquareUpdate = 'solar-line-duotone-ServerSquareUpdate';
    case LineduotonePrinterMinimalistic = 'solar-line-duotone-PrinterMinimalistic';
    case LineduotoneBluetooth = 'solar-line-duotone-Bluetooth';
    case LineduotoneWirelessCharge = 'solar-line-duotone-WirelessCharge';
    case LineduotoneBluetoothCircle = 'solar-line-duotone-BluetoothCircle';
    case LineduotoneAirbudsCaseMinimalistic = 'solar-line-duotone-AirbudsCaseMinimalistic';
    case LineduotoneLightbulb = 'solar-line-duotone-Lightbulb';
    case LineduotoneAirbudsRemove = 'solar-line-duotone-AirbudsRemove';
    case LineduotoneSmartphoneRotate2 = 'solar-line-duotone-SmartphoneRotate2';
    case LineduotoneSsdSquare = 'solar-line-duotone-SsdSquare';
    case LineduotonePrinter = 'solar-line-duotone-Printer';
    case LineduotoneSmartphone2 = 'solar-line-duotone-Smartphone2';
    case LineduotoneServerMinimalistic = 'solar-line-duotone-ServerMinimalistic';
    case LineduotoneHeadphonesSquareSound = 'solar-line-duotone-HeadphonesSquareSound';
    case LineduotoneDiskette = 'solar-line-duotone-Diskette';
    case LineduotoneBluetoothWave = 'solar-line-duotone-BluetoothWave';
    case LineduotoneSmartSpeaker2 = 'solar-line-duotone-SmartSpeaker2';
    case LineduotoneLaptop3 = 'solar-line-duotone-Laptop3';
    case LineduotoneLaptop2 = 'solar-line-duotone-Laptop2';
    case LineduotoneMouseCircle = 'solar-line-duotone-MouseCircle';
    case LineduotoneTurntableMinimalistic = 'solar-line-duotone-TurntableMinimalistic';
    case LineduotoneSmartphoneUpdate = 'solar-line-duotone-SmartphoneUpdate';
    case LineduotoneGamepadMinimalistic = 'solar-line-duotone-GamepadMinimalistic';
    case LineduotoneSdCard = 'solar-line-duotone-SdCard';
    case LineduotonePlugCircle = 'solar-line-duotone-PlugCircle';
    case LineduotoneAirbudsCase = 'solar-line-duotone-AirbudsCase';
    case LineduotoneSsdRound = 'solar-line-duotone-SsdRound';
    case LineduotoneLaptop = 'solar-line-duotone-Laptop';
    case LineduotoneAirbudsRight = 'solar-line-duotone-AirbudsRight';
    case LineduotoneDisplay = 'solar-line-duotone-Display';
    case LineduotoneMonitorSmartphone = 'solar-line-duotone-MonitorSmartphone';
    case LineduotoneSocket = 'solar-line-duotone-Socket';
    case LineduotoneGamepadOld = 'solar-line-duotone-GamepadOld';
    case LineduotoneCpuBolt = 'solar-line-duotone-CpuBolt';
    case LineduotoneAirbudsCaseCharge = 'solar-line-duotone-AirbudsCaseCharge';
    case LineduotoneTablet = 'solar-line-duotone-Tablet';
    case LineduotoneWeigher = 'solar-line-duotone-Weigher';
    case LineduotoneServerSquare = 'solar-line-duotone-ServerSquare';
    case LineduotoneMouse = 'solar-line-duotone-Mouse';
    case LineduotoneGamepadNoCharge = 'solar-line-duotone-GamepadNoCharge';
    case LineduotoneBluetoothSquare = 'solar-line-duotone-BluetoothSquare';
    case LineduotoneCloudStorage = 'solar-line-duotone-CloudStorage';
    case LineduotoneGamepad = 'solar-line-duotone-Gamepad';
    case LineduotoneMonitor = 'solar-line-duotone-Monitor';
    case LineduotoneCassette = 'solar-line-duotone-Cassette';

    /**
     * Get the icon for a specific size.
     *
     * This method provides compatibility with Filament's ScalableIcon interface.
     * For Solar icons, we return the same icon regardless of size
     * since they are SVG and scale naturally.
     *
     * @param string $size The requested icon size (ignored for SVG icons)
     * @return string The icon identifier
     */
    public function getIconForSize(string $size): string
    {
        return $this->value;
    }

    public function getIconName(): string
    {
        return $this->value;
    }

    /**
     * Get all available Solar icon styles with descriptions.
     *
     * @return array<string, string> Array of style names and descriptions
     */
    public static function getAvailableStyles(): array
    {
        return [
            'bold' => 'Bold - Filled, strong visual weight',
            'bold-duotone' => 'Bold Duotone - Two-tone bold style',
            'broken' => 'Broken - Stylized broken line style',
            'line-duotone' => 'Line Duotone - Two-tone line style',
            'linear' => 'Linear - Clean, minimal lines',
            'outline' => 'Outline - Clean outlined style',
        ];
    }

    /**
     * Get icon identifier by name and style.
     *
     * @param string $name The icon name
     * @param string $style The icon style (default: 'linear')
     * @return string The full icon identifier
     */
    public static function getIcon(string $name, string $style = 'linear'): string
    {
        return "solar-{$style}-{$name}";
    }

    /**
     * Check if an icon exists in the enum.
     *
     * @param string $iconName The icon identifier to check
     * @return bool True if the icon exists in the enum, false otherwise
     */
    public static function exists(string $iconName): bool
    {
        if (empty(trim($iconName))) {
            return false;
        }

        return collect(self::cases())->contains(fn($case) => $case->value === $iconName);
    }

    /**
     * Get all enum cases as an array of values.
     *
     * @return array<string> Array of all icon identifiers
     */
    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * Get enum cases grouped by style.
     *
     * @return array<string, array<self>> Array of styles with their icons
     */
    public static function groupedByStyle(): array
    {
        $grouped = [];

        foreach (self::cases() as $case) {
            $parts = explode('-', $case->value, 3);
            if (count($parts) >= 2) {
                $style = $parts[1];
                if (count($parts) === 3 && $parts[2] === 'duotone') {
                    $style .= '-duotone';
                }
                $grouped[$style][] = $case;
            }
        }

        return $grouped;
    }

    /**
     * Get all icon names grouped by style
     */
    public static function getIconsByStyle(): array
    {
        $icons = [];
        foreach (self::cases() as $case) {
            $parts = explode('-', $case->value, 3);
            if (count($parts) >= 3) {
                $style = $parts[1];
                $name = $parts[2];
                $icons[$style][] = $name;
            }
        }
        return $icons;
    }

}
